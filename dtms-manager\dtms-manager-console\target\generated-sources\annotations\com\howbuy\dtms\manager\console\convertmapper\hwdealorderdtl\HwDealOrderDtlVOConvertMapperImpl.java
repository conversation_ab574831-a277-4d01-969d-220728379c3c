package com.howbuy.dtms.manager.console.convertmapper.hwdealorderdtl;

import com.howbuy.dtms.manager.console.vo.hwdealorderdtl.HwDealOrderDtlVO;
import com.howbuy.dtms.manager.dao.bo.CustDealOrderAppBO;
import com.howbuy.dtms.manager.dao.po.HwDealOrderDtlPO;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T16:12:59+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class HwDealOrderDtlVOConvertMapperImpl implements HwDealOrderDtlVOConvertMapper {

    @Override
    public HwDealOrderDtlVO convert(HwDealOrderDtlPO po) {
        if ( po == null ) {
            return null;
        }

        HwDealOrderDtlVO hwDealOrderDtlVO = new HwDealOrderDtlVO();

        hwDealOrderDtlVO.setAckAmt( po.getAckAmt() );
        hwDealOrderDtlVO.setAckDt( po.getAckDt() );
        hwDealOrderDtlVO.setAckNav( po.getAckNav() );
        hwDealOrderDtlVO.setAckNavDt( po.getAckNavDt() );
        hwDealOrderDtlVO.setAckStatus( po.getAckStatus() );
        hwDealOrderDtlVO.setAckVol( po.getAckVol() );
        hwDealOrderDtlVO.setAppAmt( po.getAppAmt() );
        hwDealOrderDtlVO.setAppStatus( po.getAppStatus() );
        hwDealOrderDtlVO.setAppVol( po.getAppVol() );
        hwDealOrderDtlVO.setCancelCause( po.getCancelCause() );
        hwDealOrderDtlVO.setCancelCpAcctNo( po.getCancelCpAcctNo() );
        hwDealOrderDtlVO.setCancelDate( po.getCancelDate() );
        hwDealOrderDtlVO.setCpAcctNo( po.getCpAcctNo() );
        hwDealOrderDtlVO.setCreateTimestamp( po.getCreateTimestamp() );
        hwDealOrderDtlVO.setCurrency( po.getCurrency() );
        hwDealOrderDtlVO.setDealDtlNo( po.getDealDtlNo() );
        hwDealOrderDtlVO.setDealNo( po.getDealNo() );
        hwDealOrderDtlVO.setDiscountAmt( po.getDiscountAmt() );
        hwDealOrderDtlVO.setDiscountRate( po.getDiscountRate() );
        hwDealOrderDtlVO.setDiscountType( po.getDiscountType() );
        hwDealOrderDtlVO.setEstimateFee( po.getEstimateFee() );
        hwDealOrderDtlVO.setExtControlNum( po.getExtControlNum() );
        hwDealOrderDtlVO.setExtControlType( po.getExtControlType() );
        hwDealOrderDtlVO.setExtOption( po.getExtOption() );
        hwDealOrderDtlVO.setFee( po.getFee() );
        hwDealOrderDtlVO.setFeeCalMode( po.getFeeCalMode() );
        hwDealOrderDtlVO.setFeeRate( po.getFeeRate() );
        hwDealOrderDtlVO.setFundAbbr( po.getFundAbbr() );
        hwDealOrderDtlVO.setFundCategory( po.getFundCategory() );
        hwDealOrderDtlVO.setFundCode( po.getFundCode() );
        hwDealOrderDtlVO.setFundDivMode( po.getFundDivMode() );
        hwDealOrderDtlVO.setFundManCode( po.getFundManCode() );
        hwDealOrderDtlVO.setFundName( po.getFundName() );
        hwDealOrderDtlVO.setFundRiskLevel( po.getFundRiskLevel() );
        hwDealOrderDtlVO.setFundTxAcctNo( po.getFundTxAcctNo() );
        hwDealOrderDtlVO.setHkCustNo( po.getHkCustNo() );
        hwDealOrderDtlVO.setId( po.getId() );
        hwDealOrderDtlVO.setIntoAckAmt( po.getIntoAckAmt() );
        hwDealOrderDtlVO.setIntoAckNav( po.getIntoAckNav() );
        hwDealOrderDtlVO.setIntoAckNavDt( po.getIntoAckNavDt() );
        hwDealOrderDtlVO.setIntoAckVol( po.getIntoAckVol() );
        hwDealOrderDtlVO.setIntoCurrency( po.getIntoCurrency() );
        hwDealOrderDtlVO.setIntoFundAbbr( po.getIntoFundAbbr() );
        hwDealOrderDtlVO.setIntoFundCode( po.getIntoFundCode() );
        hwDealOrderDtlVO.setIntoFundTxAcctNo( po.getIntoFundTxAcctNo() );
        hwDealOrderDtlVO.setIntoMainFundCode( po.getIntoMainFundCode() );
        hwDealOrderDtlVO.setMainFundCode( po.getMainFundCode() );
        hwDealOrderDtlVO.setMiddleBusiCode( po.getMiddleBusiCode() );
        hwDealOrderDtlVO.setNetAppAmt( po.getNetAppAmt() );
        hwDealOrderDtlVO.setOpenDt( po.getOpenDt() );
        hwDealOrderDtlVO.setPayEndDt( po.getPayEndDt() );
        hwDealOrderDtlVO.setPayEndTm( po.getPayEndTm() );
        hwDealOrderDtlVO.setPreSubmitTaDt( po.getPreSubmitTaDt() );
        hwDealOrderDtlVO.setPreSubmitTaTm( po.getPreSubmitTaTm() );
        hwDealOrderDtlVO.setPrebookDiscount( po.getPrebookDiscount() );
        hwDealOrderDtlVO.setRecStat( po.getRecStat() );
        hwDealOrderDtlVO.setRedeemDirectionList( po.getRedeemDirectionList() );
        hwDealOrderDtlVO.setRedeemType( po.getRedeemType() );
        hwDealOrderDtlVO.setRelationalDealDtlNo( po.getRelationalDealDtlNo() );
        hwDealOrderDtlVO.setSubAmt( po.getSubAmt() );
        hwDealOrderDtlVO.setSubmitStatus( po.getSubmitStatus() );
        hwDealOrderDtlVO.setTaAckNo( po.getTaAckNo() );
        hwDealOrderDtlVO.setTaTradeDt( po.getTaTradeDt() );
        hwDealOrderDtlVO.setTransferPrice( po.getTransferPrice() );
        hwDealOrderDtlVO.setUpdateTimestamp( po.getUpdateTimestamp() );
        hwDealOrderDtlVO.setVolDtlNo( po.getVolDtlNo() );

        return hwDealOrderDtlVO;
    }

    @Override
    public HwDealOrderDtlVO convert(CustDealOrderAppBO po) {
        if ( po == null ) {
            return null;
        }

        HwDealOrderDtlVO hwDealOrderDtlVO = new HwDealOrderDtlVO();

        hwDealOrderDtlVO.setAckAmt( po.getAckAmt() );
        hwDealOrderDtlVO.setAckDt( po.getAckDt() );
        hwDealOrderDtlVO.setAckNav( po.getAckNav() );
        hwDealOrderDtlVO.setAckNavDt( po.getAckNavDt() );
        hwDealOrderDtlVO.setAckStatus( po.getAckStatus() );
        hwDealOrderDtlVO.setAckVol( po.getAckVol() );
        hwDealOrderDtlVO.setAppAmt( po.getAppAmt() );
        hwDealOrderDtlVO.setAppDt( po.getAppDt() );
        hwDealOrderDtlVO.setAppStatus( po.getAppStatus() );
        hwDealOrderDtlVO.setAppTm( po.getAppTm() );
        hwDealOrderDtlVO.setAppVol( po.getAppVol() );
        hwDealOrderDtlVO.setCancelCause( po.getCancelCause() );
        hwDealOrderDtlVO.setCancelCpAcctNo( po.getCancelCpAcctNo() );
        hwDealOrderDtlVO.setCancelDate( po.getCancelDate() );
        hwDealOrderDtlVO.setCpAcctNo( po.getCpAcctNo() );
        hwDealOrderDtlVO.setCreateTimestamp( po.getCreateTimestamp() );
        hwDealOrderDtlVO.setCurrency( po.getCurrency() );
        hwDealOrderDtlVO.setCustChineseName( po.getCustChineseName() );
        hwDealOrderDtlVO.setDealDtlNo( po.getDealDtlNo() );
        hwDealOrderDtlVO.setDealNo( po.getDealNo() );
        hwDealOrderDtlVO.setDiscountAmt( po.getDiscountAmt() );
        hwDealOrderDtlVO.setDiscountRate( po.getDiscountRate() );
        hwDealOrderDtlVO.setDiscountType( po.getDiscountType() );
        hwDealOrderDtlVO.setEstimateFee( po.getEstimateFee() );
        hwDealOrderDtlVO.setExtControlNum( po.getExtControlNum() );
        hwDealOrderDtlVO.setExtControlType( po.getExtControlType() );
        hwDealOrderDtlVO.setExtOption( po.getExtOption() );
        hwDealOrderDtlVO.setFee( po.getFee() );
        hwDealOrderDtlVO.setFeeCalMode( po.getFeeCalMode() );
        hwDealOrderDtlVO.setFeeRate( po.getFeeRate() );
        hwDealOrderDtlVO.setFundAbbr( po.getFundAbbr() );
        hwDealOrderDtlVO.setFundCategory( po.getFundCategory() );
        hwDealOrderDtlVO.setFundCode( po.getFundCode() );
        hwDealOrderDtlVO.setFundDivMode( po.getFundDivMode() );
        hwDealOrderDtlVO.setFundManCode( po.getFundManCode() );
        hwDealOrderDtlVO.setFundName( po.getFundName() );
        hwDealOrderDtlVO.setFundRiskLevel( po.getFundRiskLevel() );
        hwDealOrderDtlVO.setFundTxAcctNo( po.getFundTxAcctNo() );
        hwDealOrderDtlVO.setHkCustNo( po.getHkCustNo() );
        hwDealOrderDtlVO.setId( po.getId() );
        hwDealOrderDtlVO.setIdNoMask( po.getIdNoMask() );
        hwDealOrderDtlVO.setIntoAckAmt( po.getIntoAckAmt() );
        hwDealOrderDtlVO.setIntoAckNav( po.getIntoAckNav() );
        hwDealOrderDtlVO.setIntoAckNavDt( po.getIntoAckNavDt() );
        hwDealOrderDtlVO.setIntoAckVol( po.getIntoAckVol() );
        hwDealOrderDtlVO.setIntoCurrency( po.getIntoCurrency() );
        hwDealOrderDtlVO.setIntoFundAbbr( po.getIntoFundAbbr() );
        hwDealOrderDtlVO.setIntoFundCode( po.getIntoFundCode() );
        hwDealOrderDtlVO.setIntoFundTxAcctNo( po.getIntoFundTxAcctNo() );
        hwDealOrderDtlVO.setIntoMainFundCode( po.getIntoMainFundCode() );
        hwDealOrderDtlVO.setIsScheduledTrade( po.getIsScheduledTrade() );
        hwDealOrderDtlVO.setMainFundCode( po.getMainFundCode() );
        hwDealOrderDtlVO.setMiddleBusiCode( po.getMiddleBusiCode() );
        hwDealOrderDtlVO.setNetAppAmt( po.getNetAppAmt() );
        hwDealOrderDtlVO.setOpenDt( po.getOpenDt() );
        hwDealOrderDtlVO.setPayEndDt( po.getPayEndDt() );
        hwDealOrderDtlVO.setPayEndTm( po.getPayEndTm() );
        hwDealOrderDtlVO.setPayStatus( po.getPayStatus() );
        hwDealOrderDtlVO.setPaymentTypeList( po.getPaymentTypeList() );
        hwDealOrderDtlVO.setPreSubmitTaDt( po.getPreSubmitTaDt() );
        hwDealOrderDtlVO.setPreSubmitTaTm( po.getPreSubmitTaTm() );
        hwDealOrderDtlVO.setPrebookDiscount( po.getPrebookDiscount() );
        hwDealOrderDtlVO.setRecStat( po.getRecStat() );
        hwDealOrderDtlVO.setRedeemDirectionList( po.getRedeemDirectionList() );
        hwDealOrderDtlVO.setRedeemType( po.getRedeemType() );
        hwDealOrderDtlVO.setRelationalDealDtlNo( po.getRelationalDealDtlNo() );
        hwDealOrderDtlVO.setSubAmt( po.getSubAmt() );
        hwDealOrderDtlVO.setSubmitStatus( po.getSubmitStatus() );
        hwDealOrderDtlVO.setTaAckNo( po.getTaAckNo() );
        hwDealOrderDtlVO.setTaTradeDt( po.getTaTradeDt() );
        hwDealOrderDtlVO.setTransferPrice( po.getTransferPrice() );
        hwDealOrderDtlVO.setUpdateTimestamp( po.getUpdateTimestamp() );
        hwDealOrderDtlVO.setVolDtlNo( po.getVolDtlNo() );

        return hwDealOrderDtlVO;
    }
}
