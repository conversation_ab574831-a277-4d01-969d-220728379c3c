package com.howbuy.dtms.manager.outservice.hkfin.dto;

import com.howbuy.hkfin.facade.query.balance.BalanceChangeInfo;
import com.howbuy.hkfin.facade.query.balance.BalanceInfo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:23:42+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class CashAccountConvertMapperImpl implements CashAccountConvertMapper {

    @Override
    public CashAccountDetailDTO toDTO(BalanceChangeInfo changeInfo) {
        if ( changeInfo == null ) {
            return null;
        }

        CashAccountDetailDTO cashAccountDetailDTO = new CashAccountDetailDTO();

        cashAccountDetailDTO.setChangeType( changeInfo.getChangeType() );
        cashAccountDetailDTO.setCurCode( changeInfo.getCurCode() );
        cashAccountDetailDTO.setCurEnCode( changeInfo.getCurEnCode() );
        cashAccountDetailDTO.setDataDt( changeInfo.getDataDt() );
        cashAccountDetailDTO.setFundTxAcctNo( changeInfo.getFundTxAcctNo() );
        cashAccountDetailDTO.setHkCustNo( changeInfo.getHkCustNo() );
        cashAccountDetailDTO.setOccurBalance( changeInfo.getOccurBalance() );
        cashAccountDetailDTO.setRemark( changeInfo.getRemark() );

        return cashAccountDetailDTO;
    }

    @Override
    public List<CashAccountDetailDTO> toDTOList(List<BalanceChangeInfo> changeInfoList) {
        if ( changeInfoList == null ) {
            return null;
        }

        List<CashAccountDetailDTO> list = new ArrayList<CashAccountDetailDTO>( changeInfoList.size() );
        for ( BalanceChangeInfo balanceChangeInfo : changeInfoList ) {
            list.add( toDTO( balanceChangeInfo ) );
        }

        return list;
    }

    @Override
    public CashAccountBalanceDTO toBalanceDTO(BalanceInfo balanceInfo) {
        if ( balanceInfo == null ) {
            return null;
        }

        CashAccountBalanceDTO cashAccountBalanceDTO = new CashAccountBalanceDTO();

        cashAccountBalanceDTO.setCurCode( balanceInfo.getCurCode() );
        cashAccountBalanceDTO.setCurEnCode( balanceInfo.getCurEnCode() );
        cashAccountBalanceDTO.setDataDt( balanceInfo.getDataDt() );
        cashAccountBalanceDTO.setEndBalance( balanceInfo.getEndBalance() );
        cashAccountBalanceDTO.setEndFreezeBalance( balanceInfo.getEndFreezeBalance() );
        cashAccountBalanceDTO.setFundTxAcctNo( balanceInfo.getFundTxAcctNo() );
        cashAccountBalanceDTO.setHandledEndBalance( balanceInfo.getHandledEndBalance() );
        cashAccountBalanceDTO.setHandledEndFreezeBalance( balanceInfo.getHandledEndFreezeBalance() );
        cashAccountBalanceDTO.setHkCustNo( balanceInfo.getHkCustNo() );
        cashAccountBalanceDTO.setStartBalance( balanceInfo.getStartBalance() );

        return cashAccountBalanceDTO;
    }

    @Override
    public List<CashAccountBalanceDTO> toBalanceDTOList(List<BalanceInfo> balanceList) {
        if ( balanceList == null ) {
            return null;
        }

        List<CashAccountBalanceDTO> list = new ArrayList<CashAccountBalanceDTO>( balanceList.size() );
        for ( BalanceInfo balanceInfo : balanceList ) {
            list.add( toBalanceDTO( balanceInfo ) );
        }

        return list;
    }
}
