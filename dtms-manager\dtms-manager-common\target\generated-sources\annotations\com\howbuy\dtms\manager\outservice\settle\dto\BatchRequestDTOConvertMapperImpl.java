package com.howbuy.dtms.manager.outservice.settle.dto;

import com.howbuy.dtms.settle.client.facade.batch.BatchProcessRequest;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:23:43+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class BatchRequestDTOConvertMapperImpl implements BatchRequestDTOConvertMapper {

    @Override
    public BatchProcessRequest convert(BatchProcessRequestDTO dto) {
        if ( dto == null ) {
            return null;
        }

        BatchProcessRequest batchProcessRequest = new BatchProcessRequest();

        batchProcessRequest.setTraceId( dto.getTraceId() );
        batchProcessRequest.setAckSerialNo( dto.getAckSerialNo() );
        batchProcessRequest.setAuto( dto.isAuto() );
        batchProcessRequest.setContextId( dto.getContextId() );
        batchProcessRequest.setFundCode( dto.getFundCode() );
        batchProcessRequest.setNodeAlias( dto.getNodeAlias() );
        batchProcessRequest.setNodeId( dto.getNodeId() );
        batchProcessRequest.setRepeatable( dto.isRepeatable() );
        batchProcessRequest.setRequestId( dto.getRequestId() );
        batchProcessRequest.setTaskId( dto.getTaskId() );
        batchProcessRequest.setTaskName( dto.getTaskName() );
        batchProcessRequest.setTaskType( dto.getTaskType() );
        batchProcessRequest.setTradeDt( dto.getTradeDt() );

        return batchProcessRequest;
    }
}
