package com.howbuy.dtms.manager.enums.auditorder.handle;

public enum CounterOrderHandleEnum {
    SUBS_COUNTER_ORDER_AUDIT_PASS_HANDLE("SUBS_COUNTER_ORDER_AUDIT_PASS_HANDLE","认申购审核通过校验器"),

    SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE("SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE","认申购审核不通过校验器"),

    COUNTER_ORDER_AUDIT_VOIDED_HANDLE("COUNTER_ORDER_AUDIT_VOIDED_HANDLE","作废校验器"),

    SUBS_COUNTER_ORDER_SUBMIT_HANDLE("SUBS_COUNTER_ORDER_SUBMIT_HANDLE","认购柜台下单校验处理器"),

    REDEEM_COUNTER_ORDER_SUBMIT_HANDLE("REDEEM_COUNTER_ORDER_SUBMIT_HANDLE","赎回柜台下单校验处理器"),

    REDEEM_COUNTER_ORDER_AUDIT_PASS_HANDLE("REDEEM_COUNTER_ORDER_AUDIT_PASS_HANDLE","赎回审核通过校验器"),


    REVOKE_COUNTER_ORDER_AUDIT_PASS_HANDLE("REVOKE_COUNTER_ORDER_AUDIT_PASS_HANDLE","交易撤单审核通过校验器"),

    EXTENSION_COUNTER_ORDER_AUDIT_PASS_HANDLE("EXTENSION_COUNTER_ORDER_AUDIT_PASS_HANDLE","展期修改审核通过校验器"),

    FUND_TRANSFER_COUNTER_ORDER_AUDIT_PASS_HANDLE("FUND_TRANSFER_COUNTER_ORDER_AUDIT_PASS_HANDLE","基金修改审核通过校验器"),


    /**
     * 等待回访的校验器
     */
    WAIT_REVISIT_TO_APPROVE_HANDLE("WAIT_REVISIT_TO_APPROVE_HANDLE","等待回访审核通过的校验器"),

    /**
     * 回访的校验器
     */
    WAIT_REVISIT_NOT_PASS_HANDLE("WAIT_REVISIT_NOT_PASS_HANDLE","等待回访审核不通过的校验器"),


    CRM_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE("CRM_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE", "CRM订单初审审核不通过校验器"),


    REVOKE_COUNTER_ORDER_SUBMIT_HANDLE("REVOKE_COUNTER_ORDER_SUBMIT_HANDLE","柜台撤单处理器"),

    EXTENSION_COUNTER_ORDER_SUBMIT_HANDLE("EXTENSION_COUNTER_ORDER_SUBMIT_HANDLE","柜台展期修改处理器"),

    /**
     * 基金转换提交处理器
     */
    FUND_TRANSFER_COUNTER_ORDER_SUBMIT_HANDLE("FUND_TRANSFER_COUNTER_ORDER_SUBMIT_HANDLE", "基金转换提交处理器"),

    /**
     * 强减提交处理器
     */
    FORCE_SUBTRACT_COUNTER_ORDER_SUBMIT_HANDLE("FORCE_SUBTRACT_COUNTER_ORDER_SUBMIT_HANDLE","强减柜台订单提交校验器"),

    /**
     * 强增提交处理器
     */
    FORCE_ADD_COUNTER_ORDER_SUBMIT_HANDLE("FORCE_ADD_COUNTER_ORDER_SUBMIT_HANDLE","强增柜台订单提交校验器"),

    /**
     * 强减审核通过处理器
     */
    FORCE_SUBTRACT_COUNTER_ORDER_AUDIT_PASS_HANDLE("FORCE_SUBTRACT_COUNTER_ORDER_AUDIT_PASS_HANDLE","强减柜台订单审核通过校验器"),

    /**
     * 强增审核通过处理器
     */
    FORCE_ADD_COUNTER_ORDER_AUDIT_PASS_HANDLE("FORCE_ADD_COUNTER_ORDER_AUDIT_PASS_HANDLE","强增柜台订单审核通过校验器"),

    /**
     * 批量实缴柜台订单提交校验器
     */
    BATCH_PAID_COUNTER_ORDER_SUBMIT_HANDLE("BATCH_PAID_COUNTER_ORDER_SUBMIT_HANDLE","批量实缴柜台订单提交校验器"),

    /**
     * 批量实缴柜台订单审核通过校验器
     */
    BATCH_PAID_COUNTER_ORDER_AUDIT_PASS_HANDLE("BATCH_PAID_COUNTER_ORDER_AUDIT_PASS_HANDLE","批量实缴柜台订单审核通过校验器"),

    /**
     * 全委专户批量认申购提交校验器
     */
    FULL_BATCH_SUBSCRIBE_COUNTER_ORDER_SUBMIT_HANDLE("FULL_BATCH_SUBSCRIBE_COUNTER_ORDER_SUBMIT_HANDLE","全委专户批量认申购柜台订单提交校验器"),

    /**
     * 全委专户批量认申购审核通过处理器
     */
    FULL_BATCH_SUBSCRIBE_COUNTER_ORDER_AUDIT_PASS_HANDLE("FULL_BATCH_SUBSCRIBE_COUNTER_ORDER_AUDIT_PASS_HANDLE","全委专户批量认申购审核通过处理器"),

    /**
     * 全委专户批量赎回提交校验器
     */
    FULL_BATCH_REDEEM_COUNTER_ORDER_SUBMIT_HANDLE("FULL_BATCH_REDEEM_COUNTER_ORDER_SUBMIT_HANDLE","全委专户批量赎回柜台订单提交校验器"),

    /**
     * 全委专户批量赎回审核通过处理器
     */
    FULL_BATCH_REDEEM_COUNTER_ORDER_AUDIT_PASS_HANDLE("FULL_BATCH_REDEEM_COUNTER_ORDER_AUDIT_PASS_HANDLE","全委专户批量赎回审核通过处理器"),

    /**
     * 基金交易账号开通处理器
     */
    FUND_TX_ACCT_NO_OPEN_COUNTER_ORDER_SUBMIT_HANDLE("FUND_TX_ACCT_NO_OPEN_COUNTER_ORDER_SUBMIT_HANDLE","基金交易账号开通处理器"),

    /**
     * 基金交易账号开通审核通过处理器
     */
    FUND_TX_ACCT_NO_OPEN_COUNTER_ORDER_AUDIT_PASS_HANDLE("FUND_TX_ACCT_NO_OPEN_COUNTER_ORDER_AUDIT_PASS_HANDLE","基金交易账号开通审核通过处理器");


    private final String code;


    private final String value;

    CounterOrderHandleEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static String getName(String a ){
        return CounterOrderHandleEnum.SUBS_COUNTER_ORDER_SUBMIT_HANDLE.name();
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }


}
