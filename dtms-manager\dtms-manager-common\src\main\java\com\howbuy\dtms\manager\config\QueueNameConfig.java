package com.howbuy.dtms.manager.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/15 20:29
 * @since JDK 1.8
 */
@Getter
@Component
public class QueueNameConfig {

    /**
     * 订单明细上报状态更新队列
     */
    @Value("${queue.orderDealDtlSubmitStatusUpdate}")
    private String orderDealDtlSubmitStatusUpdate;
    /**
     * 检查是否存在未上报的客户订单明细
     */
    @Value("${queue.checkIsExistsNotSubmitDealDtl}")
    private String checkIsExistsNotSubmitDealDtl;
    /**
     * 储蓄罐基金自动赎回队列
     */
    @Value("${queue.piggyFundAutoSell}")
    private String piggyFundAutoSell;

    /**
     * 订单上报topic
     */
    @Value("${queue.dtms_order_trade_record_reported}")
    private String dtmsOrderTradeRecordReported;

    /**
     * 批处理
     */
    @Value("${queue.batchProcess}")
    private String batchProcess;

    /**
     * 调用任务处理
     */
    @Value("${queue.ecTask}")
    private String ecTask;

    /**
     * 批处理跑批日志消息
     */
    @Value("${queue.batchExecuteLog}")
    private String batchExecuteLog;

    /**
     * 邮件发送
     */
    @Value("${queue.email}")
    private String email;
}
