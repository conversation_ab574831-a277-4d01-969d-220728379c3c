<#assign currencyMap = {"CNY":"人民币","USD":"美元","HKD":"港元","EUR":"欧元","JPY":"日元","GBP":"英镑","FRF":"法郎","DEM":"马克"}>
<#assign payStatusMap = {"0":"无需付款","1":"未付款","2":"付款中","3":"部分成功","4":"付款成功","5":"付款失败","6":"退款"}>

<#function dicMapFn dicMap dicValue="" defaultValue="">
   <#if dicMap[dicValue]?has_content>
      <#return dicMap[dicValue]>
   </#if>
   <#return defaultValue>
</#function>

<#--数字格式化(金额格式化)
--整数部分每三位用 , 分割，并且保证小数点后保留两位，不足用 0 代替
*balance ：参数金额
-->
<#function formatBalance balance>
   <#if (balance??)>
      <#return balance?string('#,##0.00')>
   <#else>
      <#return "0.00">
   </#if>
</#function>