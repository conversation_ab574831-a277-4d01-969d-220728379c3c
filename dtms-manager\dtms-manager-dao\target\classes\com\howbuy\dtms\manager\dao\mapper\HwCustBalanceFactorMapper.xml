<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.HwCustBalanceFactorMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwCustBalanceFactorPO">
    <!--@mbg.generated-->
    <!--@Table hw_cust_balance_factor-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="hk_cust_no" jdbcType="VARCHAR" property="hkCustNo" />
    <result column="fund_tx_acct_no" jdbcType="VARCHAR" property="fundTxAcctNo" />
    <result column="fund_code" jdbcType="VARCHAR" property="fundCode" />
    <result column="nav_dt" jdbcType="VARCHAR" property="navDt" />
    <result column="balance_factor" jdbcType="DECIMAL" property="balanceFactor" />
    <result column="rec_stat" jdbcType="CHAR" property="recStat" />
    <result column="create_timestamp" jdbcType="TIMESTAMP" property="createTimestamp" />
    <result column="update_timestamp" jdbcType="TIMESTAMP" property="updateTimestamp" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, hk_cust_no, fund_tx_acct_no, fund_code, nav_dt, balance_factor, rec_stat, create_timestamp, 
    update_timestamp, version
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from hw_cust_balance_factor
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from hw_cust_balance_factor
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.HwCustBalanceFactorPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hw_cust_balance_factor (hk_cust_no, fund_tx_acct_no, fund_code, 
      nav_dt, balance_factor, rec_stat, 
      create_timestamp, update_timestamp, version
      )
    values (#{hkCustNo,jdbcType=VARCHAR}, #{fundTxAcctNo,jdbcType=VARCHAR}, #{fundCode,jdbcType=VARCHAR}, 
      #{navDt,jdbcType=VARCHAR}, #{balanceFactor,jdbcType=DECIMAL}, #{recStat,jdbcType=CHAR}, 
      #{createTimestamp,jdbcType=TIMESTAMP}, #{updateTimestamp,jdbcType=TIMESTAMP}, #{version,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.HwCustBalanceFactorPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hw_cust_balance_factor
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hkCustNo != null">
        hk_cust_no,
      </if>
      <if test="fundTxAcctNo != null">
        fund_tx_acct_no,
      </if>
      <if test="fundCode != null">
        fund_code,
      </if>
      <if test="navDt != null">
        nav_dt,
      </if>
      <if test="balanceFactor != null">
        balance_factor,
      </if>
      <if test="recStat != null">
        rec_stat,
      </if>
      <if test="createTimestamp != null">
        create_timestamp,
      </if>
      <if test="updateTimestamp != null">
        update_timestamp,
      </if>
      <if test="version != null">
        version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hkCustNo != null">
        #{hkCustNo,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAcctNo != null">
        #{fundTxAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="fundCode != null">
        #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="navDt != null">
        #{navDt,jdbcType=VARCHAR},
      </if>
      <if test="balanceFactor != null">
        #{balanceFactor,jdbcType=DECIMAL},
      </if>
      <if test="recStat != null">
        #{recStat,jdbcType=CHAR},
      </if>
      <if test="createTimestamp != null">
        #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTimestamp != null">
        #{updateTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.dtms.manager.dao.po.HwCustBalanceFactorPO">
    <!--@mbg.generated-->
    update hw_cust_balance_factor
    <set>
      <if test="hkCustNo != null">
        hk_cust_no = #{hkCustNo,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAcctNo != null">
        fund_tx_acct_no = #{fundTxAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="fundCode != null">
        fund_code = #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="navDt != null">
        nav_dt = #{navDt,jdbcType=VARCHAR},
      </if>
      <if test="balanceFactor != null">
        balance_factor = #{balanceFactor,jdbcType=DECIMAL},
      </if>
      <if test="recStat != null">
        rec_stat = #{recStat,jdbcType=CHAR},
      </if>
      <if test="createTimestamp != null">
        create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTimestamp != null">
        update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.dtms.manager.dao.po.HwCustBalanceFactorPO">
    <!--@mbg.generated-->
    update hw_cust_balance_factor
    set hk_cust_no = #{hkCustNo,jdbcType=VARCHAR},
      fund_tx_acct_no = #{fundTxAcctNo,jdbcType=VARCHAR},
      fund_code = #{fundCode,jdbcType=VARCHAR},
      nav_dt = #{navDt,jdbcType=VARCHAR},
      balance_factor = #{balanceFactor,jdbcType=DECIMAL},
      rec_stat = #{recStat,jdbcType=CHAR},
      create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
      update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP},
      version = #{version,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>