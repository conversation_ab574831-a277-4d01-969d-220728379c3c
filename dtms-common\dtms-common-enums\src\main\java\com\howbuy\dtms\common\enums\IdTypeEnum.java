/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/8/1 19:50
 * @since JDK 1.8
 */
public enum IdTypeEnum {

    ID("0", "身份证"),
    PASSPORT("1", "中国护照"),
    OFFICERS_CERTIFICATE("2", "军官证"),
    SOLDIER("3", "士兵证"),
    HOME_RETURN_PERMIT("4", "港澳居民来往内地通行证"),
    ACCOUNT_BOOK("5", "户口本"),
    FOREIGN_PASSPORT("6", "外国护照"),
    OTHER("7", "其他"),
    CIVIL_CERTIFICATE("8", "文职证"),
    POLICE_CARD("9", "警官证"),
    MTP("A", "台胞证"),
    FOREIGNERS_ID("B", "外国人永久居留身份证"),
    HMT_ID("C", "港澳台居民居住证"),
    HK_ID_CARD("D", "香港身份证"),
    MO_ID_CARD("E", "澳门身份证"),
    TW_ID_CARD("F", "台湾身份证"),

    ;



    private String code;
    private String description;

    private IdTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static String getDescription(String code) {
        IdTypeEnum[] arr$ = values();
        int len$ = arr$.length;

        for(int i$ = 0; i$ < len$; ++i$) {
            IdTypeEnum b = arr$[i$];
            if (b.getCode().equals(code)) {
                return b.description;
            }
        }

        return null;
    }


    public static IdTypeEnum getEnum(String code) {
        if (code != null && !"".equals(code)) {
            IdTypeEnum[] arr$ = values();
            int len$ = arr$.length;

            for(int i$ = 0; i$ < len$; ++i$) {
                IdTypeEnum b = arr$[i$];
                if (b.getCode().equals(code)) {
                    return b;
                }
            }
        }

        return null;
    }

    public static String toString(String code) {
        return code != null && !code.isEmpty() ? String.format("%s-%s", code, getDescription(code)) : "";
    }


    public String getCode() {
        return this.code;
    }

    public String getDescription() {
        return this.description;
    }

}
