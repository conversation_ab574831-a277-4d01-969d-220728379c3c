package com.howbuy.dtms.manager.counter.vo.hwpiggytradeappimport;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 计算储蓄罐交易申请手续费响应对象
 *
 * <AUTHOR>
 * @date 2025-07-21 19:24:03
 * @since JDK 1.8
 */
@Getter
@Setter
public class ComputePiggyTradeAppFeeVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 手续费
     */
    private String fee;
}
