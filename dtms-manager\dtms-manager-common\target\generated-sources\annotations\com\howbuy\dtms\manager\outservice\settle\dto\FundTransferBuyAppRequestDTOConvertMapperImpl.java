package com.howbuy.dtms.manager.outservice.settle.dto;

import com.howbuy.dtms.settle.client.facade.trade.transfer.FundTransferBuyAppRequest;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:23:44+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class FundTransferBuyAppRequestDTOConvertMapperImpl implements FundTransferBuyAppRequestDTOConvertMapper {

    @Override
    public FundTransferBuyAppRequest convert(FundTransferBuyAppRequestDTO dto) {
        if ( dto == null ) {
            return null;
        }

        FundTransferBuyAppRequest fundTransferBuyAppRequest = new FundTransferBuyAppRequest();

        fundTransferBuyAppRequest.setTraceId( dto.getTraceId() );
        fundTransferBuyAppRequest.setAppDt( dto.getAppDt() );
        fundTransferBuyAppRequest.setAppStatus( dto.getAppStatus() );
        fundTransferBuyAppRequest.setAppTm( dto.getAppTm() );
        fundTransferBuyAppRequest.setCpAcctNo( dto.getCpAcctNo() );
        fundTransferBuyAppRequest.setCurrency( dto.getCurrency() );
        fundTransferBuyAppRequest.setDealDtlNo( dto.getDealDtlNo() );
        fundTransferBuyAppRequest.setDiscountRate( dto.getDiscountRate() );
        fundTransferBuyAppRequest.setEsitmateFee( dto.getEsitmateFee() );
        fundTransferBuyAppRequest.setFeeRate( dto.getFeeRate() );
        fundTransferBuyAppRequest.setFundCode( dto.getFundCode() );
        fundTransferBuyAppRequest.setFundTxAcctNo( dto.getFundTxAcctNo() );
        fundTransferBuyAppRequest.setHkCustNo( dto.getHkCustNo() );
        fundTransferBuyAppRequest.setMiddleOrderNo( dto.getMiddleOrderNo() );
        fundTransferBuyAppRequest.setNetAppAmt( dto.getNetAppAmt() );
        fundTransferBuyAppRequest.setOpenDt( dto.getOpenDt() );
        fundTransferBuyAppRequest.setPreSubmitTaDt( dto.getPreSubmitTaDt() );
        fundTransferBuyAppRequest.setPreSubmitTaTm( dto.getPreSubmitTaTm() );
        fundTransferBuyAppRequest.setRelationalDealDtlNo( dto.getRelationalDealDtlNo() );
        fundTransferBuyAppRequest.setSerialNumber( dto.getSerialNumber() );
        fundTransferBuyAppRequest.setShareRegDt( dto.getShareRegDt() );
        fundTransferBuyAppRequest.setSubmitTaDt( dto.getSubmitTaDt() );

        return fundTransferBuyAppRequest;
    }
}
