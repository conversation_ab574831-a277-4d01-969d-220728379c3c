/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.manager.counter.vo.payment;

import com.howbuy.dtms.manager.common.PageVo;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/7/23 15:02
 * @since JDK 1.8
 */
public class PaymentCheckResultListVO extends PageVo<PaymentCheckResultVO> {

}
