<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.BtStatementInfoRecMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.BtStatementInfoRecPO">
        <!--@Table bt_statement_info_rec-->
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="hkCustNo" column="hk_cust_no" jdbcType="VARCHAR"/>
        <result property="fundTxAcctNo" column="fund_tx_acct_no" jdbcType="VARCHAR"/>
        <result property="statementType" column="statement_type" jdbcType="VARCHAR"/>
        <result property="fundCode" column="fund_code" jdbcType="VARCHAR"/>
        <result property="statementDt" column="statement_dt" jdbcType="VARCHAR"/>
        <result property="filePath" column="file_path" jdbcType="VARCHAR"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="sendStat" column="send_stat" jdbcType="VARCHAR"/>
        <result property="sendTime" column="send_time" jdbcType="TIMESTAMP"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
        <result property="fileGenerateStat" column="file_generate_stat" jdbcType="VARCHAR"/>
        <result property="memo" column="memo" jdbcType="VARCHAR"/>
        <result property="sendCount" column="send_count" jdbcType="INTEGER"/>
        <result property="fileGenerateTime" column="file_generate_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
        hk_cust_no, 
        fund_tx_acct_no, 
        statement_type, 
        fund_code, 
        statement_dt, 
        file_path, 
        file_name, 
        is_deleted, 
        create_time, 
        update_time, 
        send_stat, 
        send_time, 
        version, 
        file_generate_stat, 
        memo, 
        send_count, 
        file_generate_time
    </sql>

    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bt_statement_info_rec
        where id = #{id}
    </select>

    <!-- 查询符合条件的数据 -->
    <select id="selectBySelective" parameterType="com.howbuy.dtms.manager.dao.po.BtStatementInfoRecPO"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bt_statement_info_rec
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and hk_cust_no = #{hkCustNo}
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                and fund_tx_acct_no = #{fundTxAcctNo}
            </if>
            <if test="statementType != null and statementType != ''">
                and statement_type = #{statementType}
            </if>
            <if test="fundCode != null and fundCode != ''">
                and fund_code = #{fundCode}
            </if>
            <if test="statementDt != null and statementDt != ''">
                and statement_dt = #{statementDt}
            </if>
            <if test="filePath != null and filePath != ''">
                and file_path = #{filePath}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name = #{fileName}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="sendStat != null and sendStat != ''">
                and send_stat = #{sendStat}
            </if>
            <if test="sendTime != null">
                and send_time = #{sendTime}
            </if>
            <if test="version != null">
                and version = #{version}
            </if>
            <if test="fileGenerateStat != null and fileGenerateStat != ''">
                and file_generate_stat = #{fileGenerateStat}
            </if>
            <if test="memo != null and memo != ''">
                and memo = #{memo}
            </if>
            <if test="sendCount != null">
                and send_count = #{sendCount}
            </if>
            <if test="fileGenerateTime != null">
                and file_generate_time = #{fileGenerateTime}
            </if>
        </where>
        order by id
    </select>


    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(*)
        from bt_statement_info_rec
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and hk_cust_no = #{hkCustNo}
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                and fund_tx_acct_no = #{fundTxAcctNo}
            </if>
            <if test="statementType != null and statementType != ''">
                and statement_type = #{statementType}
            </if>
            <if test="fundCode != null and fundCode != ''">
                and fund_code = #{fundCode}
            </if>
            <if test="statementDt != null and statementDt != ''">
                and statement_dt = #{statementDt}
            </if>
            <if test="filePath != null and filePath != ''">
                and file_path = #{filePath}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name = #{fileName}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="sendStat != null and sendStat != ''">
                and send_stat = #{sendStat}
            </if>
            <if test="sendTime != null">
                and send_time = #{sendTime}
            </if>
            <if test="version != null">
                and version = #{version}
            </if>
            <if test="fileGenerateStat != null and fileGenerateStat != ''">
                and file_generate_stat = #{fileGenerateStat}
            </if>
            <if test="memo != null and memo != ''">
                and memo = #{memo}
            </if>
            <if test="sendCount != null">
                and send_count = #{sendCount}
            </if>
            <if test="fileGenerateTime != null">
                and file_generate_time = #{fileGenerateTime}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into bt_statement_info_rec(id, hk_cust_no, fund_tx_acct_no, statement_type, fund_code, statement_dt,
                                          file_path, file_name, is_deleted, create_time, update_time, send_stat,
                                          send_time, version, file_generate_stat, memo, send_count, file_generate_time)
        values (#{id},#{hkCustNo}, #{fundTxAcctNo}, #{statementType}, #{fundCode}, #{statementDt}, #{filePath}, #{fileName},
                #{isDeleted}, #{createTime}, #{updateTime}, #{sendStat}, #{sendTime}, #{version}, #{fileGenerateStat},
                #{memo}, #{sendCount}, #{fileGenerateTime})
    </insert>


    <insert id="insertSelective">
        insert into bt_statement_info_rec
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                hk_cust_no,
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                fund_tx_acct_no,
            </if>
            <if test="statementType != null and statementType != ''">
                statement_type,
            </if>
            <if test="fundCode != null and fundCode != ''">
                fund_code,
            </if>
            <if test="statementDt != null and statementDt != ''">
                statement_dt,
            </if>
            <if test="filePath != null and filePath != ''">
                file_path,
            </if>
            <if test="fileName != null and fileName != ''">
                file_name,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="sendStat != null and sendStat != ''">
                send_stat,
            </if>
            <if test="sendTime != null">
                send_time,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="fileGenerateStat != null and fileGenerateStat != ''">
                file_generate_stat,
            </if>
            <if test="memo != null and memo != ''">
                memo,
            </if>
            <if test="sendCount != null">
                send_count,
            </if>
            <if test="fileGenerateTime != null">
                file_generate_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                #{hkCustNo},
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                #{fundTxAcctNo},
            </if>
            <if test="statementType != null and statementType != ''">
                #{statementType},
            </if>
            <if test="fundCode != null and fundCode != ''">
                #{fundCode},
            </if>
            <if test="statementDt != null and statementDt != ''">
                #{statementDt},
            </if>
            <if test="filePath != null and filePath != ''">
                #{filePath},
            </if>
            <if test="fileName != null and fileName != ''">
                #{fileName},
            </if>
            <if test="isDeleted != null">
                #{isDeleted},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="sendStat != null and sendStat != ''">
                #{sendStat},
            </if>
            <if test="sendTime != null">
                #{sendTime},
            </if>
            <if test="version != null">
                #{version},
            </if>
            <if test="fileGenerateStat != null and fileGenerateStat != ''">
                #{fileGenerateStat},
            </if>
            <if test="memo != null and memo != ''">
                #{memo},
            </if>
            <if test="sendCount != null">
                #{sendCount},
            </if>
            <if test="fileGenerateTime != null">
                #{fileGenerateTime},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch">
        insert into bt_statement_info_rec(id, hk_cust_no, fund_tx_acct_no, statement_type, fund_code, statement_dt,
        file_path, file_name, is_deleted, create_time, update_time, send_stat, send_time, version, file_generate_stat,
        memo, send_count, file_generate_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id}, #{entity.hkCustNo}, #{entity.fundTxAcctNo}, #{entity.statementType}, #{entity.fundCode},
            #{entity.statementDt}, #{entity.filePath}, #{entity.fileName}, #{entity.isDeleted}, #{entity.createTime},
            #{entity.updateTime}, #{entity.sendStat}, #{entity.sendTime}, #{entity.version}, #{entity.fileGenerateStat},
            #{entity.memo}, #{entity.sendCount}, #{entity.fileGenerateTime})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update bt_statement_info_rec
        <set>
            <if test="hkCustNo != null and hkCustNo != ''">
                hk_cust_no = #{hkCustNo},
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                fund_tx_acct_no = #{fundTxAcctNo},
            </if>
            <if test="statementType != null and statementType != ''">
                statement_type = #{statementType},
            </if>
            <if test="fundCode != null and fundCode != ''">
                fund_code = #{fundCode},
            </if>
            <if test="statementDt != null and statementDt != ''">
                statement_dt = #{statementDt},
            </if>
            <if test="filePath != null and filePath != ''">
                file_path = #{filePath},
            </if>
            <if test="fileName != null and fileName != ''">
                file_name = #{fileName},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="sendStat != null and sendStat != ''">
                send_stat = #{sendStat},
            </if>
            <if test="sendTime != null">
                send_time = #{sendTime},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="fileGenerateStat != null and fileGenerateStat != ''">
                file_generate_stat = #{fileGenerateStat},
            </if>
            <if test="memo != null and memo != ''">
                memo = #{memo},
            </if>
            <if test="sendCount != null">
                send_count = #{sendCount},
            </if>
            <if test="fileGenerateTime != null">
                file_generate_time = #{fileGenerateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from bt_statement_info_rec
        where id = #{id}
    </delete>

    <!--分页查询，根据主键ID升序排序-->
    <select id="selectWithPage" parameterType="com.howbuy.dtms.manager.dao.query.BtStatementInfoRecQuery"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bt_statement_info_rec
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and hk_cust_no = #{hkCustNo}
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                and fund_tx_acct_no = #{fundTxAcctNo}
            </if>
            <if test="statementTypeList != null and statementTypeList.size() > 0">
                and statement_type in
                <foreach item="item" index="index" collection="statementTypeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="fundCodeList != null and fundCodeList.size() > 0">
                and fund_code in
                <foreach item="item" index="index" collection="fundCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="statementStartDt != null and statementStartDt != '' ">
                <![CDATA[ and statement_dt >= #{statementStartDt} ]]>
            </if>
            <if test="statementEndDt != null and statementEndDt != '' ">
                <![CDATA[ and statement_dt <= #{statementEndDt} ]]>
            </if>
            <if test="fundTxAcctNoList != null and fundTxAcctNoList.size() > 0">
                and fund_tx_acct_no in
                <foreach item="item" index="index" collection="fundTxAcctNoList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="fileGenerateStatList != null and fileGenerateStatList.size() > 0">
                and file_generate_stat in
                <foreach item="item" index="index" collection="fileGenerateStatList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sendStatList != null and sendStatList.size() > 0">
                and send_stat in
                <foreach item="item" index="index" collection="sendStatList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="idList != null and idList.size > 0">
                and id in
                <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="hkCustNoList != null and hkCustNoList.size > 0">
                and hk_cust_no in
                <foreach item="item" index="index" collection="hkCustNoList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test='sendCountFlag != null and sendCountFlag == "0"'>
                and (send_count = 0 or send_count is null)
            </if>
            <if test='sendCountFlag != null and sendCountFlag == "1"'>
                and send_count = 1
            </if>
            <if test='sendCountFlag != null and sendCountFlag == "2"'>
                and send_count = 2
            </if>
            <if test='sendCountFlag != null and sendCountFlag == "3"'>
                and send_count <![CDATA[>]]> 2
            </if>
            and is_deleted = 0
        </where>
        order by statement_dt desc, hk_cust_no asc
    </select>
</mapper>

