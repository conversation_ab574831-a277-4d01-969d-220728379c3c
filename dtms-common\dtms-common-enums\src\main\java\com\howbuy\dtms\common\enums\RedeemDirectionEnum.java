/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 赎回方向枚举
 * @date 2024/4/17 14:08
 * @since JDK 1.8
 */
public enum RedeemDirectionEnum {
    /**
     * 1-回银行卡|电汇
     */
    KEEPBANK("1", "回银行卡", 0),
    /**
     * 2-留账好买香港账户
     */
    KEEPACCOUNT("2", "留账好买香港账户", 1),
    /**
     * 3-回海外储蓄罐
     */
    OVERSEASPIGGY("3", "回海外储蓄罐", 2),
    /**
     * 4-基金转投
     */
    TRANSFER("4", "基金转投", 3);

    private final String key;
    private final String desc;
    /**
     * redeemDirection 1111 . 该枚举所属 index 值
     */
    private final int index;

    public static RedeemDirectionEnum getEnum(String key) {
        for (RedeemDirectionEnum statusEnum : RedeemDirectionEnum.values()) {
            if (statusEnum.getKey().equals(key)) {
                return statusEnum;
            }
        }
        return null;
    }

    public static String getDesc(String code) {
        RedeemDirectionEnum redeemDirectionEnum = getEnum(code);
        return redeemDirectionEnum == null ? null : redeemDirectionEnum.getDesc();
    }

    /**
     * @param redeemDirection
     * @return java.util.List<com.howbuy.dtms.common.enums.RedeemDirectionEnum>
     * @description:根据值 获取 包括的枚举列表
     * <AUTHOR>
     * @date 2024/5/8 18:03
     * @since JDK 1.8
     */
    public static List<RedeemDirectionEnum> getListByValue(String redeemDirection) {
        if (StringUtils.isEmpty(redeemDirection)) {
            return Lists.newArrayList();
        }
        List<RedeemDirectionEnum> returnList = Lists.newArrayList();
        for (RedeemDirectionEnum directionEnum : RedeemDirectionEnum.values()) {
            if (YesOrNoEnum.YES.getValue().equals(String.valueOf(redeemDirection.charAt(directionEnum.index)))) {
                returnList.add(directionEnum);
            }
        }
        return returnList;
    }

    /**
     * @param redeemDirection
     * @return java.lang.String
     * @description:(根据值获取第一个枚举code)
     * <AUTHOR>
     * @date 2024/12/11 10:34
     * @since JDK 1.8
     */
    public static String getFirstCodeByValue(String redeemDirection) {
        List<RedeemDirectionEnum> enumList = getListByValue(redeemDirection);
        if (CollectionUtils.isEmpty(enumList)) {
            return null;
        }
        return enumList.get(0).getKey();
    }

    /**
     * @param code
     * @return java.lang.String
     * @description:根据枚举code获取值
     * <AUTHOR>
     * @date 2024/5/8 18:04
     * @since JDK 1.8
     */
    public static String getValueByEnumCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (RedeemDirectionEnum directionEnum : RedeemDirectionEnum.values()) {
            if (code.equals(directionEnum.getKey())) {
                sb.append(YesOrNoEnum.YES.getValue());
            } else {
                sb.append(YesOrNoEnum.NO.getValue());
            }
        }
        return sb.toString();

    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    RedeemDirectionEnum(String key, String desc, int index) {
        this.key = key;
        this.desc = desc;
        this.index = index;
    }
}
