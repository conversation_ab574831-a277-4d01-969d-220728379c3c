<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.BpSupplementalAgreementConfigDtlMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.BpSupplementalAgreementConfigDtlPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="agrId" column="agr_id" jdbcType="BIGINT"/>
            <result property="agrDtlId" column="agr_dtl_id" jdbcType="BIGINT"/>
            <result property="fundCode" column="fund_code" jdbcType="VARCHAR"/>
            <result property="hkCustNo" column="hk_cust_no" jdbcType="VARCHAR"/>
            <result property="custChineseName" column="cust_chinese_name" jdbcType="VARCHAR"/>
            <result property="idNoMask" column="id_no_mask" jdbcType="VARCHAR"/>
            <result property="idNoCipher" column="id_no_cipher" jdbcType="VARCHAR"/>
            <result property="idNoDigest" column="id_no_digest" jdbcType="VARCHAR"/>
            <result property="needSignAgreement" column="need_sign_agreement" jdbcType="CHAR"/>
            <result property="recStat" column="rec_stat" jdbcType="CHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,agr_id,agr_dtl_id,
        fund_code,hk_cust_no,cust_chinese_name,
        id_no_mask,id_no_cipher,id_no_digest,
        need_sign_agreement,rec_stat,create_time,
        creator,update_time,modifier
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from bp_supplemental_agreement_config_dtl
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from bp_supplemental_agreement_config_dtl
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.BpSupplementalAgreementConfigDtlPO" useGeneratedKeys="true">
        insert into bp_supplemental_agreement_config_dtl
        ( id,agr_id,agr_dtl_id
        ,fund_code,hk_cust_no,cust_chinese_name
        ,id_no_mask,id_no_cipher,id_no_digest
        ,need_sign_agreement,rec_stat,create_time
        ,creator,update_time,modifier
        )
        values (#{id,jdbcType=BIGINT},#{agrId,jdbcType=BIGINT},#{agrDtlId,jdbcType=BIGINT}
        ,#{fundCode,jdbcType=VARCHAR},#{hkCustNo,jdbcType=VARCHAR},#{custChineseName,jdbcType=VARCHAR}
        ,#{idNoMask,jdbcType=VARCHAR},#{idNoCipher,jdbcType=VARCHAR},#{idNoDigest,jdbcType=VARCHAR}
        ,#{needSignAgreement,jdbcType=CHAR},#{recStat,jdbcType=CHAR},#{createTime,jdbcType=TIMESTAMP}
        ,#{creator,jdbcType=VARCHAR},#{updateTime,jdbcType=TIMESTAMP},#{modifier,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.BpSupplementalAgreementConfigDtlPO" useGeneratedKeys="true">
        insert into bp_supplemental_agreement_config_dtl
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="agrId != null">agr_id,</if>
                <if test="agrDtlId != null">agr_dtl_id,</if>
                <if test="fundCode != null">fund_code,</if>
                <if test="hkCustNo != null">hk_cust_no,</if>
                <if test="custChineseName != null">cust_chinese_name,</if>
                <if test="idNoMask != null">id_no_mask,</if>
                <if test="idNoCipher != null">id_no_cipher,</if>
                <if test="idNoDigest != null">id_no_digest,</if>
                <if test="needSignAgreement != null">need_sign_agreement,</if>
                <if test="recStat != null">rec_stat,</if>
                <if test="createTime != null">create_time,</if>
                <if test="creator != null">creator,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="modifier != null">modifier,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="agrId != null">#{agrId,jdbcType=BIGINT},</if>
                <if test="agrDtlId != null">#{agrDtlId,jdbcType=BIGINT},</if>
                <if test="fundCode != null">#{fundCode,jdbcType=VARCHAR},</if>
                <if test="hkCustNo != null">#{hkCustNo,jdbcType=VARCHAR},</if>
                <if test="custChineseName != null">#{custChineseName,jdbcType=VARCHAR},</if>
                <if test="idNoMask != null">#{idNoMask,jdbcType=VARCHAR},</if>
                <if test="idNoCipher != null">#{idNoCipher,jdbcType=VARCHAR},</if>
                <if test="idNoDigest != null">#{idNoDigest,jdbcType=VARCHAR},</if>
                <if test="needSignAgreement != null">#{needSignAgreement,jdbcType=CHAR},</if>
                <if test="recStat != null">#{recStat,jdbcType=CHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="creator != null">#{creator,jdbcType=VARCHAR},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="modifier != null">#{modifier,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.dtms.manager.dao.po.BpSupplementalAgreementConfigDtlPO">
        update bp_supplemental_agreement_config_dtl
        <set>
                <if test="agrId != null">
                    agr_id = #{agrId,jdbcType=BIGINT},
                </if>
                <if test="agrDtlId != null">
                    agr_dtl_id = #{agrDtlId,jdbcType=BIGINT},
                </if>
                <if test="fundCode != null">
                    fund_code = #{fundCode,jdbcType=VARCHAR},
                </if>
                <if test="hkCustNo != null">
                    hk_cust_no = #{hkCustNo,jdbcType=VARCHAR},
                </if>
                <if test="custChineseName != null">
                    cust_chinese_name = #{custChineseName,jdbcType=VARCHAR},
                </if>
                <if test="idNoMask != null">
                    id_no_mask = #{idNoMask,jdbcType=VARCHAR},
                </if>
                <if test="idNoCipher != null">
                    id_no_cipher = #{idNoCipher,jdbcType=VARCHAR},
                </if>
                <if test="idNoDigest != null">
                    id_no_digest = #{idNoDigest,jdbcType=VARCHAR},
                </if>
                <if test="needSignAgreement != null">
                    need_sign_agreement = #{needSignAgreement,jdbcType=CHAR},
                </if>
                <if test="recStat != null">
                    rec_stat = #{recStat,jdbcType=CHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="creator != null">
                    creator = #{creator,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="modifier != null">
                    modifier = #{modifier,jdbcType=VARCHAR},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.howbuy.dtms.manager.dao.po.BpSupplementalAgreementConfigDtlPO">
        update bp_supplemental_agreement_config_dtl
        set 
            agr_id =  #{agrId,jdbcType=BIGINT},
            agr_dtl_id =  #{agrDtlId,jdbcType=BIGINT},
            fund_code =  #{fundCode,jdbcType=VARCHAR},
            hk_cust_no =  #{hkCustNo,jdbcType=VARCHAR},
            cust_chinese_name =  #{custChineseName,jdbcType=VARCHAR},
            id_no_mask =  #{idNoMask,jdbcType=VARCHAR},
            id_no_cipher =  #{idNoCipher,jdbcType=VARCHAR},
            id_no_digest =  #{idNoDigest,jdbcType=VARCHAR},
            need_sign_agreement =  #{needSignAgreement,jdbcType=CHAR},
            rec_stat =  #{recStat,jdbcType=CHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            creator =  #{creator,jdbcType=VARCHAR},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            modifier =  #{modifier,jdbcType=VARCHAR}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
