package com.howbuy.dtms.manager.counter.request.paymentcheck;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 支付对账查询请求对象
 *
 * <AUTHOR>
 * @date 2025-07-23 15:11:25
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryPaymentCheckListRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 支付订单号
     */
    private String pmtDealNo;

    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 外部支付订单号
     */
    private String outPmtDealNo;

    /**
     * 基金代码列表
     */
    private List<String> fundCodes;

    /**
     * 支付对账日期
     */
    private String pmtCheckDt;

    /**
     * 支付对账标记列表
     */
    private List<String> pmtCompFlags;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 20;
}
