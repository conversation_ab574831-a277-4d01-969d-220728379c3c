<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.HwPayVoucherOrderAlterRecordMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwPayVoucherOrderAlterRecordPO">
        <!--@Table hw_pay_voucher_order_alter_record-->
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="voucherNo" column="voucher_no" jdbcType="VARCHAR"/>
        <result property="auditStatus" column="audit_status" jdbcType="VARCHAR"/>
        <result property="auditReason" column="audit_reason" jdbcType="VARCHAR"/>
        <result property="auditOperator" column="audit_operator" jdbcType="VARCHAR"/>
        <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
        <result property="recStat" column="rec_stat" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="operateStatus" column="operate_status" jdbcType="VARCHAR"/>
        <result property="ipAddress" column="ip_address" jdbcType="VARCHAR"/>
        <result property="macAddress" column="mac_address" jdbcType="VARCHAR"/>
        <result property="deviceNumber" column="device_number" jdbcType="VARCHAR"/>
        <result property="deviceModel" column="device_model" jdbcType="VARCHAR"/>
        <result property="deviceName" column="device_name" jdbcType="VARCHAR"/>
        <result property="systemVersion" column="system_version" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, 
        voucher_no, 
        audit_status, 
        audit_reason, 
        audit_operator, 
        audit_time, 
        rec_stat, 
        creator, 
        modifier, 
        create_time, 
        update_time, 
        operate_status, 
        ip_address, 
        mac_address, 
        device_number, 
        device_model, 
        device_name, 
        system_version
    </sql>

    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hw_pay_voucher_order_alter_record
        where id = #{id}
    </select>

    <!-- 查询符合条件的数据 -->
    <select id="selectBySelective" parameterType="com.howbuy.dtms.manager.dao.po.HwPayVoucherOrderAlterRecordPO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hw_pay_voucher_order_alter_record
        <where>
                <if test="id != null">
                    and id = #{id}
                </if>
                <if test="voucherNo != null and voucherNo != ''">
                    and voucher_no = #{voucherNo}
                </if>
                <if test="auditStatus != null and auditStatus != ''">
                    and audit_status = #{auditStatus}
                </if>
                <if test="auditReason != null and auditReason != ''">
                    and audit_reason = #{auditReason}
                </if>
                <if test="auditOperator != null and auditOperator != ''">
                    and audit_operator = #{auditOperator}
                </if>
                <if test="auditTime != null">
                    and audit_time = #{auditTime}
                </if>
                <if test="recStat != null and recStat != ''">
                    and rec_stat = #{recStat}
                </if>
                <if test="creator != null and creator != ''">
                    and creator = #{creator}
                </if>
                <if test="modifier != null and modifier != ''">
                    and modifier = #{modifier}
                </if>
                <if test="createTime != null">
                    and create_time = #{createTime}
                </if>
                <if test="updateTime != null">
                    and update_time = #{updateTime}
                </if>
                <if test="operateStatus != null and operateStatus != ''">
                    and operate_status = #{operateStatus}
                </if>
                <if test="ipAddress != null and ipAddress != ''">
                    and ip_address = #{ipAddress}
                </if>
                <if test="macAddress != null and macAddress != ''">
                    and mac_address = #{macAddress}
                </if>
                <if test="deviceNumber != null and deviceNumber != ''">
                    and device_number = #{deviceNumber}
                </if>
                <if test="deviceModel != null and deviceModel != ''">
                    and device_model = #{deviceModel}
                </if>
                <if test="deviceName != null and deviceName != ''">
                    and device_name = #{deviceName}
                </if>
                <if test="systemVersion != null and systemVersion != ''">
                    and system_version = #{systemVersion}
                </if>
        </where>
        order by id
    </select>


    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(*)
        from hw_pay_voucher_order_alter_record
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="voucherNo != null and voucherNo != ''">
                and voucher_no = #{voucherNo}
            </if>
            <if test="auditStatus != null and auditStatus != ''">
                and audit_status = #{auditStatus}
            </if>
            <if test="auditReason != null and auditReason != ''">
                and audit_reason = #{auditReason}
            </if>
            <if test="auditOperator != null and auditOperator != ''">
                and audit_operator = #{auditOperator}
            </if>
            <if test="auditTime != null">
                and audit_time = #{auditTime}
            </if>
            <if test="recStat != null and recStat != ''">
                and rec_stat = #{recStat}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="modifier != null and modifier != ''">
                and modifier = #{modifier}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="operateStatus != null and operateStatus != ''">
                and operate_status = #{operateStatus}
            </if>
            <if test="ipAddress != null and ipAddress != ''">
                and ip_address = #{ipAddress}
            </if>
            <if test="macAddress != null and macAddress != ''">
                and mac_address = #{macAddress}
            </if>
            <if test="deviceNumber != null and deviceNumber != ''">
                and device_number = #{deviceNumber}
            </if>
            <if test="deviceModel != null and deviceModel != ''">
                and device_model = #{deviceModel}
            </if>
            <if test="deviceName != null and deviceName != ''">
                and device_name = #{deviceName}
            </if>
            <if test="systemVersion != null and systemVersion != ''">
                and system_version = #{systemVersion}
            </if>
        </where>
    </select>

    <!--分页查询，根据主键ID升序排序-->
    <select id="selectWithPage" parameterType="com.howbuy.dtms.manager.dao.query.HwPayVoucherOrderAlterRecordQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hw_pay_voucher_order_alter_record
        <where>
                    <if test="id != null">
                        and id = #{id}
                    </if>
                    <if test="voucherNo != null and voucherNo != ''">
                        and voucher_no = #{voucherNo}
                    </if>
                    <if test="auditStatus != null and auditStatus != ''">
                        and audit_status = #{auditStatus}
                    </if>
                    <if test="auditReason != null and auditReason != ''">
                        and audit_reason = #{auditReason}
                    </if>
                    <if test="auditOperator != null and auditOperator != ''">
                        and audit_operator = #{auditOperator}
                    </if>
                    <if test="auditTime != null">
                        and audit_time = #{auditTime}
                    </if>
                    <if test="recStat != null and recStat != ''">
                        and rec_stat = #{recStat}
                    </if>
                    <if test="creator != null and creator != ''">
                        and creator = #{creator}
                    </if>
                    <if test="modifier != null and modifier != ''">
                        and modifier = #{modifier}
                    </if>
                    <if test="createTime != null">
                        and create_time = #{createTime}
                    </if>
                    <if test="updateTime != null">
                        and update_time = #{updateTime}
                    </if>
                    <if test="operateStatus != null and operateStatus != ''">
                        and operate_status = #{operateStatus}
                    </if>
                    <if test="ipAddress != null and ipAddress != ''">
                        and ip_address = #{ipAddress}
                    </if>
                    <if test="macAddress != null and macAddress != ''">
                        and mac_address = #{macAddress}
                    </if>
                    <if test="deviceNumber != null and deviceNumber != ''">
                        and device_number = #{deviceNumber}
                    </if>
                    <if test="deviceModel != null and deviceModel != ''">
                        and device_model = #{deviceModel}
                    </if>
                    <if test="deviceName != null and deviceName != ''">
                        and device_name = #{deviceName}
                    </if>
                    <if test="systemVersion != null and systemVersion != ''">
                        and system_version = #{systemVersion}
                    </if>
            <if test="idList != null and idList.size > 0">
                and id in
                <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and rec_stat = '0'
        </where>
        order by id
    </select>

    <select id="listRecordByVoucherNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_pay_voucher_order_alter_record
        where voucher_no = #{voucherNo} and rec_stat = '0' order by id
    </select>

    <select id="getLastRecordByVoucherNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_pay_voucher_order_alter_record
        where voucher_no = #{voucherNo,jdbcType=BIGINT}
        order by create_time desc,id desc
        limit 1
    </select>
</mapper>

