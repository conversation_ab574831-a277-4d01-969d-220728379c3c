package com.howbuy.dtms.manager.cache.redis;


import java.util.concurrent.TimeUnit;

/**
 * @Description: 缓存接口类
 * <AUTHOR> jiangwei.ji
 * @Date: 2024/8/15
*/
public interface IRedisService {

    /**
     * 获取key值
     * @param key
     * @return
     */
    Object get(String key);

    /**
     * 获取string类型值
     * @param key
     * @return
     */
    String getString(String key);

    /**
     * 设置string类型值
     * @param key
     * @param value
     * @param timeout
     * @param unit
     */
    void set(String key,String value,long timeout,TimeUnit unit);

    /**
     * 设置string类型值
     * @param key
     * @param value
     */
    void set(String key,String value);
    /**
     * 设置值
     * @param key
     * @param timeout
     * @param unit
     * @return
     */
    boolean lock(String key,long timeout , TimeUnit unit);

    boolean lock(String key, String v, long timeout, TimeUnit unit);

    /**
     * 加自旋锁
     */
    boolean lockAcquire(String key, long timeout, TimeUnit unit, Integer acquireSeconds);

    /**
     * 删除
     * @param key
     */
    void delete(String key);

    /**
     * 设置值，以及超时时间
     * @param key
     * @param value
     * @param timeout
     * @param unit
     */
    void set(String key, Object value, long timeout, TimeUnit unit);

    /**
     * 设置值，以及超时时间
     * @param key
     * @param value
     */
    void set(String key, Object value);


    /**
     * 自增
     * @param key
     * @return
     */
    long incr(String key);


    /**
     * 判断是否存在
     * @param key
     * @return
     */
    boolean exists(String key);

}
