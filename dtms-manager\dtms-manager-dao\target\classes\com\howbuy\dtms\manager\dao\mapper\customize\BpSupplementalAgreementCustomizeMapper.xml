<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.customize.BpSupplementalAgreementCustomizeMapper">

    <sql id="Base_Column_List">
        a.id,a.agr_id,a.fund_code,a.fund_abbr,
        a.fund_man_code,a.agreement_name,a.agreement_description,
        a.agreement_url,a.agreement_sign_end_dt,a.version,
        a.rec_stat,a.creator,a.create_time,
        a.modifier,a.update_time
    </sql>
    <select id="queryAgreementPageListByHkCustNoAndFundCodeAndFundMainCode"
            resultMap="com.howbuy.dtms.manager.dao.mapper.BpSupplementalAgreementConfigMapper.BaseResultMap">
        select
            <include refid="Base_Column_List"/>
            from bp_supplemental_agreement_config a
        <if test="agreementQuery.hkCustNo != null and agreementQuery.hkCustNo != ''">
            left join bp_supplemental_agreement_config_dtl b
            on a.agr_id = b.agr_id
        </if>
        where a.rec_stat = '1'
            <if test="agreementQuery.hkCustNo != null and agreementQuery.hkCustNo != ''">
                and b.hk_cust_no = #{agreementQuery.hkCustNo,jdbcType=VARCHAR}
            </if>
            <if test="agreementQuery.fundCodeList != null and agreementQuery.fundCodeList.size() > 0">
                and a.fund_code in
                <foreach collection="agreementQuery.fundCodeList" item="fundCode" open="(" separator="," close=")">
                    #{fundCode,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="agreementQuery.fundManCodeList != null and agreementQuery.fundManCodeList.size() > 0">
                and a.fund_man_code in
                <foreach collection="agreementQuery.fundManCodeList" item="fundManCode" open="(" separator="," close=")">
                    #{fundManCode,jdbcType=VARCHAR}
                </foreach>
            </if>

        order by a.create_time DESC ,a.id DESC
    </select>

    <select id="queryAgreementByFundCodeAndFundName" resultMap="com.howbuy.dtms.manager.dao.mapper.BpSupplementalAgreementConfigMapper.BaseResultMap">
        SELECT 
            <include refid="com.howbuy.dtms.manager.dao.mapper.BpSupplementalAgreementConfigMapper.Base_Column_List"/>
        FROM 
            bp_supplemental_agreement_config
        WHERE 
            fund_code = #{fundCode,jdbcType=VARCHAR}
            AND agreement_name = #{agreementName,jdbcType=VARCHAR}
            AND rec_stat = '1'
        LIMIT 1
    </select>
    <select id="queryAgreementConfigByAgrId"
            resultType="com.howbuy.dtms.manager.dao.po.BpSupplementalAgreementConfigPO">
        select
            <include refid="com.howbuy.dtms.manager.dao.mapper.BpSupplementalAgreementConfigMapper.Base_Column_List"/>
        from bp_supplemental_agreement_config
        where agr_id = #{agrId,jdbcType=BIGINT}
          and rec_stat = '1'
        limit 1
    </select>
</mapper> 