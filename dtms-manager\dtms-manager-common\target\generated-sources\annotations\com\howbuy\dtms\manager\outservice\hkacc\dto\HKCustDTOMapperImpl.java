package com.howbuy.dtms.manager.outservice.hkacc.dto;

import com.howbuy.hkacconline.facade.query.querycipher.QueryHkCustCipherResponse;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:23:42+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class HKCustDTOMapperImpl implements HKCustDTOMapper {

    @Override
    public HkCustInfoCipherDTO toCipherDTO(QueryHkCustCipherResponse custCipherResponse) {
        if ( custCipherResponse == null ) {
            return null;
        }

        HkCustInfoCipherDTO hkCustInfoCipherDTO = new HkCustInfoCipherDTO();

        hkCustInfoCipherDTO.setEmailCipher( custCipherResponse.getEmailCipher() );
        hkCustInfoCipherDTO.setEmailDigest( custCipherResponse.getEmailDigest() );
        hkCustInfoCipherDTO.setEmailMask( custCipherResponse.getEmailMask() );
        hkCustInfoCipherDTO.setIdNoCipher( custCipherResponse.getIdNoCipher() );
        hkCustInfoCipherDTO.setIdNoDigest( custCipherResponse.getIdNoDigest() );
        hkCustInfoCipherDTO.setIdNoMask( custCipherResponse.getIdNoMask() );
        hkCustInfoCipherDTO.setStatementEmailCipher( custCipherResponse.getStatementEmailCipher() );
        hkCustInfoCipherDTO.setStatementEmailDigest( custCipherResponse.getStatementEmailDigest() );
        hkCustInfoCipherDTO.setStatementEmailMask( custCipherResponse.getStatementEmailMask() );

        return hkCustInfoCipherDTO;
    }
}
