<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.HwBankCardSnapshotMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwBankCardSnapshotPO">
        <!--@Table hw_bank_card_snapshot-->
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="hkCpAcctNo" column="hk_cp_acct_no" jdbcType="VARCHAR"/>
        <result property="bankName" column="bank_name" jdbcType="VARCHAR"/>
        <result property="bankChineseName" column="bank_chinese_name" jdbcType="VARCHAR"/>
        <result property="bankAcctDigest" column="bank_acct_digest" jdbcType="VARCHAR"/>
        <result property="bankAcctMask" column="bank_acct_mask" jdbcType="VARCHAR"/>
        <result property="bankAcctCipher" column="bank_acct_cipher" jdbcType="VARCHAR"/>
        <result property="bankAcctName" column="bank_acct_name" jdbcType="VARCHAR"/>
        <result property="currencyCodes" column="currency_codes" jdbcType="VARCHAR"/>
        <result property="swiftCode" column="swift_code" jdbcType="VARCHAR"/>
        <result property="jointAccount" column="joint_account" jdbcType="VARCHAR"/>
        <result property="bizType" column="biz_type" jdbcType="VARCHAR"/>
        <result property="bizId" column="biz_id" jdbcType="BIGINT"/>
        <result property="recStat" column="rec_stat" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, 
        hk_cp_acct_no, 
        bank_name, 
        bank_chinese_name, 
        bank_acct_digest, 
        bank_acct_mask, 
        bank_acct_name,
        bank_acct_cipher,
        currency_codes,
        swift_code, 
        joint_account, 
        biz_type, 
        biz_id, 
        rec_stat, 
        creator, 
        modifier, 
        create_time, 
        update_time
    </sql>

    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hw_bank_card_snapshot
        where id = #{id}
    </select>

    <!-- 查询符合条件的数据 -->
    <select id="selectBySelective" parameterType="com.howbuy.dtms.manager.dao.po.HwBankCardSnapshotPO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hw_bank_card_snapshot
        <where>
                <if test="id != null">
                    and id = #{id}
                </if>
                <if test="hkCpAcctNo != null and hkCpAcctNo != ''">
                    and hk_cp_acct_no = #{hkCpAcctNo}
                </if>
                <if test="bankName != null and bankName != ''">
                    and bank_name = #{bankName}
                </if>
                <if test="bankChineseName != null and bankChineseName != ''">
                    and bank_chinese_name = #{bankChineseName}
                </if>
                <if test="bankAcctDigest != null and bankAcctDigest != ''">
                    and bank_acct_digest = #{bankAcctDigest}
                </if>
                <if test="bankAcctMask != null and bankAcctMask != ''">
                    and bank_acct_mask = #{bankAcctMask}
                </if>
                <if test="bankAcctName != null and bankAcctName != ''">
                    and bank_acct_name = #{bankAcctName}
                </if>
                <if test="currencyCodes != null and currencyCodes != ''">
                    and currency_codes = #{currencyCodes}
                </if>
                <if test="swiftCode != null and swiftCode != ''">
                    and swift_code = #{swiftCode}
                </if>
                <if test="jointAccount != null and jointAccount != ''">
                    and joint_account = #{jointAccount}
                </if>
                <if test="bizType != null and bizType != ''">
                    and biz_type = #{bizType}
                </if>
                <if test="bizId != null">
                    and biz_id = #{bizId}
                </if>
                <if test="recStat != null and recStat != ''">
                    and rec_stat = #{recStat}
                </if>
                <if test="creator != null and creator != ''">
                    and creator = #{creator}
                </if>
                <if test="modifier != null and modifier != ''">
                    and modifier = #{modifier}
                </if>
                <if test="createTime != null">
                    and create_time = #{createTime}
                </if>
                <if test="updateTime != null">
                    and update_time = #{updateTime}
                </if>
        </where>
        order by id
    </select>


    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(*)
        from hw_bank_card_snapshot
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="hkCpAcctNo != null and hkCpAcctNo != ''">
                and hk_cp_acct_no = #{hkCpAcctNo}
            </if>
            <if test="bankName != null and bankName != ''">
                and bank_name = #{bankName}
            </if>
            <if test="bankChineseName != null and bankChineseName != ''">
                and bank_chinese_name = #{bankChineseName}
            </if>
            <if test="bankAcctDigest != null and bankAcctDigest != ''">
                and bank_acct_digest = #{bankAcctDigest}
            </if>
            <if test="bankAcctMask != null and bankAcctMask != ''">
                and bank_acct_mask = #{bankAcctMask}
            </if>
            <if test="bankAcctName != null and bankAcctName != ''">
                and bank_acct_name = #{bankAcctName}
            </if>
            <if test="currencyCodes != null and currencyCodes != ''">
                and currency_codes = #{currencyCodes}
            </if>
            <if test="swiftCode != null and swiftCode != ''">
                and swift_code = #{swiftCode}
            </if>
            <if test="jointAccount != null and jointAccount != ''">
                and joint_account = #{jointAccount}
            </if>
            <if test="bizType != null and bizType != ''">
                and biz_type = #{bizType}
            </if>
            <if test="bizId != null">
                and biz_id = #{bizId}
            </if>
            <if test="recStat != null and recStat != ''">
                and rec_stat = #{recStat}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="modifier != null and modifier != ''">
                and modifier = #{modifier}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>

    <select id="selectByBizIdAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hw_bank_card_snapshot
        where biz_id = #{bizId} and biz_type = #{bizType}
        and rec_stat = '0'
    </select>
</mapper>

