<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="4c1de256-cd78-4bb0-a2c7-145a1de96a55" name="Changes" comment="fix：修改预约日期-判断是否支持预约">
      <change afterPath="$PROJECT_DIR$/dtms-manager/.知识库/设计文档/公共模块提示词.md" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/dtms-manager" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="useMavenConfig" value="true" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectId" id="2zxKJUAOR57Mt8c6OLDUUT9EIa6" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "last_opened_file_path": "D:/workspace/git/dtms-for-manager/dtms-manager/.知识库/设计文档",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "editor.preferences.fonts.default",
    "spring.configuration.checksum": "4acf449e210af08bf0c5e5393f04188e",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\workspace\git\dtms-for-manager\dtms-manager\dtms-manager-counter\src\main\java\com\howbuy\dtms\manager\counter\request" />
      <recent name="D:\workspace\git\dtms-for-manager\dtms-manager\dtms-manager-counter\src\main\java\com\howbuy\dtms\manager\counter\service\payment" />
      <recent name="D:\workspace\git\dtms-for-manager\dtms-manager\dtms-manager-counter\src\main\java\com\howbuy\dtms\manager\counter\controller\payment" />
      <recent name="D:\workspace\git\dtms-for-manager\dtms-manager\.知识库\设计文档\支付对账" />
      <recent name="D:\workspace\git\dtms-for-manager\dtms-manager\.知识库\设计文档\储蓄罐交易申请" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.howbuy.dtms.manager.outservice.param.dto" />
    </key>
  </component>
  <component name="RunManager" selected="JUnit.TestBuildUpdateItemDtl">
    <configuration name="TestBuildUpdateItemDtl" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="dtms-manager-console" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.howbuy.dtms.manager.console.service.hwdealorderdtl.batchupdatepresubmittadtservice.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.howbuy.dtms.manager.console.service.hwdealorderdtl.batchupdatepresubmittadtservice" />
      <option name="MAIN_CLASS_NAME" value="com.howbuy.dtms.manager.console.service.hwdealorderdtl.batchupdatepresubmittadtservice.TestBuildUpdateItemDtl" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DtmsManagerRemoteApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="dtms-manager-remote" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.howbuy.dtms.manager.DtmsManagerRemoteApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.TestBuildUpdateItemDtl" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="4c1de256-cd78-4bb0-a2c7-145a1de96a55" name="Changes" comment="" />
      <created>1752667212795</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752667212795</updated>
      <workItem from="1752667213541" duration="21533000" />
      <workItem from="1752800877867" duration="645000" />
      <workItem from="1752809361275" duration="2911000" />
      <workItem from="1752828173280" duration="776000" />
      <workItem from="1753089364831" duration="58725000" />
      <workItem from="1753406606108" duration="1472000" />
      <workItem from="1753410009538" duration="414000" />
      <workItem from="1753424387250" duration="1787000" />
      <workItem from="1753687304803" duration="997000" />
      <workItem from="1753696732097" duration="599000" />
      <workItem from="1753843835666" duration="15820000" />
      <workItem from="1753949822353" duration="671000" />
      <workItem from="1754013076024" duration="3282000" />
      <workItem from="1754032494211" duration="2810000" />
      <workItem from="1754273223231" duration="6142000" />
      <workItem from="1754299078157" duration="316000" />
    </task>
    <task id="LOCAL-00001" summary="设计文档">
      <created>1752667638001</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752667638001</updated>
    </task>
    <task id="LOCAL-00002" summary="fix：修改枚举">
      <created>1752714933990</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752714933990</updated>
    </task>
    <task id="LOCAL-00003" summary="fix：导入储蓄罐交易申请(修改)">
      <created>1752717742343</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752717742343</updated>
    </task>
    <task id="LOCAL-00004" summary="fix：资金client">
      <created>1752736681451</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1752736681451</updated>
    </task>
    <task id="LOCAL-00005" summary="fix：生成储蓄罐订单(修改)">
      <created>1752739829120</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1752739829120</updated>
    </task>
    <task id="LOCAL-00006" summary="设计文档">
      <created>1752739840983</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1752739840983</updated>
    </task>
    <task id="LOCAL-00007" summary="fix：生成储蓄罐订单(修改)">
      <created>1752740360354</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1752740360354</updated>
    </task>
    <task id="LOCAL-00008" summary="fix：资金client">
      <created>1752740586020</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1752740586020</updated>
    </task>
    <task id="LOCAL-00009" summary="fix：生成储蓄罐订单(修改)">
      <created>1752746894320</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1752746894320</updated>
    </task>
    <task id="LOCAL-00010" summary="设计文档">
      <created>1752747017170</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1752747017170</updated>
    </task>
    <task id="LOCAL-00011" summary="fix：生成储蓄罐订单(修改)">
      <created>1752751552281</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1752751552281</updated>
    </task>
    <task id="LOCAL-00012" summary="fix：批量生成失败">
      <created>1752752782520</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1752752782520</updated>
    </task>
    <task id="LOCAL-00013" summary="fix：批量生成失败">
      <created>1752753227984</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1752753227984</updated>
    </task>
    <task id="LOCAL-00014" summary="fix：外部服务">
      <created>1753096765363</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1753096765363</updated>
    </task>
    <task id="LOCAL-00015" summary="设计文档">
      <created>1753096863612</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1753096863612</updated>
    </task>
    <task id="LOCAL-00016" summary="feat:计算储蓄罐交易申请手续费接口">
      <created>1753098701457</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1753098701457</updated>
    </task>
    <task id="LOCAL-00017" summary="设计文档">
      <created>1753098846938</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1753098846938</updated>
    </task>
    <task id="LOCAL-00018" summary="feat:计算储蓄罐交易申请手续费接口">
      <created>1753098890604</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1753098890604</updated>
    </task>
    <task id="LOCAL-00019" summary="feat:修改储蓄罐交易申请买入接口">
      <created>1753100013628</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1753100013628</updated>
    </task>
    <task id="LOCAL-00020" summary="feat:修改储蓄罐交易申请买入接口">
      <created>1753146869338</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1753146869338</updated>
    </task>
    <task id="LOCAL-00021" summary="feat:修改储蓄罐交易申请买入接口">
      <created>1753146932490</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1753146932490</updated>
    </task>
    <task id="LOCAL-00022" summary="feat:修改储蓄罐交易申请买入接口">
      <created>1753163563986</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1753163563986</updated>
    </task>
    <task id="LOCAL-00023" summary="fix：bootstrap文件修改">
      <created>1753170453002</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1753170453002</updated>
    </task>
    <task id="LOCAL-00024" summary="fix：查询海外储蓄罐导入申请表数据列表">
      <created>1753171412521</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1753171412521</updated>
    </task>
    <task id="LOCAL-00025" summary="fix：枚举">
      <created>1753181172133</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1753181172133</updated>
    </task>
    <task id="LOCAL-00026" summary="fix：查询海外储蓄罐导入申请表数据列表">
      <created>1753181211638</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1753181211638</updated>
    </task>
    <task id="LOCAL-00027" summary="feat：支付对账外部服务">
      <created>1753254463995</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1753254463995</updated>
    </task>
    <task id="LOCAL-00028" summary="设计文档">
      <created>1753254707940</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1753254707940</updated>
    </task>
    <task id="LOCAL-00029" summary="feat：支付对账查询接口">
      <created>1753256160590</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1753256160590</updated>
    </task>
    <task id="LOCAL-00030" summary="feat：支付对账接口">
      <created>1753258052853</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1753258052853</updated>
    </task>
    <task id="LOCAL-00031" summary="feat：重置交易支付标识">
      <created>1753258695941</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1753258695941</updated>
    </task>
    <task id="LOCAL-00032" summary="feat：支付对账下载接口">
      <created>1753263766011</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1753263766011</updated>
    </task>
    <task id="LOCAL-00033" summary="feat：重置交易支付标识">
      <created>1753346390639</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1753346390639</updated>
    </task>
    <task id="LOCAL-00034" summary="fix：支付方式-基金转投">
      <created>1753349552541</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1753349552541</updated>
    </task>
    <task id="LOCAL-00035" summary="fix：产品打款截止日期">
      <created>1753424608373</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1753424608373</updated>
    </task>
    <task id="LOCAL-00036" summary="fix：修改预计上报日期-每日开放">
      <created>1753866592693</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1753866592693</updated>
    </task>
    <task id="LOCAL-00037" summary="fix：单元测试">
      <created>1754015415723</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1754015415723</updated>
    </task>
    <task id="LOCAL-00038" summary="fix：订单明细查询">
      <created>1754034014043</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1754034014043</updated>
    </task>
    <task id="LOCAL-00039" summary="fix：修改预约日期-判断是否支持预约">
      <created>1754278403789</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1754278403789</updated>
    </task>
    <option name="localTasksCounter" value="40" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="fix：修改枚举" />
    <MESSAGE value="fix：导入储蓄罐交易申请(修改)" />
    <MESSAGE value="fix：资金client" />
    <MESSAGE value="fix：生成储蓄罐订单(修改)" />
    <MESSAGE value="fix：批量生成失败" />
    <MESSAGE value="fix：外部服务" />
    <MESSAGE value="feat:计算储蓄罐交易申请手续费接口" />
    <MESSAGE value="feat:修改储蓄罐交易申请买入接口" />
    <MESSAGE value="fix：bootstrap文件修改" />
    <MESSAGE value="fix：枚举" />
    <MESSAGE value="fix：查询海外储蓄罐导入申请表数据列表" />
    <MESSAGE value="feat：支付对账外部服务" />
    <MESSAGE value="设计文档" />
    <MESSAGE value="feat：支付对账查询接口" />
    <MESSAGE value="feat：支付对账接口" />
    <MESSAGE value="feat：支付对账下载接口" />
    <MESSAGE value="feat：重置交易支付标识" />
    <MESSAGE value="fix：支付方式-基金转投" />
    <MESSAGE value="fix：产品打款截止日期" />
    <MESSAGE value="fix：修改预计上报日期-每日开放" />
    <MESSAGE value="fix：单元测试" />
    <MESSAGE value="fix：订单明细查询" />
    <MESSAGE value="fix：修改预约日期-判断是否支持预约" />
    <option name="LAST_COMMIT_MESSAGE" value="fix：修改预约日期-判断是否支持预约" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>