/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description: 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @date 2024/4/18 19:28
 * @since JDK 1.8
 */
public enum TradeChannelEnum {
    /**
     * 柜台
     */
    COUNTER("1", "柜台"),
    /**
     * 网站
     */
    WEBSITE("2", "网站"),
    /**
     * 电话
     */
    PHONE("3", "电话"),
    /**
     * Wap
     */
    WAP("4", "Wap"),
    /**
     * CRM-PC
     */
    CRM_PC("9", "CRM-PC"),
    /**
     * CRM移动端
     */
    CRM_MOBILE("10", "CRM移动端"),
    /**
     * APP
     */
    APP("11", "APP"),

    /**
     * H5
     */
    H5("12", "H5"),

    ;

    private String code;
    private String desc;

    TradeChannelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TradeChannelEnum getEnumByCode(String code) {
        for (TradeChannelEnum tradeChannelEnum : TradeChannelEnum.values()) {
            if (tradeChannelEnum.getCode().equals(code)) {
                return tradeChannelEnum;
            }
        }
        return null;
    }



    /**
     * @description: 获取渠道code和desc，示例："11-APP"
     * @param code
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2024/7/26 17:36
     * @since JDK 1.8
     */
    public static String getCodeAndDesc(String code) {
        for (TradeChannelEnum tradeChannelEnum : TradeChannelEnum.values()) {
            if (tradeChannelEnum.getCode().equals(code)) {
                return tradeChannelEnum.getCode() + "-" + tradeChannelEnum.getDesc();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    /**
     * @description: 判断是否为线上渠道
     * @param code
     * @return boolean
     * @author: jin.wang03
     * @date: 2024/7/25 19:01
     * @since JDK 1.8
     */
    public static boolean isOnline(String code) {
        if (StringUtils.isEmpty(code)) {
            return false;
        }

        return code.equals(TradeChannelEnum.WAP.getCode()) || code.equals(TradeChannelEnum.APP.getCode())
                || code.equals(TradeChannelEnum.H5.getCode());

    }

    /**
     * @description: 判断是否为CRM渠道
     * @param code
     * @return boolean
     * @author: jin.wang03
     * @date: 2024/7/25 19:01
     * @since JDK 1.8
     */
    public static boolean isCrmTradeChannel(String code) {
        if (StringUtils.isEmpty(code)) {
            return false;
        }

        return code.equals(TradeChannelEnum.CRM_PC.getCode()) || code.equals(TradeChannelEnum.CRM_MOBILE.getCode());

    }


}
