package com.howbuy.dtms.manager.outservice.param.dto;

import com.howbuy.dtms.product.client.query.vo.FundTradeCalendarInfoVO;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:23:45+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class FundTradeCalendarInfoDTOConvertMapperImpl implements FundTradeCalendarInfoDTOConvertMapper {

    @Override
    public FundTradeCalendarInfoDTO convert(FundTradeCalendarInfoVO vo) {
        if ( vo == null ) {
            return null;
        }

        FundTradeCalendarInfoDTO fundTradeCalendarInfoDTO = new FundTradeCalendarInfoDTO();

        fundTradeCalendarInfoDTO.setAdvanceEndDt( vo.getAdvanceEndDt() );
        fundTradeCalendarInfoDTO.setAdvanceEndTm( vo.getAdvanceEndTm() );
        fundTradeCalendarInfoDTO.setAdvanceStartDt( vo.getAdvanceStartDt() );
        fundTradeCalendarInfoDTO.setAdvanceStartTm( vo.getAdvanceStartTm() );
        fundTradeCalendarInfoDTO.setBusiCode( vo.getBusiCode() );
        fundTradeCalendarInfoDTO.setCurrentLimit( vo.getCurrentLimit() );
        fundTradeCalendarInfoDTO.setCurrentNum( vo.getCurrentNum() );
        fundTradeCalendarInfoDTO.setCurrentPayInRatio( vo.getCurrentPayInRatio() );
        fundTradeCalendarInfoDTO.setFirstPayInRatio( vo.getFirstPayInRatio() );
        fundTradeCalendarInfoDTO.setFundCode( vo.getFundCode() );
        fundTradeCalendarInfoDTO.setOpenEndDt( vo.getOpenEndDt() );
        fundTradeCalendarInfoDTO.setOpenStartDt( vo.getOpenStartDt() );
        fundTradeCalendarInfoDTO.setPaymentDeadlineDt( vo.getPaymentDeadlineDt() );
        fundTradeCalendarInfoDTO.setPaymentDeadlineTime( vo.getPaymentDeadlineTime() );

        return fundTradeCalendarInfoDTO;
    }
}
