<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.customize.BpSupplementalAgreementConfigDtlCustomizeMapper">

    <select id="queryDtlListByAgreementId" resultMap="com.howbuy.dtms.manager.dao.mapper.BpSupplementalAgreementConfigDtlMapper.BaseResultMap">
        select
            <include refid="com.howbuy.dtms.manager.dao.mapper.BpSupplementalAgreementConfigDtlMapper.Base_Column_List"/>
        from bp_supplemental_agreement_config_dtl
        where agr_id = #{agrId,jdbcType=BIGINT}
        and rec_stat = '1'
        order by create_time desc
    </select>

    <select id="queryPageDtlByAgreementIdAndHkCustNo" resultMap="com.howbuy.dtms.manager.dao.mapper.BpSupplementalAgreementConfigDtlMapper.BaseResultMap">
        select
            <include refid="com.howbuy.dtms.manager.dao.mapper.BpSupplementalAgreementConfigDtlMapper.Base_Column_List"/>
        from bp_supplemental_agreement_config_dtl
        where agr_id = #{agrId,jdbcType=BIGINT}
        <if test="hkCustNo != null and hkCustNo != ''">
            and hk_cust_no = #{hkCustNo,jdbcType=VARCHAR}
        </if>
        <if test="needSignAgreement != null and needSignAgreement != ''">
            and need_sign_agreement = #{needSignAgreement,jdbcType=VARCHAR}
        </if>

        and rec_stat = '1'
        limit 1
    </select>

    <select id="batchQueryPageByAgreementIdAndHkCustNos" resultType="com.howbuy.dtms.manager.dao.po.BpSupplementalAgreementConfigDtlPO">
        SELECT 
            d.id,
            d.agr_id as agrId,
            d.fund_code as fundCode,
            d.hk_cust_no as hkCustNo,
            d.cust_chinese_name as custChineseName,
            d.id_no_mask as idNoMask,
            d.id_no_cipher as idNoCipher,
            d.id_no_digest as idNoDigest,
            d.need_sign_agreement as needSignAgreement,
            d.rec_stat as recStat,
            d.create_time as createTime,
            d.creator,
            d.update_time as updateTime,
            d.modifier
        FROM 
            bp_supplemental_agreement_config_dtl d
        WHERE 
            d.agr_id = #{agrId,jdbcType=BIGINT}
            AND d.rec_stat = '1'
            <if test="needSignAgreement != null and needSignAgreement != ''">
                AND d.need_sign_agreement = #{needSignAgreement,jdbcType=CHAR}
            </if>
            <if test="hkCustNoList != null and hkCustNoList.size() > 0">
                AND d.hk_cust_no IN
                <foreach collection="hkCustNoList" item="hkCustNo" open="(" separator="," close=")">
                    #{hkCustNo,jdbcType=VARCHAR}
                </foreach>
            </if>
         order by d.create_time desc
    </select>

</mapper> 