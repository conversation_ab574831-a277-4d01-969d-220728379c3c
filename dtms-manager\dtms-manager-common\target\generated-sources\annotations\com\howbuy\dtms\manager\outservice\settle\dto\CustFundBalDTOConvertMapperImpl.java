package com.howbuy.dtms.manager.outservice.settle.dto;

import com.howbuy.dtms.settle.client.facade.query.vo.CustFundBalVO;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:23:41+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class CustFundBalDTOConvertMapperImpl implements CustFundBalDTOConvertMapper {

    @Override
    public CustFundBalDTO convert(CustFundBalVO vo) {
        if ( vo == null ) {
            return null;
        }

        CustFundBalDTO custFundBalDTO = new CustFundBalDTO();

        custFundBalDTO.setAvailVol( vo.getAvailVol() );
        custFundBalDTO.setBalanceFactor( vo.getBalanceFactor() );
        custFundBalDTO.setBalanceVol( vo.getBalanceVol() );
        custFundBalDTO.setCreateTime( vo.getCreateTime() );
        custFundBalDTO.setFrznVol( vo.getFrznVol() );
        custFundBalDTO.setFundCode( vo.getFundCode() );
        custFundBalDTO.setFundManCode( vo.getFundManCode() );
        custFundBalDTO.setFundTxAcctNo( vo.getFundTxAcctNo() );
        custFundBalDTO.setHkCustNo( vo.getHkCustNo() );
        custFundBalDTO.setInvstType( vo.getInvstType() );
        custFundBalDTO.setUpdateDt( vo.getUpdateDt() );
        custFundBalDTO.setUpdateTime( vo.getUpdateTime() );

        return custFundBalDTO;
    }
}
