package com.howbuy.dtms.manager.console.convertmapper.hwdealorderdtl;

import com.howbuy.dtms.manager.console.vo.hwdealorderdtl.HwDealOrderDtlExportVO;
import com.howbuy.dtms.manager.console.vo.hwdealorderdtl.HwDealOrderDtlVO;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T16:12:59+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class HwDealOrderDtlExportVOConvertMapperImpl implements HwDealOrderDtlExportVOConvertMapper {

    @Override
    public HwDealOrderDtlExportVO convert(HwDealOrderDtlVO vo) {
        if ( vo == null ) {
            return null;
        }

        HwDealOrderDtlExportVO.HwDealOrderDtlExportVOBuilder hwDealOrderDtlExportVO = HwDealOrderDtlExportVO.builder();

        hwDealOrderDtlExportVO.ackStatus( vo.getAckStatus() );
        hwDealOrderDtlExportVO.appAmt( vo.getAppAmt() );
        hwDealOrderDtlExportVO.appDt( vo.getAppDt() );
        hwDealOrderDtlExportVO.appStatus( vo.getAppStatus() );
        hwDealOrderDtlExportVO.appTm( vo.getAppTm() );
        hwDealOrderDtlExportVO.appVol( vo.getAppVol() );
        hwDealOrderDtlExportVO.cpAcctNo( vo.getCpAcctNo() );
        hwDealOrderDtlExportVO.currency( vo.getCurrency() );
        hwDealOrderDtlExportVO.custChineseName( vo.getCustChineseName() );
        hwDealOrderDtlExportVO.dealDtlNo( vo.getDealDtlNo() );
        hwDealOrderDtlExportVO.dealNo( vo.getDealNo() );
        hwDealOrderDtlExportVO.discountAmt( vo.getDiscountAmt() );
        hwDealOrderDtlExportVO.discountRate( vo.getDiscountRate() );
        hwDealOrderDtlExportVO.discountType( vo.getDiscountType() );
        hwDealOrderDtlExportVO.estimateFee( vo.getEstimateFee() );
        hwDealOrderDtlExportVO.feeRate( vo.getFeeRate() );
        hwDealOrderDtlExportVO.fundAbbr( vo.getFundAbbr() );
        hwDealOrderDtlExportVO.fundCode( vo.getFundCode() );
        hwDealOrderDtlExportVO.fundDivMode( vo.getFundDivMode() );
        hwDealOrderDtlExportVO.fundManCode( vo.getFundManCode() );
        hwDealOrderDtlExportVO.hkCustNo( vo.getHkCustNo() );
        hwDealOrderDtlExportVO.id( vo.getId() );
        hwDealOrderDtlExportVO.idNoMask( vo.getIdNoMask() );
        hwDealOrderDtlExportVO.mainFundCode( vo.getMainFundCode() );
        hwDealOrderDtlExportVO.middleBusiCode( vo.getMiddleBusiCode() );
        hwDealOrderDtlExportVO.netAppAmt( vo.getNetAppAmt() );
        hwDealOrderDtlExportVO.openDt( vo.getOpenDt() );
        hwDealOrderDtlExportVO.payStatus( vo.getPayStatus() );
        hwDealOrderDtlExportVO.paymentTypeList( vo.getPaymentTypeList() );
        hwDealOrderDtlExportVO.preSubmitTaDt( vo.getPreSubmitTaDt() );
        hwDealOrderDtlExportVO.preSubmitTaTm( vo.getPreSubmitTaTm() );
        hwDealOrderDtlExportVO.redeemDirectionList( vo.getRedeemDirectionList() );
        hwDealOrderDtlExportVO.redeemType( vo.getRedeemType() );
        hwDealOrderDtlExportVO.subAmt( vo.getSubAmt() );
        hwDealOrderDtlExportVO.submitStatus( vo.getSubmitStatus() );

        return hwDealOrderDtlExportVO.build();
    }
}
