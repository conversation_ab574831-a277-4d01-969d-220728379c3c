<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.HwSubmitDealOrderMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwSubmitDealOrderPO">
        <!--@Table hw_submit_deal_order-->
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="submitDealNo" column="submit_deal_no" jdbcType="BIGINT"/>
        <result property="dealDtlNo" column="deal_dtl_no" jdbcType="BIGINT"/>
        <result property="dealNo" column="deal_no" jdbcType="BIGINT"/>
        <result property="hkCustNo" column="hk_cust_no" jdbcType="VARCHAR"/>
        <result property="invstType" column="invst_type" jdbcType="VARCHAR"/>
        <result property="cpAcctNo" column="cp_acct_no" jdbcType="VARCHAR"/>
        <result property="fundCode" column="fund_code" jdbcType="VARCHAR"/>
        <result property="mainFundCode" column="main_fund_code" jdbcType="VARCHAR"/>
        <result property="fundCategory" column="fund_category" jdbcType="VARCHAR"/>
        <result property="fundRiskLevel" column="fund_risk_level" jdbcType="VARCHAR"/>
        <result property="redeemType" column="redeem_type" jdbcType="VARCHAR"/>
        <result property="redeemDirectionList" column="redeem_direction_list" jdbcType="VARCHAR"/>
        <result property="middleBusiCode" column="middle_busi_code" jdbcType="VARCHAR"/>
        <result property="busiCode" column="busi_code" jdbcType="VARCHAR"/>
        <result property="appDt" column="app_dt" jdbcType="VARCHAR"/>
        <result property="appTm" column="app_tm" jdbcType="VARCHAR"/>
        <result property="appAmt" column="app_amt" jdbcType="NUMERIC"/>
        <result property="netAppAmt" column="net_app_amt" jdbcType="NUMERIC"/>
        <result property="appVol" column="app_vol" jdbcType="NUMERIC"/>
        <result property="discountRate" column="discount_rate" jdbcType="NUMERIC"/>
        <result property="feeRate" column="fee_rate" jdbcType="NUMERIC"/>
        <result property="feeCalMode" column="fee_cal_mode" jdbcType="VARCHAR"/>
        <result property="estimateFee" column="estimate_fee" jdbcType="NUMERIC"/>
        <result property="fee" column="fee" jdbcType="NUMERIC"/>
        <result property="ackAmt" column="ack_amt" jdbcType="NUMERIC"/>
        <result property="ackVol" column="ack_vol" jdbcType="NUMERIC"/>
        <result property="ackNav" column="ack_nav" jdbcType="NUMERIC"/>
        <result property="ackNavDt" column="ack_nav_dt" jdbcType="VARCHAR"/>
        <result property="currency" column="currency" jdbcType="VARCHAR"/>
        <result property="appStatus" column="app_status" jdbcType="VARCHAR"/>
        <result property="ackStatus" column="ack_status" jdbcType="VARCHAR"/>
        <result property="submitStatus" column="submit_status" jdbcType="VARCHAR"/>
        <result property="fundDivMode" column="fund_div_mode" jdbcType="VARCHAR"/>
        <result property="openDt" column="open_dt" jdbcType="VARCHAR"/>
        <result property="taTradeDt" column="ta_trade_dt" jdbcType="VARCHAR"/>
        <result property="ackDt" column="ack_dt" jdbcType="VARCHAR"/>
        <result property="taAckNo" column="ta_ack_no" jdbcType="VARCHAR"/>
        <result property="extOption" column="ext_option" jdbcType="VARCHAR"/>
        <result property="extControlType" column="ext_control_type" jdbcType="VARCHAR"/>
        <result property="extControlNum" column="ext_control_num" jdbcType="VARCHAR"/>
        <result property="fundTxAcctNo" column="fund_tx_acct_no" jdbcType="VARCHAR"/>
        <result property="intoFundCode" column="into_fund_code" jdbcType="VARCHAR"/>
        <result property="intoCurrency" column="into_currency" jdbcType="VARCHAR"/>
        <result property="intoAckAmt" column="into_ack_amt" jdbcType="NUMERIC"/>
        <result property="intoAckVol" column="into_ack_vol" jdbcType="NUMERIC"/>
        <result property="intoAckNav" column="into_ack_nav" jdbcType="NUMERIC"/>
        <result property="intoAckNavDt" column="into_ack_nav_dt" jdbcType="VARCHAR"/>
        <result property="intoFundTxAcctNo" column="into_fund_tx_acct_no" jdbcType="VARCHAR"/>
        <result property="relationalDealDtlNo" column="relational_deal_dtl_no" jdbcType="BIGINT"/>
        <result property="preSubmitTaDt" column="pre_submit_ta_dt" jdbcType="VARCHAR"/>
        <result property="preSubmitTaTm" column="pre_submit_ta_tm" jdbcType="VARCHAR"/>
        <result property="fundManCode" column="fund_man_code" jdbcType="VARCHAR"/>
        <result property="serialNumber" column="serial_number" jdbcType="VARCHAR"/>
        <result property="shareRegDt" column="share_reg_dt" jdbcType="VARCHAR"/>
        <result property="volDtlNo" column="vol_dtl_no" jdbcType="VARCHAR"/>
        <result property="ackRecCode" column="ack_rec_code" jdbcType="VARCHAR"/>
        <result property="ackRecMsg" column="ack_rec_msg" jdbcType="VARCHAR"/>
        <result property="createTimestamp" column="create_timestamp" jdbcType="TIMESTAMP"/>
        <result property="updateTimestamp" column="update_timestamp" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        submit_deal_no, 
        deal_dtl_no, 
        deal_no, 
        hk_cust_no, 
        invst_type, 
        cp_acct_no, 
        fund_code, 
        main_fund_code, 
        fund_category, 
        fund_risk_level, 
        redeem_type, 
        redeem_direction_list, 
        middle_busi_code, 
        busi_code, 
        app_dt, 
        app_tm, 
        app_amt, 
        net_app_amt, 
        app_vol, 
        discount_rate, 
        fee_rate, 
        fee_cal_mode, 
        estimate_fee, 
        fee, 
        ack_amt, 
        ack_vol, 
        ack_nav, 
        ack_nav_dt, 
        currency, 
        app_status, 
        ack_status, 
        submit_status, 
        fund_div_mode, 
        open_dt, 
        ta_trade_dt, 
        ack_dt, 
        ta_ack_no, 
        ext_option, 
        ext_control_type, 
        ext_control_num, 
        fund_tx_acct_no, 
        into_fund_code, 
        into_currency, 
        into_ack_amt, 
        into_ack_vol, 
        into_ack_nav, 
        into_ack_nav_dt, 
        into_fund_tx_acct_no, 
        relational_deal_dtl_no, 
        pre_submit_ta_dt, 
        pre_submit_ta_tm, 
        fund_man_code, 
        serial_number, 
        share_reg_dt, 
        vol_dtl_no, 
        ack_rec_code, 
        ack_rec_msg, 
        create_timestamp, 
        update_timestamp
    </sql>

    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_submit_deal_order
        where id = #{id}
    </select>

    <!-- 查询符合条件的数据 -->
    <select id="selectBySelective" parameterType="com.howbuy.dtms.manager.dao.po.HwSubmitDealOrderPO"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_submit_deal_order
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="submitDealNo != null">
                and submit_deal_no = #{submitDealNo}
            </if>
            <if test="dealDtlNo != null">
                and deal_dtl_no = #{dealDtlNo}
            </if>
            <if test="dealNo != null">
                and deal_no = #{dealNo}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and hk_cust_no = #{hkCustNo}
            </if>
            <if test="invstType != null and invstType != ''">
                and invst_type = #{invstType}
            </if>
            <if test="cpAcctNo != null and cpAcctNo != ''">
                and cp_acct_no = #{cpAcctNo}
            </if>
            <if test="fundCode != null and fundCode != ''">
                and fund_code = #{fundCode}
            </if>
            <if test="mainFundCode != null and mainFundCode != ''">
                and main_fund_code = #{mainFundCode}
            </if>
            <if test="fundCategory != null and fundCategory != ''">
                and fund_category = #{fundCategory}
            </if>
            <if test="fundRiskLevel != null and fundRiskLevel != ''">
                and fund_risk_level = #{fundRiskLevel}
            </if>
            <if test="redeemType != null and redeemType != ''">
                and redeem_type = #{redeemType}
            </if>
            <if test="redeemDirectionList != null and redeemDirectionList != ''">
                and redeem_direction_list = #{redeemDirectionList}
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                and middle_busi_code = #{middleBusiCode}
            </if>
            <if test="busiCode != null and busiCode != ''">
                and busi_code = #{busiCode}
            </if>
            <if test="appDt != null and appDt != ''">
                and app_dt = #{appDt}
            </if>
            <if test="appTm != null and appTm != ''">
                and app_tm = #{appTm}
            </if>
            <if test="appAmt != null">
                and app_amt = #{appAmt}
            </if>
            <if test="netAppAmt != null">
                and net_app_amt = #{netAppAmt}
            </if>
            <if test="appVol != null">
                and app_vol = #{appVol}
            </if>
            <if test="discountRate != null">
                and discount_rate = #{discountRate}
            </if>
            <if test="feeRate != null">
                and fee_rate = #{feeRate}
            </if>
            <if test="feeCalMode != null and feeCalMode != ''">
                and fee_cal_mode = #{feeCalMode}
            </if>
            <if test="estimateFee != null">
                and estimate_fee = #{estimateFee}
            </if>
            <if test="fee != null">
                and fee = #{fee}
            </if>
            <if test="ackAmt != null">
                and ack_amt = #{ackAmt}
            </if>
            <if test="ackVol != null">
                and ack_vol = #{ackVol}
            </if>
            <if test="ackNav != null">
                and ack_nav = #{ackNav}
            </if>
            <if test="ackNavDt != null and ackNavDt != ''">
                and ack_nav_dt = #{ackNavDt}
            </if>
            <if test="currency != null and currency != ''">
                and currency = #{currency}
            </if>
            <if test="appStatus != null and appStatus != ''">
                and app_status = #{appStatus}
            </if>
            <if test="ackStatus != null and ackStatus != ''">
                and ack_status = #{ackStatus}
            </if>
            <if test="submitStatus != null and submitStatus != ''">
                and submit_status = #{submitStatus}
            </if>
            <if test="fundDivMode != null and fundDivMode != ''">
                and fund_div_mode = #{fundDivMode}
            </if>
            <if test="openDt != null and openDt != ''">
                and open_dt = #{openDt}
            </if>
            <if test="taTradeDt != null and taTradeDt != ''">
                and ta_trade_dt = #{taTradeDt}
            </if>
            <if test="ackDt != null and ackDt != ''">
                and ack_dt = #{ackDt}
            </if>
            <if test="taAckNo != null and taAckNo != ''">
                and ta_ack_no = #{taAckNo}
            </if>
            <if test="extOption != null and extOption != ''">
                and ext_option = #{extOption}
            </if>
            <if test="extControlType != null and extControlType != ''">
                and ext_control_type = #{extControlType}
            </if>
            <if test="extControlNum != null and extControlNum != ''">
                and ext_control_num = #{extControlNum}
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                and fund_tx_acct_no = #{fundTxAcctNo}
            </if>
            <if test="intoFundCode != null and intoFundCode != ''">
                and into_fund_code = #{intoFundCode}
            </if>
            <if test="intoCurrency != null and intoCurrency != ''">
                and into_currency = #{intoCurrency}
            </if>
            <if test="intoAckAmt != null">
                and into_ack_amt = #{intoAckAmt}
            </if>
            <if test="intoAckVol != null">
                and into_ack_vol = #{intoAckVol}
            </if>
            <if test="intoAckNav != null">
                and into_ack_nav = #{intoAckNav}
            </if>
            <if test="intoAckNavDt != null and intoAckNavDt != ''">
                and into_ack_nav_dt = #{intoAckNavDt}
            </if>
            <if test="intoFundTxAcctNo != null and intoFundTxAcctNo != ''">
                and into_fund_tx_acct_no = #{intoFundTxAcctNo}
            </if>
            <if test="relationalDealDtlNo != null">
                and relational_deal_dtl_no = #{relationalDealDtlNo}
            </if>
            <if test="preSubmitTaDt != null and preSubmitTaDt != ''">
                and pre_submit_ta_dt = #{preSubmitTaDt}
            </if>
            <if test="preSubmitTaTm != null and preSubmitTaTm != ''">
                and pre_submit_ta_tm = #{preSubmitTaTm}
            </if>
            <if test="fundManCode != null and fundManCode != ''">
                and fund_man_code = #{fundManCode}
            </if>
            <if test="serialNumber != null and serialNumber != ''">
                and serial_number = #{serialNumber}
            </if>
            <if test="shareRegDt != null and shareRegDt != ''">
                and share_reg_dt = #{shareRegDt}
            </if>
            <if test="volDtlNo != null and volDtlNo != ''">
                and vol_dtl_no = #{volDtlNo}
            </if>
            <if test="ackRecCode != null and ackRecCode != ''">
                and ack_rec_code = #{ackRecCode}
            </if>
            <if test="ackRecMsg != null and ackRecMsg != ''">
                and ack_rec_msg = #{ackRecMsg}
            </if>
            <if test="createTimestamp != null">
                and create_timestamp = #{createTimestamp}
            </if>
            <if test="updateTimestamp != null">
                and update_timestamp = #{updateTimestamp}
            </if>
        </where>
        order by id
    </select>


    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(*)
        from hw_submit_deal_order
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="submitDealNo != null">
                and submit_deal_no = #{submitDealNo}
            </if>
            <if test="dealDtlNo != null">
                and deal_dtl_no = #{dealDtlNo}
            </if>
            <if test="dealNo != null">
                and deal_no = #{dealNo}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and hk_cust_no = #{hkCustNo}
            </if>
            <if test="invstType != null and invstType != ''">
                and invst_type = #{invstType}
            </if>
            <if test="cpAcctNo != null and cpAcctNo != ''">
                and cp_acct_no = #{cpAcctNo}
            </if>
            <if test="fundCode != null and fundCode != ''">
                and fund_code = #{fundCode}
            </if>
            <if test="mainFundCode != null and mainFundCode != ''">
                and main_fund_code = #{mainFundCode}
            </if>
            <if test="fundCategory != null and fundCategory != ''">
                and fund_category = #{fundCategory}
            </if>
            <if test="fundRiskLevel != null and fundRiskLevel != ''">
                and fund_risk_level = #{fundRiskLevel}
            </if>
            <if test="redeemType != null and redeemType != ''">
                and redeem_type = #{redeemType}
            </if>
            <if test="redeemDirectionList != null and redeemDirectionList != ''">
                and redeem_direction_list = #{redeemDirectionList}
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                and middle_busi_code = #{middleBusiCode}
            </if>
            <if test="busiCode != null and busiCode != ''">
                and busi_code = #{busiCode}
            </if>
            <if test="appDt != null and appDt != ''">
                and app_dt = #{appDt}
            </if>
            <if test="appTm != null and appTm != ''">
                and app_tm = #{appTm}
            </if>
            <if test="appAmt != null">
                and app_amt = #{appAmt}
            </if>
            <if test="netAppAmt != null">
                and net_app_amt = #{netAppAmt}
            </if>
            <if test="appVol != null">
                and app_vol = #{appVol}
            </if>
            <if test="discountRate != null">
                and discount_rate = #{discountRate}
            </if>
            <if test="feeRate != null">
                and fee_rate = #{feeRate}
            </if>
            <if test="feeCalMode != null and feeCalMode != ''">
                and fee_cal_mode = #{feeCalMode}
            </if>
            <if test="estimateFee != null">
                and estimate_fee = #{estimateFee}
            </if>
            <if test="fee != null">
                and fee = #{fee}
            </if>
            <if test="ackAmt != null">
                and ack_amt = #{ackAmt}
            </if>
            <if test="ackVol != null">
                and ack_vol = #{ackVol}
            </if>
            <if test="ackNav != null">
                and ack_nav = #{ackNav}
            </if>
            <if test="ackNavDt != null and ackNavDt != ''">
                and ack_nav_dt = #{ackNavDt}
            </if>
            <if test="currency != null and currency != ''">
                and currency = #{currency}
            </if>
            <if test="appStatus != null and appStatus != ''">
                and app_status = #{appStatus}
            </if>
            <if test="ackStatus != null and ackStatus != ''">
                and ack_status = #{ackStatus}
            </if>
            <if test="submitStatus != null and submitStatus != ''">
                and submit_status = #{submitStatus}
            </if>
            <if test="fundDivMode != null and fundDivMode != ''">
                and fund_div_mode = #{fundDivMode}
            </if>
            <if test="openDt != null and openDt != ''">
                and open_dt = #{openDt}
            </if>
            <if test="taTradeDt != null and taTradeDt != ''">
                and ta_trade_dt = #{taTradeDt}
            </if>
            <if test="ackDt != null and ackDt != ''">
                and ack_dt = #{ackDt}
            </if>
            <if test="taAckNo != null and taAckNo != ''">
                and ta_ack_no = #{taAckNo}
            </if>
            <if test="extOption != null and extOption != ''">
                and ext_option = #{extOption}
            </if>
            <if test="extControlType != null and extControlType != ''">
                and ext_control_type = #{extControlType}
            </if>
            <if test="extControlNum != null and extControlNum != ''">
                and ext_control_num = #{extControlNum}
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                and fund_tx_acct_no = #{fundTxAcctNo}
            </if>
            <if test="intoFundCode != null and intoFundCode != ''">
                and into_fund_code = #{intoFundCode}
            </if>
            <if test="intoCurrency != null and intoCurrency != ''">
                and into_currency = #{intoCurrency}
            </if>
            <if test="intoAckAmt != null">
                and into_ack_amt = #{intoAckAmt}
            </if>
            <if test="intoAckVol != null">
                and into_ack_vol = #{intoAckVol}
            </if>
            <if test="intoAckNav != null">
                and into_ack_nav = #{intoAckNav}
            </if>
            <if test="intoAckNavDt != null and intoAckNavDt != ''">
                and into_ack_nav_dt = #{intoAckNavDt}
            </if>
            <if test="intoFundTxAcctNo != null and intoFundTxAcctNo != ''">
                and into_fund_tx_acct_no = #{intoFundTxAcctNo}
            </if>
            <if test="relationalDealDtlNo != null">
                and relational_deal_dtl_no = #{relationalDealDtlNo}
            </if>
            <if test="preSubmitTaDt != null and preSubmitTaDt != ''">
                and pre_submit_ta_dt = #{preSubmitTaDt}
            </if>
            <if test="preSubmitTaTm != null and preSubmitTaTm != ''">
                and pre_submit_ta_tm = #{preSubmitTaTm}
            </if>
            <if test="fundManCode != null and fundManCode != ''">
                and fund_man_code = #{fundManCode}
            </if>
            <if test="serialNumber != null and serialNumber != ''">
                and serial_number = #{serialNumber}
            </if>
            <if test="shareRegDt != null and shareRegDt != ''">
                and share_reg_dt = #{shareRegDt}
            </if>
            <if test="volDtlNo != null and volDtlNo != ''">
                and vol_dtl_no = #{volDtlNo}
            </if>
            <if test="ackRecCode != null and ackRecCode != ''">
                and ack_rec_code = #{ackRecCode}
            </if>
            <if test="ackRecMsg != null and ackRecMsg != ''">
                and ack_rec_msg = #{ackRecMsg}
            </if>
            <if test="createTimestamp != null">
                and create_timestamp = #{createTimestamp}
            </if>
            <if test="updateTimestamp != null">
                and update_timestamp = #{updateTimestamp}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into hw_submit_deal_order(submit_deal_no, deal_dtl_no, deal_no, hk_cust_no, invst_type, cp_acct_no,
                                         fund_code, main_fund_code, fund_category, fund_risk_level, redeem_type,
                                         redeem_direction_list, middle_busi_code, busi_code, app_dt, app_tm, app_amt,
                                         net_app_amt, app_vol, discount_rate, fee_rate, fee_cal_mode, estimate_fee, fee,
                                         ack_amt, ack_vol, ack_nav, ack_nav_dt, currency, app_status, ack_status,
                                         submit_status, fund_div_mode, open_dt, ta_trade_dt, ack_dt, ta_ack_no,
                                         ext_option, ext_control_type, ext_control_num, fund_tx_acct_no, into_fund_code,
                                         into_currency, into_ack_amt, into_ack_vol, into_ack_nav, into_ack_nav_dt,
                                         into_fund_tx_acct_no, relational_deal_dtl_no, pre_submit_ta_dt,
                                         pre_submit_ta_tm, fund_man_code, serial_number, share_reg_dt, vol_dtl_no,
                                         ack_rec_code, ack_rec_msg, create_timestamp, update_timestamp)
        values (#{submitDealNo}, #{dealDtlNo}, #{dealNo}, #{hkCustNo}, #{invstType}, #{cpAcctNo}, #{fundCode},
                #{mainFundCode}, #{fundCategory}, #{fundRiskLevel}, #{redeemType}, #{redeemDirectionList},
                #{middleBusiCode}, #{busiCode}, #{appDt}, #{appTm}, #{appAmt}, #{netAppAmt}, #{appVol}, #{discountRate},
                #{feeRate}, #{feeCalMode}, #{estimateFee}, #{fee}, #{ackAmt}, #{ackVol}, #{ackNav}, #{ackNavDt},
                #{currency}, #{appStatus}, #{ackStatus}, #{submitStatus}, #{fundDivMode}, #{openDt}, #{taTradeDt},
                #{ackDt}, #{taAckNo}, #{extOption}, #{extControlType}, #{extControlNum}, #{fundTxAcctNo},
                #{intoFundCode}, #{intoCurrency}, #{intoAckAmt}, #{intoAckVol}, #{intoAckNav}, #{intoAckNavDt},
                #{intoFundTxAcctNo}, #{relationalDealDtlNo}, #{preSubmitTaDt}, #{preSubmitTaTm}, #{fundManCode},
                #{serialNumber}, #{shareRegDt}, #{volDtlNo}, #{ackRecCode}, #{ackRecMsg}, #{createTimestamp},
                #{updateTimestamp})
    </insert>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true">
        insert into hw_submit_deal_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="submitDealNo != null">
                submit_deal_no,
            </if>
            <if test="dealDtlNo != null">
                deal_dtl_no,
            </if>
            <if test="dealNo != null">
                deal_no,
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                hk_cust_no,
            </if>
            <if test="invstType != null and invstType != ''">
                invst_type,
            </if>
            <if test="cpAcctNo != null and cpAcctNo != ''">
                cp_acct_no,
            </if>
            <if test="fundCode != null and fundCode != ''">
                fund_code,
            </if>
            <if test="mainFundCode != null and mainFundCode != ''">
                main_fund_code,
            </if>
            <if test="fundCategory != null and fundCategory != ''">
                fund_category,
            </if>
            <if test="fundRiskLevel != null and fundRiskLevel != ''">
                fund_risk_level,
            </if>
            <if test="redeemType != null and redeemType != ''">
                redeem_type,
            </if>
            <if test="redeemDirectionList != null and redeemDirectionList != ''">
                redeem_direction_list,
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                middle_busi_code,
            </if>
            <if test="busiCode != null and busiCode != ''">
                busi_code,
            </if>
            <if test="appDt != null and appDt != ''">
                app_dt,
            </if>
            <if test="appTm != null and appTm != ''">
                app_tm,
            </if>
            <if test="appAmt != null">
                app_amt,
            </if>
            <if test="netAppAmt != null">
                net_app_amt,
            </if>
            <if test="appVol != null">
                app_vol,
            </if>
            <if test="discountRate != null">
                discount_rate,
            </if>
            <if test="feeRate != null">
                fee_rate,
            </if>
            <if test="feeCalMode != null and feeCalMode != ''">
                fee_cal_mode,
            </if>
            <if test="estimateFee != null">
                estimate_fee,
            </if>
            <if test="fee != null">
                fee,
            </if>
            <if test="ackAmt != null">
                ack_amt,
            </if>
            <if test="ackVol != null">
                ack_vol,
            </if>
            <if test="ackNav != null">
                ack_nav,
            </if>
            <if test="ackNavDt != null and ackNavDt != ''">
                ack_nav_dt,
            </if>
            <if test="currency != null and currency != ''">
                currency,
            </if>
            <if test="appStatus != null and appStatus != ''">
                app_status,
            </if>
            <if test="ackStatus != null and ackStatus != ''">
                ack_status,
            </if>
            <if test="submitStatus != null and submitStatus != ''">
                submit_status,
            </if>
            <if test="fundDivMode != null and fundDivMode != ''">
                fund_div_mode,
            </if>
            <if test="openDt != null and openDt != ''">
                open_dt,
            </if>
            <if test="taTradeDt != null and taTradeDt != ''">
                ta_trade_dt,
            </if>
            <if test="ackDt != null and ackDt != ''">
                ack_dt,
            </if>
            <if test="taAckNo != null and taAckNo != ''">
                ta_ack_no,
            </if>
            <if test="extOption != null and extOption != ''">
                ext_option,
            </if>
            <if test="extControlType != null and extControlType != ''">
                ext_control_type,
            </if>
            <if test="extControlNum != null and extControlNum != ''">
                ext_control_num,
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                fund_tx_acct_no,
            </if>
            <if test="intoFundCode != null and intoFundCode != ''">
                into_fund_code,
            </if>
            <if test="intoCurrency != null and intoCurrency != ''">
                into_currency,
            </if>
            <if test="intoAckAmt != null">
                into_ack_amt,
            </if>
            <if test="intoAckVol != null">
                into_ack_vol,
            </if>
            <if test="intoAckNav != null">
                into_ack_nav,
            </if>
            <if test="intoAckNavDt != null and intoAckNavDt != ''">
                into_ack_nav_dt,
            </if>
            <if test="intoFundTxAcctNo != null and intoFundTxAcctNo != ''">
                into_fund_tx_acct_no,
            </if>
            <if test="relationalDealDtlNo != null">
                relational_deal_dtl_no,
            </if>
            <if test="preSubmitTaDt != null and preSubmitTaDt != ''">
                pre_submit_ta_dt,
            </if>
            <if test="preSubmitTaTm != null and preSubmitTaTm != ''">
                pre_submit_ta_tm,
            </if>
            <if test="fundManCode != null and fundManCode != ''">
                fund_man_code,
            </if>
            <if test="serialNumber != null and serialNumber != ''">
                serial_number,
            </if>
            <if test="shareRegDt != null and shareRegDt != ''">
                share_reg_dt,
            </if>
            <if test="volDtlNo != null and volDtlNo != ''">
                vol_dtl_no,
            </if>
            <if test="ackRecCode != null and ackRecCode != ''">
                ack_rec_code,
            </if>
            <if test="ackRecMsg != null and ackRecMsg != ''">
                ack_rec_msg,
            </if>
            <if test="createTimestamp != null">
                create_timestamp,
            </if>
            <if test="updateTimestamp != null">
                update_timestamp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="submitDealNo != null">
                #{submitDealNo},
            </if>
            <if test="dealDtlNo != null">
                #{dealDtlNo},
            </if>
            <if test="dealNo != null">
                #{dealNo},
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                #{hkCustNo},
            </if>
            <if test="invstType != null and invstType != ''">
                #{invstType},
            </if>
            <if test="cpAcctNo != null and cpAcctNo != ''">
                #{cpAcctNo},
            </if>
            <if test="fundCode != null and fundCode != ''">
                #{fundCode},
            </if>
            <if test="mainFundCode != null and mainFundCode != ''">
                #{mainFundCode},
            </if>
            <if test="fundCategory != null and fundCategory != ''">
                #{fundCategory},
            </if>
            <if test="fundRiskLevel != null and fundRiskLevel != ''">
                #{fundRiskLevel},
            </if>
            <if test="redeemType != null and redeemType != ''">
                #{redeemType},
            </if>
            <if test="redeemDirectionList != null and redeemDirectionList != ''">
                #{redeemDirectionList},
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                #{middleBusiCode},
            </if>
            <if test="busiCode != null and busiCode != ''">
                #{busiCode},
            </if>
            <if test="appDt != null and appDt != ''">
                #{appDt},
            </if>
            <if test="appTm != null and appTm != ''">
                #{appTm},
            </if>
            <if test="appAmt != null">
                #{appAmt},
            </if>
            <if test="netAppAmt != null">
                #{netAppAmt},
            </if>
            <if test="appVol != null">
                #{appVol},
            </if>
            <if test="discountRate != null">
                #{discountRate},
            </if>
            <if test="feeRate != null">
                #{feeRate},
            </if>
            <if test="feeCalMode != null and feeCalMode != ''">
                #{feeCalMode},
            </if>
            <if test="estimateFee != null">
                #{estimateFee},
            </if>
            <if test="fee != null">
                #{fee},
            </if>
            <if test="ackAmt != null">
                #{ackAmt},
            </if>
            <if test="ackVol != null">
                #{ackVol},
            </if>
            <if test="ackNav != null">
                #{ackNav},
            </if>
            <if test="ackNavDt != null and ackNavDt != ''">
                #{ackNavDt},
            </if>
            <if test="currency != null and currency != ''">
                #{currency},
            </if>
            <if test="appStatus != null and appStatus != ''">
                #{appStatus},
            </if>
            <if test="ackStatus != null and ackStatus != ''">
                #{ackStatus},
            </if>
            <if test="submitStatus != null and submitStatus != ''">
                #{submitStatus},
            </if>
            <if test="fundDivMode != null and fundDivMode != ''">
                #{fundDivMode},
            </if>
            <if test="openDt != null and openDt != ''">
                #{openDt},
            </if>
            <if test="taTradeDt != null and taTradeDt != ''">
                #{taTradeDt},
            </if>
            <if test="ackDt != null and ackDt != ''">
                #{ackDt},
            </if>
            <if test="taAckNo != null and taAckNo != ''">
                #{taAckNo},
            </if>
            <if test="extOption != null and extOption != ''">
                #{extOption},
            </if>
            <if test="extControlType != null and extControlType != ''">
                #{extControlType},
            </if>
            <if test="extControlNum != null and extControlNum != ''">
                #{extControlNum},
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                #{fundTxAcctNo},
            </if>
            <if test="intoFundCode != null and intoFundCode != ''">
                #{intoFundCode},
            </if>
            <if test="intoCurrency != null and intoCurrency != ''">
                #{intoCurrency},
            </if>
            <if test="intoAckAmt != null">
                #{intoAckAmt},
            </if>
            <if test="intoAckVol != null">
                #{intoAckVol},
            </if>
            <if test="intoAckNav != null">
                #{intoAckNav},
            </if>
            <if test="intoAckNavDt != null and intoAckNavDt != ''">
                #{intoAckNavDt},
            </if>
            <if test="intoFundTxAcctNo != null and intoFundTxAcctNo != ''">
                #{intoFundTxAcctNo},
            </if>
            <if test="relationalDealDtlNo != null">
                #{relationalDealDtlNo},
            </if>
            <if test="preSubmitTaDt != null and preSubmitTaDt != ''">
                #{preSubmitTaDt},
            </if>
            <if test="preSubmitTaTm != null and preSubmitTaTm != ''">
                #{preSubmitTaTm},
            </if>
            <if test="fundManCode != null and fundManCode != ''">
                #{fundManCode},
            </if>
            <if test="serialNumber != null and serialNumber != ''">
                #{serialNumber},
            </if>
            <if test="shareRegDt != null and shareRegDt != ''">
                #{shareRegDt},
            </if>
            <if test="volDtlNo != null and volDtlNo != ''">
                #{volDtlNo},
            </if>
            <if test="ackRecCode != null and ackRecCode != ''">
                #{ackRecCode},
            </if>
            <if test="ackRecMsg != null and ackRecMsg != ''">
                #{ackRecMsg},
            </if>
            <if test="createTimestamp != null">
                #{createTimestamp},
            </if>
            <if test="updateTimestamp != null">
                #{updateTimestamp},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into hw_submit_deal_order(submit_deal_no, deal_dtl_no, deal_no, hk_cust_no, invst_type, cp_acct_no,
        fund_code, main_fund_code, fund_category, fund_risk_level, redeem_type, redeem_direction_list, middle_busi_code,
        busi_code, app_dt, app_tm, app_amt, net_app_amt, app_vol, discount_rate, fee_rate, fee_cal_mode, estimate_fee,
        fee, ack_amt, ack_vol, ack_nav, ack_nav_dt, currency, app_status, ack_status, submit_status, fund_div_mode,
        open_dt, ta_trade_dt, ack_dt, ta_ack_no, ext_option, ext_control_type, ext_control_num, fund_tx_acct_no,
        into_fund_code, into_currency, into_ack_amt, into_ack_vol, into_ack_nav, into_ack_nav_dt, into_fund_tx_acct_no,
        relational_deal_dtl_no, pre_submit_ta_dt, pre_submit_ta_tm, fund_man_code, serial_number, share_reg_dt,
        vol_dtl_no, ack_rec_code, ack_rec_msg, create_timestamp, update_timestamp)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.submitDealNo}, #{entity.dealDtlNo}, #{entity.dealNo}, #{entity.hkCustNo}, #{entity.invstType},
            #{entity.cpAcctNo}, #{entity.fundCode}, #{entity.mainFundCode}, #{entity.fundCategory},
            #{entity.fundRiskLevel}, #{entity.redeemType}, #{entity.redeemDirectionList}, #{entity.middleBusiCode},
            #{entity.busiCode}, #{entity.appDt}, #{entity.appTm}, #{entity.appAmt}, #{entity.netAppAmt},
            #{entity.appVol}, #{entity.discountRate}, #{entity.feeRate}, #{entity.feeCalMode}, #{entity.estimateFee},
            #{entity.fee}, #{entity.ackAmt}, #{entity.ackVol}, #{entity.ackNav}, #{entity.ackNavDt}, #{entity.currency},
            #{entity.appStatus}, #{entity.ackStatus}, #{entity.submitStatus}, #{entity.fundDivMode}, #{entity.openDt},
            #{entity.taTradeDt}, #{entity.ackDt}, #{entity.taAckNo}, #{entity.extOption}, #{entity.extControlType},
            #{entity.extControlNum}, #{entity.fundTxAcctNo}, #{entity.intoFundCode}, #{entity.intoCurrency},
            #{entity.intoAckAmt}, #{entity.intoAckVol}, #{entity.intoAckNav}, #{entity.intoAckNavDt},
            #{entity.intoFundTxAcctNo}, #{entity.relationalDealDtlNo}, #{entity.preSubmitTaDt}, #{entity.preSubmitTaTm},
            #{entity.fundManCode}, #{entity.serialNumber}, #{entity.shareRegDt}, #{entity.volDtlNo},
            #{entity.ackRecCode}, #{entity.ackRecMsg}, #{entity.createTimestamp}, #{entity.updateTimestamp})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update hw_submit_deal_order
        <set>
            <if test="submitDealNo != null">
                submit_deal_no = #{submitDealNo},
            </if>
            <if test="dealDtlNo != null">
                deal_dtl_no = #{dealDtlNo},
            </if>
            <if test="dealNo != null">
                deal_no = #{dealNo},
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                hk_cust_no = #{hkCustNo},
            </if>
            <if test="invstType != null and invstType != ''">
                invst_type = #{invstType},
            </if>
            <if test="cpAcctNo != null and cpAcctNo != ''">
                cp_acct_no = #{cpAcctNo},
            </if>
            <if test="fundCode != null and fundCode != ''">
                fund_code = #{fundCode},
            </if>
            <if test="mainFundCode != null and mainFundCode != ''">
                main_fund_code = #{mainFundCode},
            </if>
            <if test="fundCategory != null and fundCategory != ''">
                fund_category = #{fundCategory},
            </if>
            <if test="fundRiskLevel != null and fundRiskLevel != ''">
                fund_risk_level = #{fundRiskLevel},
            </if>
            <if test="redeemType != null and redeemType != ''">
                redeem_type = #{redeemType},
            </if>
            <if test="redeemDirectionList != null and redeemDirectionList != ''">
                redeem_direction_list = #{redeemDirectionList},
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                middle_busi_code = #{middleBusiCode},
            </if>
            <if test="busiCode != null and busiCode != ''">
                busi_code = #{busiCode},
            </if>
            <if test="appDt != null and appDt != ''">
                app_dt = #{appDt},
            </if>
            <if test="appTm != null and appTm != ''">
                app_tm = #{appTm},
            </if>
            <if test="appAmt != null">
                app_amt = #{appAmt},
            </if>
            <if test="netAppAmt != null">
                net_app_amt = #{netAppAmt},
            </if>
            <if test="appVol != null">
                app_vol = #{appVol},
            </if>
            <if test="discountRate != null">
                discount_rate = #{discountRate},
            </if>
            <if test="feeRate != null">
                fee_rate = #{feeRate},
            </if>
            <if test="feeCalMode != null and feeCalMode != ''">
                fee_cal_mode = #{feeCalMode},
            </if>
            <if test="estimateFee != null">
                estimate_fee = #{estimateFee},
            </if>
            <if test="fee != null">
                fee = #{fee},
            </if>
            <if test="ackAmt != null">
                ack_amt = #{ackAmt},
            </if>
            <if test="ackVol != null">
                ack_vol = #{ackVol},
            </if>
            <if test="ackNav != null">
                ack_nav = #{ackNav},
            </if>
            <if test="ackNavDt != null and ackNavDt != ''">
                ack_nav_dt = #{ackNavDt},
            </if>
            <if test="currency != null and currency != ''">
                currency = #{currency},
            </if>
            <if test="appStatus != null and appStatus != ''">
                app_status = #{appStatus},
            </if>
            <if test="ackStatus != null and ackStatus != ''">
                ack_status = #{ackStatus},
            </if>
            <if test="submitStatus != null and submitStatus != ''">
                submit_status = #{submitStatus},
            </if>
            <if test="fundDivMode != null and fundDivMode != ''">
                fund_div_mode = #{fundDivMode},
            </if>
            <if test="openDt != null and openDt != ''">
                open_dt = #{openDt},
            </if>
            <if test="taTradeDt != null and taTradeDt != ''">
                ta_trade_dt = #{taTradeDt},
            </if>
            <if test="ackDt != null and ackDt != ''">
                ack_dt = #{ackDt},
            </if>
            <if test="taAckNo != null and taAckNo != ''">
                ta_ack_no = #{taAckNo},
            </if>
            <if test="extOption != null and extOption != ''">
                ext_option = #{extOption},
            </if>
            <if test="extControlType != null and extControlType != ''">
                ext_control_type = #{extControlType},
            </if>
            <if test="extControlNum != null and extControlNum != ''">
                ext_control_num = #{extControlNum},
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                fund_tx_acct_no = #{fundTxAcctNo},
            </if>
            <if test="intoFundCode != null and intoFundCode != ''">
                into_fund_code = #{intoFundCode},
            </if>
            <if test="intoCurrency != null and intoCurrency != ''">
                into_currency = #{intoCurrency},
            </if>
            <if test="intoAckAmt != null">
                into_ack_amt = #{intoAckAmt},
            </if>
            <if test="intoAckVol != null">
                into_ack_vol = #{intoAckVol},
            </if>
            <if test="intoAckNav != null">
                into_ack_nav = #{intoAckNav},
            </if>
            <if test="intoAckNavDt != null and intoAckNavDt != ''">
                into_ack_nav_dt = #{intoAckNavDt},
            </if>
            <if test="intoFundTxAcctNo != null and intoFundTxAcctNo != ''">
                into_fund_tx_acct_no = #{intoFundTxAcctNo},
            </if>
            <if test="relationalDealDtlNo != null">
                relational_deal_dtl_no = #{relationalDealDtlNo},
            </if>
            <if test="preSubmitTaDt != null and preSubmitTaDt != ''">
                pre_submit_ta_dt = #{preSubmitTaDt},
            </if>
            <if test="preSubmitTaTm != null and preSubmitTaTm != ''">
                pre_submit_ta_tm = #{preSubmitTaTm},
            </if>
            <if test="fundManCode != null and fundManCode != ''">
                fund_man_code = #{fundManCode},
            </if>
            <if test="serialNumber != null and serialNumber != ''">
                serial_number = #{serialNumber},
            </if>
            <if test="shareRegDt != null and shareRegDt != ''">
                share_reg_dt = #{shareRegDt},
            </if>
            <if test="volDtlNo != null and volDtlNo != ''">
                vol_dtl_no = #{volDtlNo},
            </if>
            <if test="ackRecCode != null and ackRecCode != ''">
                ack_rec_code = #{ackRecCode},
            </if>
            <if test="ackRecMsg != null and ackRecMsg != ''">
                ack_rec_msg = #{ackRecMsg},
            </if>
            <if test="createTimestamp != null">
                create_timestamp = #{createTimestamp},
            </if>
            <if test="updateTimestamp != null">
                update_timestamp = #{updateTimestamp},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from hw_submit_deal_order
        where id = #{id}
    </delete>

    <!--分页查询，根据主键ID升序排序-->
    <select id="selectWithPage" parameterType="com.howbuy.dtms.manager.dao.query.HwSubmitDealOrderQuery"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_submit_deal_order
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="submitDealNo != null">
                and submit_deal_no = #{submitDealNo}
            </if>
            <if test="dealDtlNo != null">
                and deal_dtl_no = #{dealDtlNo}
            </if>
            <if test="dealNo != null">
                and deal_no = #{dealNo}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and hk_cust_no = #{hkCustNo}
            </if>
            <if test="invstType != null and invstType != ''">
                and invst_type = #{invstType}
            </if>
            <if test="cpAcctNo != null and cpAcctNo != ''">
                and cp_acct_no = #{cpAcctNo}
            </if>
            <if test="fundCode != null and fundCode != ''">
                and fund_code = #{fundCode}
            </if>
            <if test="mainFundCode != null and mainFundCode != ''">
                and main_fund_code = #{mainFundCode}
            </if>
            <if test="fundCategory != null and fundCategory != ''">
                and fund_category = #{fundCategory}
            </if>
            <if test="fundRiskLevel != null and fundRiskLevel != ''">
                and fund_risk_level = #{fundRiskLevel}
            </if>
            <if test="redeemType != null and redeemType != ''">
                and redeem_type = #{redeemType}
            </if>
            <if test="redeemDirectionList != null and redeemDirectionList != ''">
                and redeem_direction_list = #{redeemDirectionList}
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                and middle_busi_code = #{middleBusiCode}
            </if>
            <if test="busiCode != null and busiCode != ''">
                and busi_code = #{busiCode}
            </if>
            <if test="appDt != null and appDt != ''">
                and app_dt = #{appDt}
            </if>
            <if test="appTm != null and appTm != ''">
                and app_tm = #{appTm}
            </if>
            <if test="appAmt != null">
                and app_amt = #{appAmt}
            </if>
            <if test="netAppAmt != null">
                and net_app_amt = #{netAppAmt}
            </if>
            <if test="appVol != null">
                and app_vol = #{appVol}
            </if>
            <if test="discountRate != null">
                and discount_rate = #{discountRate}
            </if>
            <if test="feeRate != null">
                and fee_rate = #{feeRate}
            </if>
            <if test="feeCalMode != null and feeCalMode != ''">
                and fee_cal_mode = #{feeCalMode}
            </if>
            <if test="estimateFee != null">
                and estimate_fee = #{estimateFee}
            </if>
            <if test="fee != null">
                and fee = #{fee}
            </if>
            <if test="ackAmt != null">
                and ack_amt = #{ackAmt}
            </if>
            <if test="ackVol != null">
                and ack_vol = #{ackVol}
            </if>
            <if test="ackNav != null">
                and ack_nav = #{ackNav}
            </if>
            <if test="ackNavDt != null and ackNavDt != ''">
                and ack_nav_dt = #{ackNavDt}
            </if>
            <if test="currency != null and currency != ''">
                and currency = #{currency}
            </if>
            <if test="appStatus != null and appStatus != ''">
                and app_status = #{appStatus}
            </if>
            <if test="ackStatus != null and ackStatus != ''">
                and ack_status = #{ackStatus}
            </if>
            <if test="submitStatus != null and submitStatus != ''">
                and submit_status = #{submitStatus}
            </if>
            <if test="fundDivMode != null and fundDivMode != ''">
                and fund_div_mode = #{fundDivMode}
            </if>
            <if test="openDt != null and openDt != ''">
                and open_dt = #{openDt}
            </if>
            <if test="taTradeDt != null and taTradeDt != ''">
                and ta_trade_dt = #{taTradeDt}
            </if>
            <if test="ackDt != null and ackDt != ''">
                and ack_dt = #{ackDt}
            </if>
            <if test="taAckNo != null and taAckNo != ''">
                and ta_ack_no = #{taAckNo}
            </if>
            <if test="extOption != null and extOption != ''">
                and ext_option = #{extOption}
            </if>
            <if test="extControlType != null and extControlType != ''">
                and ext_control_type = #{extControlType}
            </if>
            <if test="extControlNum != null and extControlNum != ''">
                and ext_control_num = #{extControlNum}
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                and fund_tx_acct_no = #{fundTxAcctNo}
            </if>
            <if test="intoFundCode != null and intoFundCode != ''">
                and into_fund_code = #{intoFundCode}
            </if>
            <if test="intoCurrency != null and intoCurrency != ''">
                and into_currency = #{intoCurrency}
            </if>
            <if test="intoAckAmt != null">
                and into_ack_amt = #{intoAckAmt}
            </if>
            <if test="intoAckVol != null">
                and into_ack_vol = #{intoAckVol}
            </if>
            <if test="intoAckNav != null">
                and into_ack_nav = #{intoAckNav}
            </if>
            <if test="intoAckNavDt != null and intoAckNavDt != ''">
                and into_ack_nav_dt = #{intoAckNavDt}
            </if>
            <if test="intoFundTxAcctNo != null and intoFundTxAcctNo != ''">
                and into_fund_tx_acct_no = #{intoFundTxAcctNo}
            </if>
            <if test="relationalDealDtlNo != null">
                and relational_deal_dtl_no = #{relationalDealDtlNo}
            </if>
            <if test="preSubmitTaDt != null and preSubmitTaDt != ''">
                and pre_submit_ta_dt = #{preSubmitTaDt}
            </if>
            <if test="preSubmitTaTm != null and preSubmitTaTm != ''">
                and pre_submit_ta_tm = #{preSubmitTaTm}
            </if>
            <if test="fundManCode != null and fundManCode != ''">
                and fund_man_code = #{fundManCode}
            </if>
            <if test="serialNumber != null and serialNumber != ''">
                and serial_number = #{serialNumber}
            </if>
            <if test="shareRegDt != null and shareRegDt != ''">
                and share_reg_dt = #{shareRegDt}
            </if>
            <if test="volDtlNo != null and volDtlNo != ''">
                and vol_dtl_no = #{volDtlNo}
            </if>
            <if test="ackRecCode != null and ackRecCode != ''">
                and ack_rec_code = #{ackRecCode}
            </if>
            <if test="ackRecMsg != null and ackRecMsg != ''">
                and ack_rec_msg = #{ackRecMsg}
            </if>
            <if test="createTimestamp != null">
                and create_timestamp = #{createTimestamp}
            </if>
            <if test="updateTimestamp != null">
                and update_timestamp = #{updateTimestamp}
            </if>
            <if test="idList != null and idList.size > 0">
                and id in
                <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="fundCodeList != null and fundCodeList.size > 0">
                and fund_code in
                <foreach item="item" index="index" collection="fundCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="appStatusList != null and appStatusList.size > 0">
                and app_status in
                <foreach item="item" index="index" collection="appStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ackStatusList != null and ackStatusList.size > 0">
                and ack_status in
                <foreach item="item" index="index" collection="ackStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="submitStatusList != null and submitStatusList.size > 0">
                and submit_status in
                <foreach item="item" index="index" collection="submitStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by id
    </select>
    <select id="summaryByFundCode" parameterType="com.howbuy.dtms.manager.dao.query.HwSubmitDealOrderQuery"
            resultType="com.howbuy.dtms.manager.dao.bo.HwSubmitDealOrderSummaryBO">
        select
        fund_code as fundCode,
        count(fund_code) as totalCount,
        sum(ifnull(net_app_amt,  0)) as totalNetAppAmt,
        sum(ifnull(app_amt,  0)) as totalAppAmt,
        sum(if(redeem_type = '2',0,ifnull(app_vol,  0))) as totalAppVol
        from hw_submit_deal_order
        <where>
            <if test="preSubmitTaDt != null and preSubmitTaDt != ''">
                and pre_submit_ta_dt = #{preSubmitTaDt}
            </if>
            <if test="fundCodeList != null and fundCodeList.size > 0">
                and fund_code in
                <foreach item="item" index="index" collection="fundCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and app_status in ('0')
            and rec_stat = '1'
        </where>
        group by fund_code
    </select>
</mapper>

