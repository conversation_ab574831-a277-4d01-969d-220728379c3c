/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

/**
 * @description: "0"-非全委 "1"-全委
 * <AUTHOR>
 * @date 2024/11/7 16:12
 * @since JDK 1.8
 */
public enum FundTxAcctTypeEnum {

    NON_FULL("0", "非全委"),
    FULL("1", "全委");

    private String code;
    private String desc;

    FundTxAcctTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * @description:(通过代码获取描述)
     * @param code
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025/3/7 19:13
     * @since JDK 1.8
     */
    public static String getDescByCode(String code) {
        for (FundTxAcctTypeEnum fundTxAcctTypeEnum : FundTxAcctTypeEnum.values()) {
            if (fundTxAcctTypeEnum.getCode().equals(code)) {
                return fundTxAcctTypeEnum.getDesc();
            }
        }
        return null;
    }

}
