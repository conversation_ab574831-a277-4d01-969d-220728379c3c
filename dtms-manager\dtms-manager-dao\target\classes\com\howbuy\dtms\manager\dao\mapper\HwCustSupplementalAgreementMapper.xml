<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.HwCustSupplementalAgreementMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwCustSupplementalAgreementPO">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="agrId" column="agr_id" jdbcType="BIGINT"/>
            <result property="agrDtlId" column="agr_dtl_id" jdbcType="BIGINT"/>
            <result property="fundCode" column="fund_code" jdbcType="VARCHAR"/>
            <result property="fundManCode" column="fund_man_code" jdbcType="VARCHAR"/>
            <result property="hkCustNo" column="hk_cust_no" jdbcType="VARCHAR"/>
            <result property="custChineseName" column="cust_chinese_name" jdbcType="VARCHAR"/>
            <result property="idNoMask" column="id_no_mask" jdbcType="VARCHAR"/>
            <result property="idNoCipher" column="id_no_cipher" jdbcType="VARCHAR"/>
            <result property="idNoDigest" column="id_no_digest" jdbcType="VARCHAR"/>
            <result property="signStatus" column="sign_status" jdbcType="CHAR"/>
            <result property="signMethod" column="sign_method" jdbcType="CHAR"/>
            <result property="signDate" column="sign_date" jdbcType="VARCHAR"/>
            <result property="operator" column="operator" jdbcType="VARCHAR"/>
            <result property="tradeChannel" column="trade_channel" jdbcType="VARCHAR"/>
            <result property="recStat" column="rec_stat" jdbcType="CHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,agr_id,agr_dtl_id,
        fund_code,fund_man_code,hk_cust_no,
        cust_chinese_name,id_no_mask,id_no_cipher,
        id_no_digest,sign_status,sign_method,
        sign_date,operator,trade_channel,
        rec_stat,create_time,creator,
        update_time,modifier
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hw_cust_supplemental_agreement
        where  id = #{id,jdbcType=INTEGER} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from hw_cust_supplemental_agreement
        where  id = #{id,jdbcType=INTEGER} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.HwCustSupplementalAgreementPO" useGeneratedKeys="true">
        insert into hw_cust_supplemental_agreement
        ( id,agr_id,agr_dtl_id
        ,fund_code,fund_man_code,hk_cust_no
        ,cust_chinese_name,id_no_mask,id_no_cipher
        ,id_no_digest,sign_status,sign_method
        ,sign_date,operator,trade_channel
        ,rec_stat,create_time,creator
        ,update_time,modifier)
        values (#{id,jdbcType=INTEGER},#{agrId,jdbcType=BIGINT},#{agrDtlId,jdbcType=BIGINT}
        ,#{fundCode,jdbcType=VARCHAR},#{fundManCode,jdbcType=VARCHAR},#{hkCustNo,jdbcType=VARCHAR}
        ,#{custChineseName,jdbcType=VARCHAR},#{idNoMask,jdbcType=VARCHAR},#{idNoCipher,jdbcType=VARCHAR}
        ,#{idNoDigest,jdbcType=VARCHAR},#{signStatus,jdbcType=CHAR},#{signMethod,jdbcType=CHAR}
        ,#{signDate,jdbcType=VARCHAR},#{operator,jdbcType=VARCHAR},#{tradeChannel,jdbcType=VARCHAR}
        ,#{recStat,jdbcType=CHAR},#{createTime,jdbcType=TIMESTAMP},#{creator,jdbcType=VARCHAR}
        ,#{updateTime,jdbcType=TIMESTAMP},#{modifier,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.HwCustSupplementalAgreementPO" useGeneratedKeys="true">
        insert into hw_cust_supplemental_agreement
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="agrId != null">agr_id,</if>
                <if test="agrDtlId != null">agr_dtl_id,</if>
                <if test="fundCode != null">fund_code,</if>
                <if test="fundManCode != null">fund_man_code,</if>
                <if test="hkCustNo != null">hk_cust_no,</if>
                <if test="custChineseName != null">cust_chinese_name,</if>
                <if test="idNoMask != null">id_no_mask,</if>
                <if test="idNoCipher != null">id_no_cipher,</if>
                <if test="idNoDigest != null">id_no_digest,</if>
                <if test="signStatus != null">sign_status,</if>
                <if test="signMethod != null">sign_method,</if>
                <if test="signDate != null">sign_date,</if>
                <if test="operator != null">operator,</if>
                <if test="tradeChannel != null">trade_channel,</if>
                <if test="recStat != null">rec_stat,</if>
                <if test="createTime != null">create_time,</if>
                <if test="creator != null">creator,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="modifier != null">modifier,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=INTEGER},</if>
                <if test="agrId != null">#{agrId,jdbcType=BIGINT},</if>
                <if test="agrDtlId != null">#{agrDtlId,jdbcType=BIGINT},</if>
                <if test="fundCode != null">#{fundCode,jdbcType=VARCHAR},</if>
                <if test="fundManCode != null">#{fundManCode,jdbcType=VARCHAR},</if>
                <if test="hkCustNo != null">#{hkCustNo,jdbcType=VARCHAR},</if>
                <if test="custChineseName != null">#{custChineseName,jdbcType=VARCHAR},</if>
                <if test="idNoMask != null">#{idNoMask,jdbcType=VARCHAR},</if>
                <if test="idNoCipher != null">#{idNoCipher,jdbcType=VARCHAR},</if>
                <if test="idNoDigest != null">#{idNoDigest,jdbcType=VARCHAR},</if>
                <if test="signStatus != null">#{signStatus,jdbcType=CHAR},</if>
                <if test="signMethod != null">#{signMethod,jdbcType=CHAR},</if>
                <if test="signDate != null">#{signDate,jdbcType=VARCHAR},</if>
                <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
                <if test="tradeChannel != null">#{tradeChannel,jdbcType=VARCHAR},</if>
                <if test="recStat != null">#{recStat,jdbcType=CHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="creator != null">#{creator,jdbcType=VARCHAR},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="modifier != null">#{modifier,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.dtms.manager.dao.po.HwCustSupplementalAgreementPO">
        update hw_cust_supplemental_agreement
        <set>
                <if test="agrId != null">
                    agr_id = #{agrId,jdbcType=BIGINT},
                </if>
                <if test="agrDtlId != null">
                    agr_dtl_id = #{agrDtlId,jdbcType=BIGINT},
                </if>
                <if test="fundCode != null">
                    fund_code = #{fundCode,jdbcType=VARCHAR},
                </if>
                <if test="fundManCode != null">
                    fund_man_code = #{fundManCode,jdbcType=VARCHAR},
                </if>
                <if test="hkCustNo != null">
                    hk_cust_no = #{hkCustNo,jdbcType=VARCHAR},
                </if>
                <if test="custChineseName != null">
                    cust_chinese_name = #{custChineseName,jdbcType=VARCHAR},
                </if>
                <if test="idNoMask != null">
                    id_no_mask = #{idNoMask,jdbcType=VARCHAR},
                </if>
                <if test="idNoCipher != null">
                    id_no_cipher = #{idNoCipher,jdbcType=VARCHAR},
                </if>
                <if test="idNoDigest != null">
                    id_no_digest = #{idNoDigest,jdbcType=VARCHAR},
                </if>
                <if test="signStatus != null">
                    sign_status = #{signStatus,jdbcType=CHAR},
                </if>
                <if test="signMethod != null">
                    sign_method = #{signMethod,jdbcType=CHAR},
                </if>
                <if test="signDate != null">
                    sign_date = #{signDate,jdbcType=VARCHAR},
                </if>
                <if test="operator != null">
                    operator = #{operator,jdbcType=VARCHAR},
                </if>
                <if test="tradeChannel != null">
                    trade_channel = #{tradeChannel,jdbcType=VARCHAR},
                </if>
                <if test="recStat != null">
                    rec_stat = #{recStat,jdbcType=CHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="creator != null">
                    creator = #{creator,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="modifier != null">
                    modifier = #{modifier,jdbcType=VARCHAR},
                </if>
        </set>
        where   id = #{id,jdbcType=INTEGER} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.howbuy.dtms.manager.dao.po.HwCustSupplementalAgreementPO">
        update hw_cust_supplemental_agreement
        set 
            agr_id =  #{agrId,jdbcType=BIGINT},
            agr_dtl_id =  #{agrDtlId,jdbcType=BIGINT},
            fund_code =  #{fundCode,jdbcType=VARCHAR},
            fund_man_code =  #{fundManCode,jdbcType=VARCHAR},
            hk_cust_no =  #{hkCustNo,jdbcType=VARCHAR},
            cust_chinese_name =  #{custChineseName,jdbcType=VARCHAR},
            id_no_mask =  #{idNoMask,jdbcType=VARCHAR},
            id_no_cipher =  #{idNoCipher,jdbcType=VARCHAR},
            id_no_digest =  #{idNoDigest,jdbcType=VARCHAR},
            sign_status =  #{signStatus,jdbcType=CHAR},
            sign_method =  #{signMethod,jdbcType=CHAR},
            sign_date =  #{signDate,jdbcType=VARCHAR},
            operator =  #{operator,jdbcType=VARCHAR},
            trade_channel =  #{tradeChannel,jdbcType=VARCHAR},
            rec_stat =  #{recStat,jdbcType=CHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            creator =  #{creator,jdbcType=VARCHAR},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            modifier =  #{modifier,jdbcType=VARCHAR}
        where   id = #{id,jdbcType=INTEGER} 
    </update>
</mapper>
