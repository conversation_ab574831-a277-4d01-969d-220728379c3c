package com.howbuy.dtms.manager.common;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 待二次确认VO
 * @date 2024/7/30 9:44
 * @since JDK 1.8
 */
@Data
public class ConfirmVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 待确认场景码
     */
    private String confirmCode;

    /**
     * 确认提示信息
     */
    private String confirmMsg;

    /**
     * 确认数据列表
     */
    private List<ConfirmVO> list;
}
