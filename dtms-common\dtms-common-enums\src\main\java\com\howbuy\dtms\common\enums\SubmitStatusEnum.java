package com.howbuy.dtms.common.enums;

/**
 * <AUTHOR>
 * @description 上报状态 0-未上报；1-上报中；2-上报成功；3-需重新上报；4-撤回上报；5-无需上报
 * @date 2024/7/22 13:02
 * @since JDK 1.8
 */
public enum SubmitStatusEnum {

    UN_SUBMIT("0", "未上报"),

    SUBMITTING("1", "上报中"),

    SUBMIT_SUCCESS("2", "上报成功"),

    NEED_RE_SUBMIT("3", "需重新上报"),

    CANCEL_SUBMIT("4", "撤回上报"),

    NOT_NEED_SUBMIT("5", "无需上报"),

    ;
    private final String code;
    private final String desc;

    SubmitStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static SubmitStatusEnum getEnumByCode(String code) {
        for (SubmitStatusEnum statusEnum : SubmitStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}