## 计算储蓄罐交易申请手续费接口

- 请求地址

|                                 |      |                                                        |                              |
| ------------------------------- | ---- | ------------------------------------------------------ | ---------------------------- |
| ImportHwPiggyTradeAppController | http | /counter/importHwPiggyTradeApp/computePiggyTradeAppFee | 计算储蓄罐交易申请手续费接口 |

- 入参

|              |          |        |      |      |
| ------------ | -------- | ------ | ---- | ---- |
| id           | id       | Long   | 是   |      |
| buyAmt       | 买入金额 | String | 是   |      |
| discountRate | 折扣率   | String | 是   |      |

- 出参

|             |          |        |      |
| ----------- | -------- | ------ | ---- |
| code        | 状态码   | String |      |
| description | 描述信息 | String |      |
| fee         | 手续费   | String |      |

-  修改逻辑(HwPiggyTradeAppImportService#computePiggyTradeAppFee)：
  - 参数校验
    - 入参参数必填校验
    - buyAmt大于0校验，discountRate大于等于0小于等于1校验
  - 前置查询
    - 根据id查询储蓄罐交易申请记录HwPiggyTradeAppImportPO，获取储蓄罐交易申请记录，校验记录是否存在
    - 调用HkCustInfoOuterService#getHkCustInfo，获取客户信息，入参HwPiggyTradeAppImportPO的香港客户号
    - 调用QueryFundInfoOuterService#queryFundBasicInfo查询基金信息，入参：HwPiggyTradeAppImportPO的基金代码
    - 调用ParamOuterService#queryFundFeeRate，获取费率信息，入参HwPiggyTradeAppImportPO的基金代码，客户信息的投资者类型、业务代码为BusinessCodeEnum.PURCHASE.getCode()、收款方collectRecipient为CollectRecipientEnum.HOWBUY.getCode()
  - 手续费计算
    - 调用CounterOrderUtils#calculateEstimateFee计算手续费