<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.HwCustFundBalMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwCustFundBalPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="hkCustNo" column="hk_cust_no" jdbcType="VARCHAR"/>
            <result property="fundTxAcctNo" column="fund_tx_acct_no" jdbcType="VARCHAR"/>
            <result property="mainFundCode" column="main_fund_code" jdbcType="VARCHAR"/>
            <result property="fundCode" column="fund_code" jdbcType="VARCHAR"/>
            <result property="fundManCode" column="fund_man_code" jdbcType="VARCHAR"/>
            <result property="balanceVol" column="balance_vol" jdbcType="DECIMAL"/>
            <result property="balanceFactor" column="balance_factor" jdbcType="DECIMAL"/>
            <result property="navDt" column="nav_dt" jdbcType="VARCHAR"/>
            <result property="volUpdateDt" column="vol_update_dt" jdbcType="VARCHAR"/>
            <result property="recStat" column="rec_stat" jdbcType="CHAR"/>
            <result property="createTimestamp" column="create_timestamp" jdbcType="TIMESTAMP"/>
            <result property="updateTimestamp" column="update_timestamp" jdbcType="TIMESTAMP"/>
            <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,hk_cust_no,fund_tx_acct_no,
        main_fund_code,fund_code,fund_man_code,
        balance_vol,balance_factor,nav_dt,
        vol_update_dt,rec_stat,create_timestamp,
        update_timestamp,version
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hw_cust_fund_bal
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from hw_cust_fund_bal
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.HwCustFundBalPO" useGeneratedKeys="true">
        insert into hw_cust_fund_bal
        ( id,hk_cust_no,fund_tx_acct_no
        ,main_fund_code,fund_code,fund_man_code
        ,balance_vol,balance_factor,nav_dt
        ,vol_update_dt,rec_stat,create_timestamp
        ,update_timestamp,version)
        values (#{id,jdbcType=BIGINT},#{hkCustNo,jdbcType=VARCHAR},#{fundTxAcctNo,jdbcType=VARCHAR}
        ,#{mainFundCode,jdbcType=VARCHAR},#{fundCode,jdbcType=VARCHAR},#{fundManCode,jdbcType=VARCHAR}
        ,#{balanceVol,jdbcType=DECIMAL},#{balanceFactor,jdbcType=DECIMAL},#{navDt,jdbcType=VARCHAR}
        ,#{volUpdateDt,jdbcType=VARCHAR},#{recStat,jdbcType=CHAR},#{createTimestamp,jdbcType=TIMESTAMP}
        ,#{updateTimestamp,jdbcType=TIMESTAMP},#{version,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.HwCustFundBalPO" useGeneratedKeys="true">
        insert into hw_cust_fund_bal
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="hkCustNo != null">hk_cust_no,</if>
                <if test="fundTxAcctNo != null">fund_tx_acct_no,</if>
                <if test="mainFundCode != null">main_fund_code,</if>
                <if test="fundCode != null">fund_code,</if>
                <if test="fundManCode != null">fund_man_code,</if>
                <if test="balanceVol != null">balance_vol,</if>
                <if test="balanceFactor != null">balance_factor,</if>
                <if test="navDt != null">nav_dt,</if>
                <if test="volUpdateDt != null">vol_update_dt,</if>
                <if test="recStat != null">rec_stat,</if>
                <if test="createTimestamp != null">create_timestamp,</if>
                <if test="updateTimestamp != null">update_timestamp,</if>
                <if test="version != null">version,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="hkCustNo != null">#{hkCustNo,jdbcType=VARCHAR},</if>
                <if test="fundTxAcctNo != null">#{fundTxAcctNo,jdbcType=VARCHAR},</if>
                <if test="mainFundCode != null">#{mainFundCode,jdbcType=VARCHAR},</if>
                <if test="fundCode != null">#{fundCode,jdbcType=VARCHAR},</if>
                <if test="fundManCode != null">#{fundManCode,jdbcType=VARCHAR},</if>
                <if test="balanceVol != null">#{balanceVol,jdbcType=DECIMAL},</if>
                <if test="balanceFactor != null">#{balanceFactor,jdbcType=DECIMAL},</if>
                <if test="navDt != null">#{navDt,jdbcType=VARCHAR},</if>
                <if test="volUpdateDt != null">#{volUpdateDt,jdbcType=VARCHAR},</if>
                <if test="recStat != null">#{recStat,jdbcType=CHAR},</if>
                <if test="createTimestamp != null">#{createTimestamp,jdbcType=TIMESTAMP},</if>
                <if test="updateTimestamp != null">#{updateTimestamp,jdbcType=TIMESTAMP},</if>
                <if test="version != null">#{version,jdbcType=BIGINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.dtms.manager.dao.po.HwCustFundBalPO">
        update hw_cust_fund_bal
        <set>
                <if test="hkCustNo != null">
                    hk_cust_no = #{hkCustNo,jdbcType=VARCHAR},
                </if>
                <if test="fundTxAcctNo != null">
                    fund_tx_acct_no = #{fundTxAcctNo,jdbcType=VARCHAR},
                </if>
                <if test="mainFundCode != null">
                    main_fund_code = #{mainFundCode,jdbcType=VARCHAR},
                </if>
                <if test="fundCode != null">
                    fund_code = #{fundCode,jdbcType=VARCHAR},
                </if>
                <if test="fundManCode != null">
                    fund_man_code = #{fundManCode,jdbcType=VARCHAR},
                </if>
                <if test="balanceVol != null">
                    balance_vol = #{balanceVol,jdbcType=DECIMAL},
                </if>
                <if test="balanceFactor != null">
                    balance_factor = #{balanceFactor,jdbcType=DECIMAL},
                </if>
                <if test="navDt != null">
                    nav_dt = #{navDt,jdbcType=VARCHAR},
                </if>
                <if test="volUpdateDt != null">
                    vol_update_dt = #{volUpdateDt,jdbcType=VARCHAR},
                </if>
                <if test="recStat != null">
                    rec_stat = #{recStat,jdbcType=CHAR},
                </if>
                <if test="createTimestamp != null">
                    create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTimestamp != null">
                    update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP},
                </if>
                <if test="version != null">
                    version = #{version,jdbcType=BIGINT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.howbuy.dtms.manager.dao.po.HwCustFundBalPO">
        update hw_cust_fund_bal
        set 
            hk_cust_no =  #{hkCustNo,jdbcType=VARCHAR},
            fund_tx_acct_no =  #{fundTxAcctNo,jdbcType=VARCHAR},
            main_fund_code =  #{mainFundCode,jdbcType=VARCHAR},
            fund_code =  #{fundCode,jdbcType=VARCHAR},
            fund_man_code =  #{fundManCode,jdbcType=VARCHAR},
            balance_vol =  #{balanceVol,jdbcType=DECIMAL},
            balance_factor =  #{balanceFactor,jdbcType=DECIMAL},
            nav_dt =  #{navDt,jdbcType=VARCHAR},
            vol_update_dt =  #{volUpdateDt,jdbcType=VARCHAR},
            rec_stat =  #{recStat,jdbcType=CHAR},
            create_timestamp =  #{createTimestamp,jdbcType=TIMESTAMP},
            update_timestamp =  #{updateTimestamp,jdbcType=TIMESTAMP},
            version =  #{version,jdbcType=BIGINT}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
