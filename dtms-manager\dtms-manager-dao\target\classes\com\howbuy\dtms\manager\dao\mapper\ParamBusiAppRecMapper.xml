<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.ParamBusiAppRecMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.ParamBusiAppRecPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="appId" column="app_id" jdbcType="BIGINT"/>
            <result property="paramType" column="param_type" jdbcType="VARCHAR"/>
            <result property="operateType" column="operate_type" jdbcType="VARCHAR"/>
            <result property="auditStatus" column="audit_status" jdbcType="VARCHAR"/>
            <result property="keyContent" column="key_content" jdbcType="VARCHAR"/>
            <result property="checker" column="checker" jdbcType="VARCHAR"/>
            <result property="checkTime" column="check_time" jdbcType="TIMESTAMP"/>
            <result property="recStat" column="rec_stat" jdbcType="CHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTimestamp" column="create_timestamp" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="updateTimestamp" column="update_timestamp" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,app_id,param_type,
        operate_type,audit_status,key_content,
        checker,check_time,rec_stat,
        creator,create_timestamp,modifier,
        update_timestamp
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from param_busi_app_rec
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from param_busi_app_rec
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.ParamBusiAppRecPO" useGeneratedKeys="true">
        insert into param_busi_app_rec
        ( id,app_id,param_type
        ,operate_type,audit_status,key_content
        ,checker,check_time,rec_stat
        ,creator,create_timestamp,modifier
        ,update_timestamp)
        values (#{id,jdbcType=BIGINT},#{appId,jdbcType=BIGINT},#{paramType,jdbcType=VARCHAR}
        ,#{operateType,jdbcType=VARCHAR},#{auditStatus,jdbcType=VARCHAR},#{keyContent,jdbcType=VARCHAR}
        ,#{checker,jdbcType=VARCHAR},#{checkTime,jdbcType=TIMESTAMP},#{recStat,jdbcType=CHAR}
        ,#{creator,jdbcType=VARCHAR},#{createTimestamp,jdbcType=TIMESTAMP},#{modifier,jdbcType=VARCHAR}
        ,#{updateTimestamp,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.ParamBusiAppRecPO" useGeneratedKeys="true">
        insert into param_busi_app_rec
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="appId != null">app_id,</if>
                <if test="paramType != null">param_type,</if>
                <if test="operateType != null">operate_type,</if>
                <if test="auditStatus != null">audit_status,</if>
                <if test="keyContent != null">key_content,</if>
                <if test="checker != null">checker,</if>
                <if test="checkTime != null">check_time,</if>
                <if test="recStat != null">rec_stat,</if>
                <if test="creator != null">creator,</if>
                <if test="createTimestamp != null">create_timestamp,</if>
                <if test="modifier != null">modifier,</if>
                <if test="updateTimestamp != null">update_timestamp,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="appId != null">#{appId,jdbcType=BIGINT},</if>
                <if test="paramType != null">#{paramType,jdbcType=VARCHAR},</if>
                <if test="operateType != null">#{operateType,jdbcType=VARCHAR},</if>
                <if test="auditStatus != null">#{auditStatus,jdbcType=VARCHAR},</if>
                <if test="keyContent != null">#{keyContent,jdbcType=VARCHAR},</if>
                <if test="checker != null">#{checker,jdbcType=VARCHAR},</if>
                <if test="checkTime != null">#{checkTime,jdbcType=TIMESTAMP},</if>
                <if test="recStat != null">#{recStat,jdbcType=CHAR},</if>
                <if test="creator != null">#{creator,jdbcType=VARCHAR},</if>
                <if test="createTimestamp != null">#{createTimestamp,jdbcType=TIMESTAMP},</if>
                <if test="modifier != null">#{modifier,jdbcType=VARCHAR},</if>
                <if test="updateTimestamp != null">#{updateTimestamp,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.dtms.manager.dao.po.ParamBusiAppRecPO">
        update param_busi_app_rec
        <set>
                <if test="appId != null">
                    app_id = #{appId,jdbcType=BIGINT},
                </if>
                <if test="paramType != null">
                    param_type = #{paramType,jdbcType=VARCHAR},
                </if>
                <if test="operateType != null">
                    operate_type = #{operateType,jdbcType=VARCHAR},
                </if>
                <if test="auditStatus != null">
                    audit_status = #{auditStatus,jdbcType=VARCHAR},
                </if>
                <if test="keyContent != null">
                    key_content = #{keyContent,jdbcType=VARCHAR},
                </if>
                <if test="checker != null">
                    checker = #{checker,jdbcType=VARCHAR},
                </if>
                <if test="checkTime != null">
                    check_time = #{checkTime,jdbcType=TIMESTAMP},
                </if>
                <if test="recStat != null">
                    rec_stat = #{recStat,jdbcType=CHAR},
                </if>
                <if test="creator != null">
                    creator = #{creator,jdbcType=VARCHAR},
                </if>
                <if test="createTimestamp != null">
                    create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
                </if>
                <if test="modifier != null">
                    modifier = #{modifier,jdbcType=VARCHAR},
                </if>
                <if test="updateTimestamp != null">
                    update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.howbuy.dtms.manager.dao.po.ParamBusiAppRecPO">
        update param_busi_app_rec
        set 
            app_id =  #{appId,jdbcType=BIGINT},
            param_type =  #{paramType,jdbcType=VARCHAR},
            operate_type =  #{operateType,jdbcType=VARCHAR},
            audit_status =  #{auditStatus,jdbcType=VARCHAR},
            key_content =  #{keyContent,jdbcType=VARCHAR},
            checker =  #{checker,jdbcType=VARCHAR},
            check_time =  #{checkTime,jdbcType=TIMESTAMP},
            rec_stat =  #{recStat,jdbcType=CHAR},
            creator =  #{creator,jdbcType=VARCHAR},
            create_timestamp =  #{createTimestamp,jdbcType=TIMESTAMP},
            modifier =  #{modifier,jdbcType=VARCHAR},
            update_timestamp =  #{updateTimestamp,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
