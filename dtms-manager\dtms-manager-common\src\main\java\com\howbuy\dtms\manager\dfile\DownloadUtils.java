package com.howbuy.dtms.manager.dfile;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * guangyao.wu 2025-01-17 13:59
 */
@Slf4j
public class DownloadUtils {

    /**
     * web端文件下载
     *
     * @param fileDto storeConfigName-目录配置Key；
     *                relativePath-相对路径；
     *                fileName-文件名
     */
    public static void downToWeb(HttpServletResponse response, DFileInfoDTO fileDto) {
        downToWeb(response, fileDto, fileDto.getFileName());
    }

    /**
     * web端文件下载
     *
     * @param fileDto      storeConfigName-目录配置Key；
     *                     relativePath-相对路径；
     *                     fileName-文件名
     * @param downloadName 下载名称
     */
    public static void downToWeb(HttpServletResponse response, DFileInfoDTO fileDto, String downloadName) {
        try {
            // 后缀名处理
            String suffixName = FilenameUtils.getExtension(fileDto.getFileName());
            if (StringUtils.isNotEmpty(suffixName) && !downloadName.endsWith(suffixName)) {
                downloadName = downloadName + "." + suffixName;
            }

            response.setContentType("application/octet-stream");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(downloadName, StandardCharsets.UTF_8.name()));
            DFileUtils.read(fileDto, response.getOutputStream());
        } catch (Exception e) {
            log.error("output文件失败：{}", fileDto, e);
        }
    }
}
