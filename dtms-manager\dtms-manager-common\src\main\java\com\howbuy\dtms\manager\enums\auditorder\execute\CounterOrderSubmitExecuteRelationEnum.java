package com.howbuy.dtms.manager.enums.auditorder.execute;

import com.howbuy.dtms.common.enums.BusinessCodeEnum;
import com.howbuy.dtms.manager.enums.auditorder.CounterBizTypeEnum;

public enum CounterOrderSubmitExecuteRelationEnum {



    /**************************************************业务审核-提交业务申请执行器执行器********************************************************************/
    SUBS_COUNTER_ORDER_SUBMIT(CounterOrderExecuteEnum.PURCHASE_COUNTER_ORDER_SUBMIT,  CounterBizTypeEnum.SUBS.getCode()),

    PURCHASE_COUNTER_ORDER_SUBMIT(CounterOrderExecuteEnum.PURCHASE_COUNTER_ORDER_SUBMIT,  CounterBizTypeEnum.PURCHASE.getCode()),

    _112A_COUNTER_ORDER_SUBMIT(CounterOrderExecuteEnum.PURCHASE_COUNTER_ORDER_SUBMIT,  CounterBizTypeEnum._112A.getCode()),

    _112B_COUNTER_ORDER_SUBMIT(CounterOrderExecuteEnum.PURCHASE_COUNTER_ORDER_SUBMIT,  CounterBizTypeEnum._112B.getCode()),

    SUB_AND_FIRST_PAID_COUNTER_ORDER_SUBMIT(CounterOrderExecuteEnum.PURCHASE_COUNTER_ORDER_SUBMIT,  CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),

    REDEEM_COUNTER_ORDER_SUBMIT(CounterOrderExecuteEnum.REDEEM_COUNTER_ORDER_SUBMIT, CounterBizTypeEnum.REDEEM.getCode()),

    REVOKE_COUNTER_ORDER_SUBMIT(CounterOrderExecuteEnum.REVOKE_COUNTER_ORDER_SUBMIT, CounterBizTypeEnum.REVOKE.getCode()),

    EXTENSION_COUNTER_ORDER_SUBMIT(CounterOrderExecuteEnum.EXTENSION_COUNTER_ORDER_SUBMIT, CounterBizTypeEnum.EXTENSION.getCode()),

    FUND_TRANSFER_COUNTER_ORDER_SUBMIT(CounterOrderExecuteEnum.FUND_TRANSFER_COUNTER_ORDER_SUBMIT, CounterBizTypeEnum.FUND_TRANSFER.getCode()),

    FORCE_SUBTRACT_COUNTER_ORDER_SUBMIT(CounterOrderExecuteEnum.FORCE_SUBTRACT_COUNTER_ORDER_SUBMIT_EXECUTE, CounterBizTypeEnum.FORCE_SUBTRACT.getCode()),

    FORCE_ADD_COUNTER_ORDER_SUBMIT(CounterOrderExecuteEnum.FORCE_ADD_COUNTER_ORDER_SUBMIT_EXECUTE, CounterBizTypeEnum.FORCE_ADD.getCode()),

    BATCH_PAID_COUNTER_ORDER_SUBMIT(CounterOrderExecuteEnum.BATCH_PAID_COUNTER_ORDER_SUBMIT_EXECUTE, CounterBizTypeEnum.BATCH_PAID.getCode()),

    FULL_BATCH_SUBSCRIBE_COUNTER_ORDER_SUBMIT(CounterOrderExecuteEnum.FULL_BATCH_SUBSCRIBE_COUNTER_ORDER_SUBMIT_EXECUTE, CounterBizTypeEnum.FULL_BATCH_SUBSCRIBE.getCode()),

    FULL_BATCH_REDEEM_COUNTER_ORDER_SUBMIT(CounterOrderExecuteEnum.FULL_BATCH_REDEEM_COUNTER_ORDER_SUBMIT_EXECUTE, CounterBizTypeEnum.FULL_BATCH_REDEEM.getCode()),

    /**
     * 基金交易账号开通
     */
    FUND_TX_ACCT_NO_OPEN_COUNTER_ORDER_SUBMIT(CounterOrderExecuteEnum.FUND_TX_ACCT_NO_OPEN_COUNTER_ORDER_SUBMIT_EXECUTE, CounterBizTypeEnum.FUND_TX_ACCT_NO_OPEN.getCode()),
    ;
    private final CounterOrderExecuteEnum executeEnum;

    private final String bizType;

    CounterOrderSubmitExecuteRelationEnum(CounterOrderExecuteEnum executeEnum, String bizType) {
        this.executeEnum = executeEnum;
        this.bizType = bizType;
    }

    public static String getBizTypeByBizType(String bizType) {
        for (CounterOrderSubmitExecuteRelationEnum counterOrdeBizTypeRuleEnum : CounterOrderSubmitExecuteRelationEnum.values()) {
            if (counterOrdeBizTypeRuleEnum.getBizType().equals(bizType)) {
                return counterOrdeBizTypeRuleEnum.getExecuteEnum().getCode();
            }
        }
        return null;
    }

    public CounterOrderExecuteEnum getExecuteEnum() {
        return executeEnum;
    }

    public String getBizType() {
        return bizType;
    }
}
