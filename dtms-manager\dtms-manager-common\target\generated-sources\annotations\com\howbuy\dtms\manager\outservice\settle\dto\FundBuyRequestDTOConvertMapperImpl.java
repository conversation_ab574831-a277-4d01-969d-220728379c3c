package com.howbuy.dtms.manager.outservice.settle.dto;

import com.howbuy.dtms.settle.client.facade.trade.fundbuy.FundBuyRequest;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:23:45+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class FundBuyRequestDTOConvertMapperImpl implements FundBuyRequestDTOConvertMapper {

    @Override
    public FundBuyRequest convert(FundBuyRequestDTO dto) {
        if ( dto == null ) {
            return null;
        }

        FundBuyRequest fundBuyRequest = new FundBuyRequest();

        fundBuyRequest.setTraceId( dto.getTraceId() );
        fundBuyRequest.setAppAmt( dto.getAppAmt() );
        fundBuyRequest.setAppDt( dto.getAppDt() );
        fundBuyRequest.setAppStatus( dto.getAppStatus() );
        fundBuyRequest.setAppTm( dto.getAppTm() );
        fundBuyRequest.setBusiCode( dto.getBusiCode() );
        fundBuyRequest.setCpAcctNo( dto.getCpAcctNo() );
        fundBuyRequest.setCurrency( dto.getCurrency() );
        fundBuyRequest.setDealDtlNo( dto.getDealDtlNo() );
        fundBuyRequest.setDiscountRate( dto.getDiscountRate() );
        fundBuyRequest.setEsitmateFee( dto.getEsitmateFee() );
        fundBuyRequest.setExtControlNum( dto.getExtControlNum() );
        fundBuyRequest.setExtControlType( dto.getExtControlType() );
        fundBuyRequest.setExtOption( dto.getExtOption() );
        fundBuyRequest.setFeeRate( dto.getFeeRate() );
        fundBuyRequest.setFundCode( dto.getFundCode() );
        fundBuyRequest.setFundTxAcctNo( dto.getFundTxAcctNo() );
        fundBuyRequest.setHkCustNo( dto.getHkCustNo() );
        fundBuyRequest.setInvstType( dto.getInvstType() );
        fundBuyRequest.setMiddleOrderNo( dto.getMiddleOrderNo() );
        fundBuyRequest.setNetAppAmt( dto.getNetAppAmt() );
        fundBuyRequest.setOpenDt( dto.getOpenDt() );
        fundBuyRequest.setPreSubmitTaDt( dto.getPreSubmitTaDt() );
        fundBuyRequest.setPreSubmitTaTm( dto.getPreSubmitTaTm() );
        fundBuyRequest.setSubmitTaDt( dto.getSubmitTaDt() );

        return fundBuyRequest;
    }
}
