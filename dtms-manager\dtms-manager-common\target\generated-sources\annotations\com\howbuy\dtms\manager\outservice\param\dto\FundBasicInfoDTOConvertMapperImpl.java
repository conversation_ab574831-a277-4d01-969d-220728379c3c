package com.howbuy.dtms.manager.outservice.param.dto;

import com.howbuy.dtms.product.client.query.vo.FundBasicInfoVO;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:23:43+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class FundBasicInfoDTOConvertMapperImpl implements FundBasicInfoDTOConvertMapper {

    @Override
    public FundBasicInfoDTO convert(FundBasicInfoVO vo) {
        if ( vo == null ) {
            return null;
        }

        FundBasicInfoDTO fundBasicInfoDTO = new FundBasicInfoDTO();

        fundBasicInfoDTO.setAppendPurchaseDiffer( vo.getAppendPurchaseDiffer() );
        fundBasicInfoDTO.setBuyOpenDtExtDays( vo.getBuyOpenDtExtDays() );
        fundBasicInfoDTO.setCapitalMatchingConditions( vo.getCapitalMatchingConditions() );
        fundBasicInfoDTO.setCurrency( vo.getCurrency() );
        fundBasicInfoDTO.setDelayedDay( vo.getDelayedDay() );
        fundBasicInfoDTO.setDfltDivMode( vo.getDfltDivMode() );
        fundBasicInfoDTO.setEffectiveDt( vo.getEffectiveDt() );
        fundBasicInfoDTO.setEstablishDt( vo.getEstablishDt() );
        fundBasicInfoDTO.setExtControlNum( vo.getExtControlNum() );
        fundBasicInfoDTO.setExtControlType( vo.getExtControlType() );
        fundBasicInfoDTO.setExtMode( vo.getExtMode() );
        fundBasicInfoDTO.setExtOption( vo.getExtOption() );
        fundBasicInfoDTO.setFeeCalMode( vo.getFeeCalMode() );
        fundBasicInfoDTO.setFews( vo.getFews() );
        fundBasicInfoDTO.setFirstPurchaseDiffer( vo.getFirstPurchaseDiffer() );
        fundBasicInfoDTO.setFundAbbr( vo.getFundAbbr() );
        fundBasicInfoDTO.setFundCategory( vo.getFundCategory() );
        fundBasicInfoDTO.setFundCode( vo.getFundCode() );
        fundBasicInfoDTO.setFundEndDt( vo.getFundEndDt() );
        fundBasicInfoDTO.setFundManCode( vo.getFundManCode() );
        fundBasicInfoDTO.setFundMinHoldVol( vo.getFundMinHoldVol() );
        fundBasicInfoDTO.setFundMinRetainedAsset( vo.getFundMinRetainedAsset() );
        fundBasicInfoDTO.setFundName( vo.getFundName() );
        fundBasicInfoDTO.setFundNameEn( vo.getFundNameEn() );
        fundBasicInfoDTO.setFundRiskLevel( vo.getFundRiskLevel() );
        fundBasicInfoDTO.setGradationCall( vo.getGradationCall() );
        fundBasicInfoDTO.setHasClosedPeriod( vo.getHasClosedPeriod() );
        fundBasicInfoDTO.setHasLockPeriod( vo.getHasLockPeriod() );
        fundBasicInfoDTO.setIpoEndDt( vo.getIpoEndDt() );
        fundBasicInfoDTO.setIpoStartDt( vo.getIpoStartDt() );
        fundBasicInfoDTO.setIsComplexProduct( vo.getIsComplexProduct() );
        fundBasicInfoDTO.setIsEquity( vo.getIsEquity() );
        fundBasicInfoDTO.setIsFullyCommissionedProduct( vo.getIsFullyCommissionedProduct() );
        fundBasicInfoDTO.setIsHongkongFund( vo.getIsHongkongFund() );
        fundBasicInfoDTO.setIsInvestmentDerivative( vo.getIsInvestmentDerivative() );
        fundBasicInfoDTO.setIsInvestmentVirtualAssets( vo.getIsInvestmentVirtualAssets() );
        fundBasicInfoDTO.setIsMotherChildFund( vo.getIsMotherChildFund() );
        fundBasicInfoDTO.setIsPiggyFund( vo.getIsPiggyFund() );
        fundBasicInfoDTO.setIsScheduledTrade( vo.getIsScheduledTrade() );
        fundBasicInfoDTO.setIsSumReportFundMan( vo.getIsSumReportFundMan() );
        fundBasicInfoDTO.setJzws( vo.getJzws() );
        fundBasicInfoDTO.setLiquidationBeginDt( vo.getLiquidationBeginDt() );
        fundBasicInfoDTO.setLockAmount( vo.getLockAmount() );
        fundBasicInfoDTO.setLockPeriodType( vo.getLockPeriodType() );
        fundBasicInfoDTO.setLockStartDtType( vo.getLockStartDtType() );
        fundBasicInfoDTO.setMainFundCode( vo.getMainFundCode() );
        fundBasicInfoDTO.setMemo( vo.getMemo() );
        fundBasicInfoDTO.setMinHoldVol( vo.getMinHoldVol() );
        fundBasicInfoDTO.setMinRetainedAsset( vo.getMinRetainedAsset() );
        fundBasicInfoDTO.setOpenFlag( vo.getOpenFlag() );
        fundBasicInfoDTO.setOrderEndTm( vo.getOrderEndTm() );
        fundBasicInfoDTO.setPaymentEndTm( vo.getPaymentEndTm() );
        fundBasicInfoDTO.setPiggyAutoRedeemIntervalDays( vo.getPiggyAutoRedeemIntervalDays() );
        fundBasicInfoDTO.setPiggyPaymentIntervalDays( vo.getPiggyPaymentIntervalDays() );
        fundBasicInfoDTO.setRedeemClosedPeriodAmount( vo.getRedeemClosedPeriodAmount() );
        fundBasicInfoDTO.setRedeemClosedPeriodEndDt( vo.getRedeemClosedPeriodEndDt() );
        fundBasicInfoDTO.setRedeemClosedPeriodType( vo.getRedeemClosedPeriodType() );
        fundBasicInfoDTO.setRedeemOpenDtExtDays( vo.getRedeemOpenDtExtDays() );
        fundBasicInfoDTO.setRedemptionDiscountRatioByAmount( vo.getRedemptionDiscountRatioByAmount() );
        fundBasicInfoDTO.setRedemptionMethod( vo.getRedemptionMethod() );
        fundBasicInfoDTO.setReportFundManTm( vo.getReportFundManTm() );
        fundBasicInfoDTO.setSubsClosedPeriodAmount( vo.getSubsClosedPeriodAmount() );
        fundBasicInfoDTO.setSubsClosedPeriodEndDt( vo.getSubsClosedPeriodEndDt() );
        fundBasicInfoDTO.setSubsClosedPeriodType( vo.getSubsClosedPeriodType() );
        fundBasicInfoDTO.setSupportExt( vo.getSupportExt() );
        fundBasicInfoDTO.setSupportPartExt( vo.getSupportPartExt() );
        fundBasicInfoDTO.setSyws( vo.getSyws() );
        fundBasicInfoDTO.setSzws( vo.getSzws() );
        fundBasicInfoDTO.setVolPrecision( vo.getVolPrecision() );

        return fundBasicInfoDTO;
    }
}
