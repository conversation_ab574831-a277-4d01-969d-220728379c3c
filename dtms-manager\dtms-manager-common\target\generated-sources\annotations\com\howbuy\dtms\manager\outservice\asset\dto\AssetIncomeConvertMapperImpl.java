package com.howbuy.dtms.manager.outservice.asset.dto;

import com.howbuy.tms.asset.high.income.domain.HighIncomeDomain;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:23:42+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class AssetIncomeConvertMapperImpl implements AssetIncomeConvertMapper {

    @Override
    public AssetIncomeDTO toDTO(HighIncomeDomain domain) {
        if ( domain == null ) {
            return null;
        }

        AssetIncomeDTO assetIncomeDTO = new AssetIncomeDTO();

        assetIncomeDTO.setBalanceAmtExFee( domain.getBalanceAmtExFee() );
        assetIncomeDTO.setBalanceFactor( domain.getBalanceFactor() );
        assetIncomeDTO.setBalanceIncomeExFee( domain.getBalanceIncomeExFee() );
        assetIncomeDTO.setBalanceIncomeRateExFee( domain.getBalanceIncomeRateExFee() );
        assetIncomeDTO.setBalanceVol( domain.getBalanceVol() );
        assetIncomeDTO.setCurrencyUnit( domain.getCurrencyUnit() );
        assetIncomeDTO.setFundCode( domain.getFundCode() );
        assetIncomeDTO.setHboneNo( domain.getHboneNo() );
        assetIncomeDTO.setIncomeDt( domain.getIncomeDt() );
        assetIncomeDTO.setNaFee( domain.getNaFee() );
        assetIncomeDTO.setNav( domain.getNav() );
        assetIncomeDTO.setNavDt( domain.getNavDt() );

        return assetIncomeDTO;
    }

    @Override
    public List<AssetIncomeDTO> toDTOList(List<HighIncomeDomain> domainList) {
        if ( domainList == null ) {
            return null;
        }

        List<AssetIncomeDTO> list = new ArrayList<AssetIncomeDTO>( domainList.size() );
        for ( HighIncomeDomain highIncomeDomain : domainList ) {
            list.add( toDTO( highIncomeDomain ) );
        }

        return list;
    }
}
