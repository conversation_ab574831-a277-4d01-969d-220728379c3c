package com.howbuy.dtms.manager.console.vo.components;

import com.howbuy.dtms.manager.outservice.hkacc.dto.HkCustInfoDTO;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T16:12:59+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class HkCustInfoVOConvertMapperImpl implements HkCustInfoVOConvertMapper {

    @Override
    public HkCustInfoVO convert(HkCustInfoDTO dto) {
        if ( dto == null ) {
            return null;
        }

        HkCustInfoVO hkCustInfoVO = new HkCustInfoVO();

        return hkCustInfoVO;
    }
}
