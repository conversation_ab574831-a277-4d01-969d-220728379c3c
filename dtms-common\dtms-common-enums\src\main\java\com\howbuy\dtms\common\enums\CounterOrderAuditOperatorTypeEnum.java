package com.howbuy.dtms.common.enums;

public enum CounterOrderAuditOperatorTypeEnum {

    /**
     * 提交申请
     */
    SUBMIT_APPLY("1", "提交申请"),
    /**
     * 复核通过
     */
    APPROVE("2", "复核通过"),
    /**
     * 审核不通过
     */
    FAILURE("3", "审核不通过"),
    /**
     * 驳回至初审
     */
    REJECT_TO_FIRST_EXAMINE("4", "驳回至初审"),

    /**
     * 再提交
     */
    RE_SUBMIT("5", "再提交"),

    /**
     * 回访通过
     */
    REVISIT_PASS("6", "回访通过"),


    WAIT_REVISIT("7", "等待回访"),

    WAIT_REVISIT_FAILURE("8", "回访不通过"),

   VOIDED("9","作废"),

    ;
    private final String code;


    private final String desc;

    CounterOrderAuditOperatorTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * @param
     * @return java.lang.String
     * @description: 根据Code获取对应的描述信息
     * @author: jinqing.rao
     * @date: 2024/11/17 19:48
     * @since JDK 1.8
     */
    public static String getDescByCode(String code) {
        for (CounterOrderAuditOperatorTypeEnum item : CounterOrderAuditOperatorTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item.getDesc();
            }
        }
        return null;
    }


    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
