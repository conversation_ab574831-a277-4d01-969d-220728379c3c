## 修改储蓄罐交易申请购买金额接口

- 请求地址

|                                 |      |                                                         |                            |
| ------------------------------- | ---- | ------------------------------------------------------- | -------------------------- |
| ImportHwPiggyTradeAppController | http | /counter/importHwPiggyTradeApp/upatePiggyTradeAppBuyAmt | 修改储蓄罐交易申请购买金额 |

- 入参

|              |          |        |      |      |
| ------------ | -------- | ------ | ---- | ---- |
| id           | id       | Long   | 是   |      |
| buyAmt       | 买入金额 | String | 是   |      |
| discountRate | 折扣率   | String | 是   |      |
| remark       | 备注     | String | 是   |      |

-  修改逻辑(HwPiggyTradeAppImportService#upatePiggyTradeAppBuyAmt)：
  - 入参参数必填校验
  - 根据id查询储蓄罐交易申请记录HwPiggyTradeAppImportPO，校验记录是否存在
  - 调用DtmsOrderOuterService#updatePiggyTradeAppBuy接口，入参buyAmt、discountRate、remark从入参获取，importAppId取HwPiggyTradeAppImportPO的importAppId