---
description: DTMS-Manager项目全局编码规范与开发指南
globs: 
alwaysApply: true
---

# DTMS-Manager 项目全局编码规范与开发指南

> 本规范总结了DTMS-Manager项目的全局编码规范、命名规范、API设计规范等内容，适用于项目中所有Java代码文件。
> 作者: hongdong.xie
> 日期: 2025-03-08 19:18:34

## 1. 项目架构概述

DTMS-Manager项目是一个基于Spring Boot 2.3.12的分布式应用系统，主要用于金融与基金行业的交易管理。系统采用模块化设计，包含以下主要模块：

- `dtms-manager-dao`: 数据访问层实现
- `dtms-manager-common`: 公共类定义，包含实用工具、枚举、常量等
- `dtms-manager-console`: 控制台服务实现
- `dtms-manager-counter`: 柜台服务实现
- `dtms-manager-remote`: 远程调用包装

项目使用Spring Cloud Hoxton.SR9作为微服务框架，Dubbo 3.2.12作为RPC调用框架，整体架构符合DDD领域驱动设计思想。

## 2. 编码规范

### 2.1 通用编码规范

- 使用UTF-8编码格式
- 缩进使用4个空格
- 行宽不超过120个字符
- 文件结尾保留一个空行
- 类和方法之间保留一个空行
- 相关字段/方法应放在一起
- 遵循阿里巴巴Java开发手册规范

### 2.2 命名规范

#### 2.2.1 包命名

- 全部小写，使用点分隔
- 包结构遵循：`com.howbuy.dtms.manager.[module].[function]`
- 示例：`com.howbuy.dtms.manager.counter.controller`

#### 2.2.2 类命名

- 使用PascalCase命名法（首字母大写）
- Controller类：`{业务域}[{子域}]Controller`，如`CounterOrderController`
- Service类：`{业务域}[{子域}]Service`，如`CounterOrderService`
- Repository类：`{业务域}[{子域}]Repository`，如`CounterOrderRepository`
- 实体类：
  - PO类：`{表名}PO`，对应数据库表
  - DTO类：`{业务域}[{子域}]DTO`，内部传输对象
  - VO类：`{业务域}[{子域}]VO`，视图对象
  - Request类：`{操作动词}{业务域}[{子域}]Request`，请求参数对象

#### 2.2.3 方法命名

- 使用camelCase命名法（首字母小写）
- 查询方法：`query{业务实体}[{附加信息}]`，如`queryOrderList`
- 创建方法：`create{业务实体}[{附加信息}]`，如`createOrder`
- 更新方法：`update{业务实体}[{附加信息}]`，如`updateOrderStatus`
- 删除方法：`delete{业务实体}[{附加信息}]`，如`deleteOrder`
- 导出方法：`download{业务实体}[{附加信息}]`，如`downloadOrderList`

#### 2.2.4 变量命名

- 使用camelCase命名法（首字母小写）
- 变量名应具有明确的业务含义
- 布尔类型变量以"is"/"has"/"can"等开头，如`isValid`
- 集合类变量名应体现复数形式，如`orderList`
- 常量命名全部大写，单词间以下划线分隔，如`MAX_COUNT`

### 2.3 注释规范

#### 2.3.1 类注释模板

```java
/**
 * 类功能描述
 *
 * <AUTHOR>
 * @date 2025-03-08 19:18:34
 * @since JDK 1.8
 */
```

#### 2.3.2 方法注释模板

```java
/**
 * 方法功能描述
 *
 * @param paramName 参数说明
 * @return 返回值说明
 * <AUTHOR>
 * @date 2025-03-08 19:18:34
 */
```

#### 2.3.3 字段注释

```java
/**
 * 字段说明
 */
private String fieldName;
```

### 2.4 代码规范

#### 2.4.1 Controller层规范

- 使用`@RestController`标注
- 使用`@RequestMapping`定义基础路径
- 使用`@PostMapping`标注POST方法
- 使用`@RequestBody`接收请求参数
- 使用`Response<T>`统一返回格式
- 方法体保持简洁，仅调用Service层方法

#### 2.4.2 Service层规范

- 使用`@Service`标注
- 实现业务逻辑，包含参数校验、逻辑处理
- 使用`@Transactional`管理事务
- 使用`AssertUtil`进行参数校验
- 使用`BusinessException`抛出业务异常

#### 2.4.3 Repository层规范

- 使用自定义Repository实现，封装MyBatis Mapper
- 提供CRUD基础方法
- 封装复杂查询条件
- 管理分页逻辑

#### 2.4.4 异常处理规范

- 使用全局异常处理器捕获异常
- 业务异常使用`BusinessException`
- 系统异常使用`SystemException`
- 使用`ExceptionCodeEnum`定义异常码
- 异常信息不暴露敏感信息

## 3. API设计规范

### 3.1 URI设计

- 使用小写字母，以`/`分隔
- 格式：`/{模块名}/{业务域}/[{子域}/]{操作}`
- 示例：`/counter/order/queryorderdetail`

### 3.2 请求响应规范

- 请求统一使用POST方法，使用JSON格式
- 请求参数使用专用Request对象封装
- 响应统一使用`Response<T>`封装，包含`code`、`description`、`data`三个字段
- 成功响应使用`Response.ok(data)`方法
- 失败响应使用`Response.fail(code, description)`方法

### 3.3 APIDOC文档规范

- 使用`@api`标注API信息
- 使用`@apiParam`标注请求参数
- 使用`@apiSuccess`标注响应字段
- 使用`@apiSuccessExample`提供响应示例
- 文档中参数与实际代码保持一致

示例：
```java
/**
 * @api {POST} /counter/order/queryorderdetail queryOrderDetail()
 * @apiVersion 1.0.0
 * @apiGroup CounterOrderController
 * @apiName queryOrderDetail()
 * @apiDescription 查询订单详情
 * @apiParam (请求体) {String} orderId 订单ID
 * @apiParam (请求体) {String} hkCustNo 香港客户号
 * @apiSuccess (响应结果) {String} code 状态码      状态码0000表示成功
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccessExample 响应结果示例
 * {"code":"0000","data":{"orderNo":"123456"},"description":"成功"}
 */
```

## 4. 实体类规范

### 4.1 实体类命名与注解

- 所有实体类使用`@Setter`/`@Getter`注解，不使用`@Data`
- PO类对应数据库表，包含表结构完整映射
- VO类用于视图展示，按照API响应需求定义
- DTO类用于服务间传输，包含业务逻辑所需字段
- Request类用于API请求参数，按照API请求需求定义

### 4.2 实体类字段规范

- 所有字段提供中文注释
- 日期类型统一使用`java.util.Date`
- 金额类型使用`java.math.BigDecimal`
- 敏感字段添加`@JsonProperty`控制序列化

## 5. 数据访问规范

### 5.1 数据库访问

- 使用MyBatis作为ORM框架
- Mapper接口定义基础CRUD方法
- 复杂查询使用自定义方法，提供Query参数对象
- SQL语句在XML文件中定义，避免使用注解SQL

### 5.2 分页查询规范

- 使用PageHelper实现分页
- 分页参数统一使用`pageNo`和`pageSize`
- 分页结果统一使用带有`total`的List对象

## 6. 日志规范

### 6.1 日志配置

- 使用SLF4J + Logback组合
- 日志级别按照环境配置，生产环境为INFO级别
- 异常日志完整记录堆栈信息
- 敏感信息脱敏后记录

### 6.2 日志记录规范

- 记录方法入参与出参：DEBUG级别
- 记录业务关键操作：INFO级别
- 记录业务异常：WARN级别
- 记录系统异常：ERROR级别
- 日志内容包含关键业务信息，方便问题定位

## 7. 安全规范

### 7.1 参数校验

- 使用`AssertUtil`进行参数校验
- 必要情况下进行业务规则校验
- SQL参数使用预编译，防止SQL注入
- 文件上传限制类型和大小

### 7.2 数据安全

- 敏感数据加密存储
- 敏感数据传输时加密
- 敏感数据展示时脱敏
- 遵循最小权限原则

## 8. 性能优化规范

### 8.1 查询优化

- 合理使用索引
- 大数据量查询使用分页
- 减少不必要的关联查询
- 使用缓存减少数据库查询

### 8.2 代码优化

- 避免过深的嵌套循环
- 合理使用批量操作
- 使用线程池处理异步任务
- 避免大事务，拆分为小事务

## 9. 测试规范

### 9.1 单元测试

- 核心业务逻辑必须有单元测试
- 使用JUnit 5 + Mockito框架
- 测试覆盖率不低于80%
- 测试用例命名清晰，表明测试意图

### 9.2 接口测试

- 每个API接口提供测试用例
- 测试正常流程与异常流程
- 测试边界条件
- 使用自动化测试工具

## 10. 代码管理规范

### 10.1 版本管理

- 使用Git进行版本控制
- 遵循GitFlow工作流
- 版本号格式：`主版本号.次版本号.修订号.紧急修复号`
- 示例：`1.9.4.0-RELEASE`

### 10.2 提交规范

- 提交信息清晰简洁，说明修改内容
- 提交前进行代码review
- 每个功能独立分支开发
- 合并前解决冲突

## 11. 依赖管理规范

### 11.1 依赖版本

- 使用Maven管理依赖
- 在父POM中统一管理依赖版本
- 避免依赖冲突，使用`dependencyManagement`

### 11.2 第三方库规范

- 优先使用官方库
- 引入新库需经过评审
- 定期更新库版本，修复安全漏洞
- 禁止使用废弃或不再维护的库

## 12. 生产环境规范

### 12.1 部署规范

- 使用Docker容器化部署
- 配置信息外部化，使用配置中心
- 使用监控和告警机制
- 提供完善的部署文档

### 12.2 监控规范

- 使用健康检查确保服务可用
- 记录关键业务指标
- 监控系统资源使用情况
- 设置阈值触发告警

---

以上规范适用于dtms-manager项目的所有模块。在开发过程中，开发人员应严格遵循这些规范，确保代码质量和系统可维护性。规范会随着项目的发展持续更新和完善。 