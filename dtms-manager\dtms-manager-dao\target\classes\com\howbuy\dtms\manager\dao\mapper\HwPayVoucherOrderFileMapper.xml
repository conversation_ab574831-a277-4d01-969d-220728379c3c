<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.HwPayVoucherOrderFileMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwPayVoucherOrderFilePO">
        <!--@Table hw_pay_voucher_order_file-->
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="voucherNo" column="voucher_no" jdbcType="BIGINT"/>
        <result property="fileUrl" column="file_url" jdbcType="VARCHAR"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="fileType" column="file_type" jdbcType="VARCHAR"/>
        <result property="recStat" column="rec_stat" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, 
        voucher_no, 
        file_url, 
        file_name, 
        file_type, 
        rec_stat, 
        creator, 
        modifier, 
        create_time, 
        update_time
    </sql>

    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hw_pay_voucher_order_file
        where id = #{id}
        and rec_stat = '0'
    </select>

    <!-- 查询符合条件的数据 -->
    <select id="selectBySelective" parameterType="com.howbuy.dtms.manager.dao.po.HwPayVoucherOrderFilePO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hw_pay_voucher_order_file
        <where>
                <if test="id != null">
                    and id = #{id}
                </if>
                <if test="voucherNo != null and voucherNo != ''">
                    and voucher_no = #{voucherNo}
                </if>
                <if test="fileUrl != null and fileUrl != ''">
                    and file_url = #{fileUrl}
                </if>
                <if test="fileName != null and fileName != ''">
                    and file_name = #{fileName}
                </if>
                <if test="fileType != null and fileType != ''">
                    and file_type = #{fileType}
                </if>
                <if test="recStat != null and recStat != ''">
                    and rec_stat = #{recStat}
                </if>
                <if test="creator != null and creator != ''">
                    and creator = #{creator}
                </if>
                <if test="modifier != null and modifier != ''">
                    and modifier = #{modifier}
                </if>
                <if test="createTime != null">
                    and create_time = #{createTime}
                </if>
                <if test="updateTime != null">
                    and update_time = #{updateTime}
                </if>
        </where>
        order by id
    </select>


    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(*)
        from hw_pay_voucher_order_file
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="voucherNo != null and voucherNo != ''">
                and voucher_no = #{voucherNo}
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                and file_url = #{fileUrl}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name = #{fileName}
            </if>
            <if test="fileType != null and fileType != ''">
                and file_type = #{fileType}
            </if>
            <if test="recStat != null and recStat != ''">
                and rec_stat = #{recStat}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="modifier != null and modifier != ''">
                and modifier = #{modifier}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>

    <!--分页查询，根据主键ID升序排序-->
    <select id="selectWithPage" parameterType="com.howbuy.dtms.manager.dao.query.HwPayVoucherOrderFileQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hw_pay_voucher_order_file
        <where>
                    <if test="id != null">
                        and id = #{id}
                    </if>
                    <if test="voucherNo != null and voucherNo != ''">
                        and voucher_no = #{voucherNo}
                    </if>
                    <if test="fileUrl != null and fileUrl != ''">
                        and file_url = #{fileUrl}
                    </if>
                    <if test="fileName != null and fileName != ''">
                        and file_name = #{fileName}
                    </if>
                    <if test="fileType != null and fileType != ''">
                        and file_type = #{fileType}
                    </if>
                    <if test="recStat != null and recStat != ''">
                        and rec_stat = #{recStat}
                    </if>
                    <if test="creator != null and creator != ''">
                        and creator = #{creator}
                    </if>
                    <if test="modifier != null and modifier != ''">
                        and modifier = #{modifier}
                    </if>
                    <if test="createTime != null">
                        and create_time = #{createTime}
                    </if>
                    <if test="updateTime != null">
                        and update_time = #{updateTime}
                    </if>
            <if test="idList != null and idList.size > 0">
                and id in
                <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and rec_stat = '0'
        </where>
        order by id
    </select>

    <select id="listFileByVoucherNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hw_pay_voucher_order_file
        where voucher_no = #{voucherNo}
        and rec_stat = '0'
    </select>
</mapper>

