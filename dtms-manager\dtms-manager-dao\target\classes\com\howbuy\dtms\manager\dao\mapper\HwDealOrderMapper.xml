<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.HwDealOrderMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwDealOrderPO">
        <!--@Table hw_deal_order-->
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="dealNo" column="deal_no" jdbcType="BIGINT"/>
        <result property="hkCustNo" column="hk_cust_no" jdbcType="VARCHAR"/>
        <result property="custChineseName" column="cust_chinese_name" jdbcType="VARCHAR"/>
        <result property="idType" column="id_type" jdbcType="VARCHAR"/>
        <result property="idNoCipher" column="id_no_cipher" jdbcType="VARCHAR"/>
        <result property="idNoDigest" column="id_no_digest" jdbcType="VARCHAR"/>
        <result property="idNoMask" column="id_no_mask" jdbcType="VARCHAR"/>
        <result property="invstType" column="invst_type" jdbcType="VARCHAR"/>
        <result property="qualificationType" column="qualification_type" jdbcType="VARCHAR"/>
        <result property="custRiskLevel" column="cust_risk_level" jdbcType="VARCHAR"/>
        <result property="cpAcctNo" column="cp_acct_no" jdbcType="VARCHAR"/>
        <result property="bankAcctCipher" column="bank_acct_cipher" jdbcType="VARCHAR"/>
        <result property="bankAcctDigest" column="bank_acct_digest" jdbcType="VARCHAR"/>
        <result property="bankAcctMask" column="bank_acct_mask" jdbcType="VARCHAR"/>
        <result property="swiftCode" column="swift_code" jdbcType="VARCHAR"/>
        <result property="bankCode" column="bank_code" jdbcType="VARCHAR"/>
        <result property="bankName" column="bank_name" jdbcType="VARCHAR"/>
        <result property="bankChineseName" column="bank_chinese_name" jdbcType="VARCHAR"/>
        <result property="txCode" column="tx_code" jdbcType="VARCHAR"/>
        <result property="businessType" column="business_type" jdbcType="VARCHAR"/>
        <result property="middleBusiCode" column="middle_busi_code" jdbcType="VARCHAR"/>
        <result property="productName" column="product_name" jdbcType="VARCHAR"/>
        <result property="productAbbr" column="product_abbr" jdbcType="VARCHAR"/>
        <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
        <result property="appAmt" column="app_amt" jdbcType="NUMERIC"/>
        <result property="appVol" column="app_vol" jdbcType="NUMERIC"/>
        <result property="currency" column="currency" jdbcType="VARCHAR"/>
        <result property="openDt" column="open_dt" jdbcType="VARCHAR"/>
        <result property="taTradeDt" column="ta_trade_dt" jdbcType="VARCHAR"/>
        <result property="appDt" column="app_dt" jdbcType="VARCHAR"/>
        <result property="appTm" column="app_tm" jdbcType="VARCHAR"/>
        <result property="orderStatus" column="order_status" jdbcType="VARCHAR"/>
        <result property="paymentTypeList" column="payment_type_list" jdbcType="VARCHAR"/>
        <result property="payStatus" column="pay_status" jdbcType="VARCHAR"/>
        <result property="payVoucherStatus" column="pay_voucher_status" jdbcType="VARCHAR"/>
        <result property="actualPayAmt" column="actual_pay_amt" jdbcType="NUMERIC"/>
        <result property="actualPayDt" column="actual_pay_dt" jdbcType="VARCHAR"/>
        <result property="actualPayTm" column="actual_pay_tm" jdbcType="VARCHAR"/>
        <result property="prebookDealNo" column="prebook_deal_no" jdbcType="VARCHAR"/>
        <result property="externalDealNo" column="external_deal_no" jdbcType="VARCHAR"/>
        <result property="firstBuyFlag" column="first_buy_flag" jdbcType="VARCHAR"/>
        <result property="isAgreeCurrencyExchange" column="is_agree_currency_exchange" jdbcType="VARCHAR"/>
        <result property="orderFormType" column="order_form_type" jdbcType="VARCHAR"/>
        <result property="supportPrebookFlag" column="support_prebook_flag" jdbcType="VARCHAR"/>
        <result property="memo" column="memo" jdbcType="VARCHAR"/>
        <result property="tradeChannel" column="trade_channel" jdbcType="VARCHAR"/>
        <result property="ipAddress" column="ip_address" jdbcType="VARCHAR"/>
        <result property="outletCode" column="outlet_code" jdbcType="VARCHAR"/>
        <result property="recStat" column="rec_stat" jdbcType="VARCHAR"/>
        <result property="createTimestamp" column="create_timestamp" jdbcType="TIMESTAMP"/>
        <result property="updateTimestamp" column="update_timestamp" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
        deal_no, 
        hk_cust_no, 
        cust_chinese_name, 
        id_type, 
        id_no_cipher, 
        id_no_digest, 
        id_no_mask, 
        invst_type, 
        qualification_type, 
        cust_risk_level, 
        cp_acct_no, 
        bank_acct_cipher, 
        bank_acct_digest, 
        bank_acct_mask, 
        swift_code, 
        bank_code, 
        bank_name, 
        bank_chinese_name, 
        tx_code, 
        business_type, 
        middle_busi_code, 
        product_name, 
        product_abbr, 
        product_code, 
        app_amt, 
        app_vol, 
        currency, 
        open_dt, 
        ta_trade_dt, 
        app_dt, 
        app_tm, 
        order_status, 
        payment_type_list, 
        pay_status, 
        pay_voucher_status, 
        actual_pay_amt, 
        actual_pay_dt, 
        actual_pay_tm, 
        prebook_deal_no, 
        external_deal_no, 
        first_buy_flag, 
        is_agree_currency_exchange, 
        order_form_type, 
        support_prebook_flag, 
        memo, 
        trade_channel, 
        ip_address, 
        outlet_code, 
        rec_stat, 
        create_timestamp, 
        update_timestamp
    </sql>

    <resultMap id="UnionResultMap" type="com.howbuy.dtms.manager.dao.bo.HwDealOrderBO">
        <result column="deal_no" jdbcType="BIGINT" property="dealNo" />
        <result column="hk_cust_no" jdbcType="VARCHAR" property="hkCustNo" />
        <result column="cust_chinese_name" jdbcType="VARCHAR" property="custChineseName" />
        <result column="id_type" jdbcType="VARCHAR" property="idType" />
        <result column="id_no_cipher" jdbcType="VARCHAR" property="idNoCipher" />
        <result column="id_no_digest" jdbcType="VARCHAR" property="idNoDigest" />
        <result column="id_no_mask" jdbcType="VARCHAR" property="idNoMask" />
        <result column="invst_type" jdbcType="CHAR" property="invstType" />
        <result column="qualification_type" jdbcType="VARCHAR" property="qualificationType" />
        <result column="cust_risk_level" jdbcType="VARCHAR" property="custRiskLevel" />
        <result column="tx_code" jdbcType="VARCHAR" property="txCode" />
        <result column="business_type" jdbcType="VARCHAR" property="businessType" />
        <result column="product_name" jdbcType="VARCHAR" property="productName" />
        <result column="product_code" jdbcType="VARCHAR" property="productCode" />
        <result column="app_dt" jdbcType="VARCHAR" property="appDt" />
        <result column="app_tm" jdbcType="VARCHAR" property="appTm" />
        <result column="order_status" jdbcType="VARCHAR" property="orderStatus" />
        <result column="prebook_deal_no" jdbcType="VARCHAR" property="prebookDealNo" />
        <result column="external_deal_no" jdbcType="VARCHAR" property="externalDealNo" />
        <result column="first_buy_flag" jdbcType="CHAR" property="firstBuyFlag" />
        <result column="is_agree_currency_exchange" jdbcType="CHAR" property="isAgreeCurrencyExchange" />
        <result column="order_form_type" jdbcType="CHAR" property="orderFormType" />
        <result column="support_prebook_flag" jdbcType="CHAR" property="supportPrebookFlag" />
        <result column="memo" jdbcType="VARCHAR" property="memo" />
        <result column="trade_channel" jdbcType="VARCHAR" property="tradeChannel" />
        <result column="ip_address" jdbcType="VARCHAR" property="ipAddress" />
        <result column="outlet_code" jdbcType="VARCHAR" property="outletCode" />
        <result column="deal_dtl_no" jdbcType="BIGINT" property="dealDtlNo" />
        <result column="cp_acct_no" jdbcType="VARCHAR" property="cpAcctNo" />
        <result column="bank_acct_cipher" jdbcType="VARCHAR" property="bankAcctCipher" />
        <result column="bank_acct_digest" jdbcType="VARCHAR" property="bankAcctDigest" />
        <result column="bank_acct_mask" jdbcType="VARCHAR" property="bankAcctMask" />
        <result column="swift_code" jdbcType="VARCHAR" property="swiftCode" />
        <result column="bank_code" jdbcType="VARCHAR" property="bankCode" />
        <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
        <result column="bank_chinese_name" jdbcType="VARCHAR" property="bankChineseName" />
        <result column="fund_code" jdbcType="VARCHAR" property="fundCode" />
        <result column="fund_name" jdbcType="VARCHAR" property="fundName" />
        <result column="fund_abbr" jdbcType="VARCHAR" property="fundAbbr" />
        <result column="main_fund_code" jdbcType="VARCHAR" property="mainFundCode" />
        <result column="fund_category" jdbcType="VARCHAR" property="fundCategory" />
        <result column="fund_risk_level" jdbcType="VARCHAR" property="fundRiskLevel" />
        <result column="payment_type_list" jdbcType="VARCHAR" property="paymentTypeList" />
        <result column="redeem_type" jdbcType="CHAR" property="redeemType" />
        <result column="redeem_direction_list" jdbcType="VARCHAR" property="redeemDirectionList" />
        <result column="middle_busi_code" jdbcType="VARCHAR" property="middleBusiCode" />
        <result column="app_amt" jdbcType="DECIMAL" property="appAmt" />
        <result column="net_app_amt" jdbcType="DECIMAL" property="netAppAmt" />
        <result column="app_vol" jdbcType="DECIMAL" property="appVol" />
        <result column="estimate_fee" jdbcType="DECIMAL" property="estimateFee" />
        <result column="prebook_discount" jdbcType="DECIMAL" property="prebookDiscount" />
        <result column="discount_rate" jdbcType="DECIMAL" property="discountRate" />
        <result column="fee_rate" jdbcType="DECIMAL" property="feeRate" />
        <result column="fee_cal_mode" jdbcType="CHAR" property="feeCalMode" />
        <result column="fee" jdbcType="DECIMAL" property="fee" />
        <result column="ack_amt" jdbcType="DECIMAL" property="ackAmt" />
        <result column="ack_vol" jdbcType="DECIMAL" property="ackVol" />
        <result column="ack_nav" jdbcType="DECIMAL" property="ackNav" />
        <result column="currency" jdbcType="VARCHAR" property="currency" />
        <result column="app_status" jdbcType="VARCHAR" property="appStatus" />
        <result column="ack_status" jdbcType="VARCHAR" property="ackStatus" />
        <result column="pay_status" jdbcType="VARCHAR" property="payStatus" />
        <result column="pay_voucher_status" jdbcType="VARCHAR" property="payVoucherStatus" />
        <result column="actual_pay_amt" jdbcType="DECIMAL" property="actualPayAmt" />
        <result column="actual_pay_dt" jdbcType="VARCHAR" property="actualPayDt" />
        <result column="actual_pay_tm" jdbcType="VARCHAR" property="actualPayTm" />
        <result column="transfer_price" jdbcType="DECIMAL" property="transferPrice" />
        <result column="fund_div_mode" jdbcType="CHAR" property="fundDivMode" />
        <result column="open_dt" jdbcType="VARCHAR" property="openDt" />
        <result column="pay_end_dt" jdbcType="VARCHAR" property="payEndDt" />
        <result column="pay_end_tm" jdbcType="VARCHAR" property="payEndTm" />
        <result column="ta_trade_dt" jdbcType="VARCHAR" property="taTradeDt" />
        <result column="ack_dt" jdbcType="VARCHAR" property="ackDt" />
        <result column="ta_ack_no" jdbcType="VARCHAR" property="taAckNo" />
        <result column="pre_submit_ta_dt" jdbcType="VARCHAR" property="preSubmitTaDt" />
        <result column="pre_submit_ta_tm" jdbcType="VARCHAR" property="preSubmitTaTm" />
        <result column="fund_tx_acct_no" jdbcType="VARCHAR" property="fundTxAcctNo" />
        <result column="create_timestamp" jdbcType="TIMESTAMP" property="createTimestamp" />
        <result column="update_timestamp" jdbcType="TIMESTAMP" property="updateTimestamp" />
    </resultMap>

    <sql id="Union_Column_List">
        <!--@mbg.generated-->
        a.deal_no, a.hk_cust_no, a.cust_chinese_name, a.id_type, a.id_no_cipher, a.id_no_digest, a.id_no_mask,
        a.invst_type, a.qualification_type, a.cust_risk_level, a.tx_code, a.business_type, a.product_name,
        a.product_code, a.app_dt, a.app_tm, a.order_status,
        a.prebook_deal_no, a.external_deal_no, a.first_buy_flag, a.is_agree_currency_exchange, a.order_form_type,
        a.support_prebook_flag, a.memo, a.trade_channel, a.ip_address, a.outlet_code,
        b.deal_dtl_no, b.cp_acct_no, a.bank_acct_cipher, a.bank_acct_digest, a.bank_acct_mask,
        a.swift_code, a.bank_code, a.bank_name, a.bank_chinese_name, b.fund_code, b.fund_name, b.fund_abbr, b.main_fund_code, b.fund_category,
        b.fund_risk_level, a.payment_type_list, b.redeem_type, b.redeem_direction_list, b.middle_busi_code, b.app_amt,
        b.net_app_amt, b.app_vol, b.estimate_fee, b.prebook_discount, b.discount_rate, b.fee_rate, b.fee_cal_mode,
        b.fee, b.ack_amt, b.ack_vol, b.ack_nav, b.currency, b.app_status, b.ack_status, a.pay_status, a.pay_voucher_status,
        a.actual_pay_amt, a.actual_pay_dt, a.actual_pay_tm, b.transfer_price, b.fund_div_mode, b.open_dt, b.pay_end_dt,
        b.pay_end_tm, b.ta_trade_dt, b.ack_dt, b.ta_ack_no, b.pre_submit_ta_dt, b.pre_submit_ta_tm, b.fund_tx_acct_no,
        b.create_timestamp,b.update_timestamp
    </sql>

    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_deal_order
        where id = #{id}
    </select>

    <!-- 查询符合条件的数据 -->
    <select id="selectBySelective" parameterType="com.howbuy.dtms.manager.dao.po.HwDealOrderPO"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_deal_order
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="dealNo != null">
                and deal_no = #{dealNo}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and hk_cust_no = #{hkCustNo}
            </if>
            <if test="custChineseName != null and custChineseName != ''">
                and cust_chinese_name = #{custChineseName}
            </if>
            <if test="idType != null and idType != ''">
                and id_type = #{idType}
            </if>
            <if test="idNoCipher != null and idNoCipher != ''">
                and id_no_cipher = #{idNoCipher}
            </if>
            <if test="idNoDigest != null and idNoDigest != ''">
                and id_no_digest = #{idNoDigest}
            </if>
            <if test="idNoMask != null and idNoMask != ''">
                and id_no_mask = #{idNoMask}
            </if>
            <if test="invstType != null and invstType != ''">
                and invst_type = #{invstType}
            </if>
            <if test="qualificationType != null and qualificationType != ''">
                and qualification_type = #{qualificationType}
            </if>
            <if test="custRiskLevel != null and custRiskLevel != ''">
                and cust_risk_level = #{custRiskLevel}
            </if>
            <if test="cpAcctNo != null and cpAcctNo != ''">
                and cp_acct_no = #{cpAcctNo}
            </if>
            <if test="bankAcctCipher != null and bankAcctCipher != ''">
                and bank_acct_cipher = #{bankAcctCipher}
            </if>
            <if test="bankAcctDigest != null and bankAcctDigest != ''">
                and bank_acct_digest = #{bankAcctDigest}
            </if>
            <if test="bankAcctMask != null and bankAcctMask != ''">
                and bank_acct_mask = #{bankAcctMask}
            </if>
            <if test="swiftCode != null and swiftCode != ''">
                and swift_code = #{swiftCode}
            </if>
            <if test="bankCode != null and bankCode != ''">
                and bank_code = #{bankCode}
            </if>
            <if test="bankName != null and bankName != ''">
                and bank_name = #{bankName}
            </if>
            <if test="bankChineseName != null and bankChineseName != ''">
                and bank_chinese_name = #{bankChineseName}
            </if>
            <if test="txCode != null and txCode != ''">
                and tx_code = #{txCode}
            </if>
            <if test="businessType != null and businessType != ''">
                and business_type = #{businessType}
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                and middle_busi_code = #{middleBusiCode}
            </if>
            <if test="productName != null and productName != ''">
                and product_name = #{productName}
            </if>
            <if test="productAbbr != null and productAbbr != ''">
                and product_abbr = #{productAbbr}
            </if>
            <if test="productCode != null and productCode != ''">
                and product_code = #{productCode}
            </if>
            <if test="appAmt != null">
                and app_amt = #{appAmt}
            </if>
            <if test="appVol != null">
                and app_vol = #{appVol}
            </if>
            <if test="currency != null and currency != ''">
                and currency = #{currency}
            </if>
            <if test="openDt != null and openDt != ''">
                and open_dt = #{openDt}
            </if>
            <if test="taTradeDt != null and taTradeDt != ''">
                and ta_trade_dt = #{taTradeDt}
            </if>
            <if test="appDt != null and appDt != ''">
                and app_dt = #{appDt}
            </if>
            <if test="appTm != null and appTm != ''">
                and app_tm = #{appTm}
            </if>
            <if test="orderStatus != null and orderStatus != ''">
                and order_status = #{orderStatus}
            </if>
            <if test="paymentTypeList != null and paymentTypeList != ''">
                and payment_type_list = #{paymentTypeList}
            </if>
            <if test="payStatus != null and payStatus != ''">
                and pay_status = #{payStatus}
            </if>
            <if test="payVoucherStatus != null and payVoucherStatus != ''">
                and pay_voucher_status = #{payVoucherStatus}
            </if>
            <if test="actualPayAmt != null">
                and actual_pay_amt = #{actualPayAmt}
            </if>
            <if test="actualPayDt != null and actualPayDt != ''">
                and actual_pay_dt = #{actualPayDt}
            </if>
            <if test="actualPayTm != null and actualPayTm != ''">
                and actual_pay_tm = #{actualPayTm}
            </if>
            <if test="prebookDealNo != null and prebookDealNo != ''">
                and prebook_deal_no = #{prebookDealNo}
            </if>
            <if test="externalDealNo != null and externalDealNo != ''">
                and external_deal_no = #{externalDealNo}
            </if>
            <if test="firstBuyFlag != null and firstBuyFlag != ''">
                and first_buy_flag = #{firstBuyFlag}
            </if>
            <if test="isAgreeCurrencyExchange != null and isAgreeCurrencyExchange != ''">
                and is_agree_currency_exchange = #{isAgreeCurrencyExchange}
            </if>
            <if test="orderFormType != null and orderFormType != ''">
                and order_form_type = #{orderFormType}
            </if>
            <if test="supportPrebookFlag != null and supportPrebookFlag != ''">
                and support_prebook_flag = #{supportPrebookFlag}
            </if>
            <if test="memo != null and memo != ''">
                and memo = #{memo}
            </if>
            <if test="tradeChannel != null and tradeChannel != ''">
                and trade_channel = #{tradeChannel}
            </if>
            <if test="ipAddress != null and ipAddress != ''">
                and ip_address = #{ipAddress}
            </if>
            <if test="outletCode != null and outletCode != ''">
                and outlet_code = #{outletCode}
            </if>
            <if test="recStat != null and recStat != ''">
                and rec_stat = #{recStat}
            </if>
            <if test="createTimestamp != null">
                and create_timestamp = #{createTimestamp}
            </if>
            <if test="updateTimestamp != null">
                and update_timestamp = #{updateTimestamp}
            </if>
        </where>
        order by id
    </select>


    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(*)
        from hw_deal_order
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="dealNo != null">
                and deal_no = #{dealNo}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and hk_cust_no = #{hkCustNo}
            </if>
            <if test="custChineseName != null and custChineseName != ''">
                and cust_chinese_name = #{custChineseName}
            </if>
            <if test="idType != null and idType != ''">
                and id_type = #{idType}
            </if>
            <if test="idNoCipher != null and idNoCipher != ''">
                and id_no_cipher = #{idNoCipher}
            </if>
            <if test="idNoDigest != null and idNoDigest != ''">
                and id_no_digest = #{idNoDigest}
            </if>
            <if test="idNoMask != null and idNoMask != ''">
                and id_no_mask = #{idNoMask}
            </if>
            <if test="invstType != null and invstType != ''">
                and invst_type = #{invstType}
            </if>
            <if test="qualificationType != null and qualificationType != ''">
                and qualification_type = #{qualificationType}
            </if>
            <if test="custRiskLevel != null and custRiskLevel != ''">
                and cust_risk_level = #{custRiskLevel}
            </if>
            <if test="cpAcctNo != null and cpAcctNo != ''">
                and cp_acct_no = #{cpAcctNo}
            </if>
            <if test="bankAcctCipher != null and bankAcctCipher != ''">
                and bank_acct_cipher = #{bankAcctCipher}
            </if>
            <if test="bankAcctDigest != null and bankAcctDigest != ''">
                and bank_acct_digest = #{bankAcctDigest}
            </if>
            <if test="bankAcctMask != null and bankAcctMask != ''">
                and bank_acct_mask = #{bankAcctMask}
            </if>
            <if test="swiftCode != null and swiftCode != ''">
                and swift_code = #{swiftCode}
            </if>
            <if test="bankCode != null and bankCode != ''">
                and bank_code = #{bankCode}
            </if>
            <if test="bankName != null and bankName != ''">
                and bank_name = #{bankName}
            </if>
            <if test="bankChineseName != null and bankChineseName != ''">
                and bank_chinese_name = #{bankChineseName}
            </if>
            <if test="txCode != null and txCode != ''">
                and tx_code = #{txCode}
            </if>
            <if test="businessType != null and businessType != ''">
                and business_type = #{businessType}
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                and middle_busi_code = #{middleBusiCode}
            </if>
            <if test="productName != null and productName != ''">
                and product_name = #{productName}
            </if>
            <if test="productAbbr != null and productAbbr != ''">
                and product_abbr = #{productAbbr}
            </if>
            <if test="productCode != null and productCode != ''">
                and product_code = #{productCode}
            </if>
            <if test="appAmt != null">
                and app_amt = #{appAmt}
            </if>
            <if test="appVol != null">
                and app_vol = #{appVol}
            </if>
            <if test="currency != null and currency != ''">
                and currency = #{currency}
            </if>
            <if test="openDt != null and openDt != ''">
                and open_dt = #{openDt}
            </if>
            <if test="taTradeDt != null and taTradeDt != ''">
                and ta_trade_dt = #{taTradeDt}
            </if>
            <if test="appDt != null and appDt != ''">
                and app_dt = #{appDt}
            </if>
            <if test="appTm != null and appTm != ''">
                and app_tm = #{appTm}
            </if>
            <if test="orderStatus != null and orderStatus != ''">
                and order_status = #{orderStatus}
            </if>
            <if test="paymentTypeList != null and paymentTypeList != ''">
                and payment_type_list = #{paymentTypeList}
            </if>
            <if test="payStatus != null and payStatus != ''">
                and pay_status = #{payStatus}
            </if>
            <if test="payVoucherStatus != null and payVoucherStatus != ''">
                and pay_voucher_status = #{payVoucherStatus}
            </if>
            <if test="actualPayAmt != null">
                and actual_pay_amt = #{actualPayAmt}
            </if>
            <if test="actualPayDt != null and actualPayDt != ''">
                and actual_pay_dt = #{actualPayDt}
            </if>
            <if test="actualPayTm != null and actualPayTm != ''">
                and actual_pay_tm = #{actualPayTm}
            </if>
            <if test="prebookDealNo != null and prebookDealNo != ''">
                and prebook_deal_no = #{prebookDealNo}
            </if>
            <if test="externalDealNo != null and externalDealNo != ''">
                and external_deal_no = #{externalDealNo}
            </if>
            <if test="firstBuyFlag != null and firstBuyFlag != ''">
                and first_buy_flag = #{firstBuyFlag}
            </if>
            <if test="isAgreeCurrencyExchange != null and isAgreeCurrencyExchange != ''">
                and is_agree_currency_exchange = #{isAgreeCurrencyExchange}
            </if>
            <if test="orderFormType != null and orderFormType != ''">
                and order_form_type = #{orderFormType}
            </if>
            <if test="supportPrebookFlag != null and supportPrebookFlag != ''">
                and support_prebook_flag = #{supportPrebookFlag}
            </if>
            <if test="memo != null and memo != ''">
                and memo = #{memo}
            </if>
            <if test="tradeChannel != null and tradeChannel != ''">
                and trade_channel = #{tradeChannel}
            </if>
            <if test="ipAddress != null and ipAddress != ''">
                and ip_address = #{ipAddress}
            </if>
            <if test="outletCode != null and outletCode != ''">
                and outlet_code = #{outletCode}
            </if>
            <if test="recStat != null and recStat != ''">
                and rec_stat = #{recStat}
            </if>
            <if test="createTimestamp != null">
                and create_timestamp = #{createTimestamp}
            </if>
            <if test="updateTimestamp != null">
                and update_timestamp = #{updateTimestamp}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into hw_deal_order(deal_no, hk_cust_no, cust_chinese_name, id_type, id_no_cipher, id_no_digest,
                                  id_no_mask, invst_type, qualification_type, cust_risk_level, cp_acct_no,
                                  bank_acct_cipher, bank_acct_digest, bank_acct_mask, swift_code, bank_code, bank_name,
                                  bank_chinese_name, tx_code, business_type, middle_busi_code, product_name,
                                  product_abbr, product_code, app_amt, app_vol, currency, open_dt, ta_trade_dt, app_dt,
                                  app_tm, order_status, payment_type_list, pay_status, pay_voucher_status,
                                  actual_pay_amt, actual_pay_dt, actual_pay_tm, prebook_deal_no, external_deal_no,
                                  first_buy_flag, is_agree_currency_exchange, order_form_type, support_prebook_flag,
                                  memo, trade_channel, ip_address, outlet_code, rec_stat, create_timestamp,
                                  update_timestamp)
        values (#{dealNo}, #{hkCustNo}, #{custChineseName}, #{idType}, #{idNoCipher}, #{idNoDigest}, #{idNoMask},
                #{invstType}, #{qualificationType}, #{custRiskLevel}, #{cpAcctNo}, #{bankAcctCipher}, #{bankAcctDigest},
                #{bankAcctMask}, #{swiftCode}, #{bankCode}, #{bankName}, #{bankChineseName}, #{txCode}, #{businessType},
                #{middleBusiCode}, #{productName}, #{productAbbr}, #{productCode}, #{appAmt}, #{appVol}, #{currency},
                #{openDt}, #{taTradeDt}, #{appDt}, #{appTm}, #{orderStatus}, #{paymentTypeList}, #{payStatus},
                #{payVoucherStatus}, #{actualPayAmt}, #{actualPayDt}, #{actualPayTm}, #{prebookDealNo},
                #{externalDealNo}, #{firstBuyFlag}, #{isAgreeCurrencyExchange}, #{orderFormType}, #{supportPrebookFlag},
                #{memo}, #{tradeChannel}, #{ipAddress}, #{outletCode}, #{recStat}, #{createTimestamp},
                #{updateTimestamp})
    </insert>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true">
        insert into hw_deal_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dealNo != null">
                deal_no,
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                hk_cust_no,
            </if>
            <if test="custChineseName != null and custChineseName != ''">
                cust_chinese_name,
            </if>
            <if test="idType != null and idType != ''">
                id_type,
            </if>
            <if test="idNoCipher != null and idNoCipher != ''">
                id_no_cipher,
            </if>
            <if test="idNoDigest != null and idNoDigest != ''">
                id_no_digest,
            </if>
            <if test="idNoMask != null and idNoMask != ''">
                id_no_mask,
            </if>
            <if test="invstType != null and invstType != ''">
                invst_type,
            </if>
            <if test="qualificationType != null and qualificationType != ''">
                qualification_type,
            </if>
            <if test="custRiskLevel != null and custRiskLevel != ''">
                cust_risk_level,
            </if>
            <if test="cpAcctNo != null and cpAcctNo != ''">
                cp_acct_no,
            </if>
            <if test="bankAcctCipher != null and bankAcctCipher != ''">
                bank_acct_cipher,
            </if>
            <if test="bankAcctDigest != null and bankAcctDigest != ''">
                bank_acct_digest,
            </if>
            <if test="bankAcctMask != null and bankAcctMask != ''">
                bank_acct_mask,
            </if>
            <if test="swiftCode != null and swiftCode != ''">
                swift_code,
            </if>
            <if test="bankCode != null and bankCode != ''">
                bank_code,
            </if>
            <if test="bankName != null and bankName != ''">
                bank_name,
            </if>
            <if test="bankChineseName != null and bankChineseName != ''">
                bank_chinese_name,
            </if>
            <if test="txCode != null and txCode != ''">
                tx_code,
            </if>
            <if test="businessType != null and businessType != ''">
                business_type,
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                middle_busi_code,
            </if>
            <if test="productName != null and productName != ''">
                product_name,
            </if>
            <if test="productAbbr != null and productAbbr != ''">
                product_abbr,
            </if>
            <if test="productCode != null and productCode != ''">
                product_code,
            </if>
            <if test="appAmt != null">
                app_amt,
            </if>
            <if test="appVol != null">
                app_vol,
            </if>
            <if test="currency != null and currency != ''">
                currency,
            </if>
            <if test="openDt != null and openDt != ''">
                open_dt,
            </if>
            <if test="taTradeDt != null and taTradeDt != ''">
                ta_trade_dt,
            </if>
            <if test="appDt != null and appDt != ''">
                app_dt,
            </if>
            <if test="appTm != null and appTm != ''">
                app_tm,
            </if>
            <if test="orderStatus != null and orderStatus != ''">
                order_status,
            </if>
            <if test="paymentTypeList != null and paymentTypeList != ''">
                payment_type_list,
            </if>
            <if test="payStatus != null and payStatus != ''">
                pay_status,
            </if>
            <if test="payVoucherStatus != null and payVoucherStatus != ''">
                pay_voucher_status,
            </if>
            <if test="actualPayAmt != null">
                actual_pay_amt,
            </if>
            <if test="actualPayDt != null and actualPayDt != ''">
                actual_pay_dt,
            </if>
            <if test="actualPayTm != null and actualPayTm != ''">
                actual_pay_tm,
            </if>
            <if test="prebookDealNo != null and prebookDealNo != ''">
                prebook_deal_no,
            </if>
            <if test="externalDealNo != null and externalDealNo != ''">
                external_deal_no,
            </if>
            <if test="firstBuyFlag != null and firstBuyFlag != ''">
                first_buy_flag,
            </if>
            <if test="isAgreeCurrencyExchange != null and isAgreeCurrencyExchange != ''">
                is_agree_currency_exchange,
            </if>
            <if test="orderFormType != null and orderFormType != ''">
                order_form_type,
            </if>
            <if test="supportPrebookFlag != null and supportPrebookFlag != ''">
                support_prebook_flag,
            </if>
            <if test="memo != null and memo != ''">
                memo,
            </if>
            <if test="tradeChannel != null and tradeChannel != ''">
                trade_channel,
            </if>
            <if test="ipAddress != null and ipAddress != ''">
                ip_address,
            </if>
            <if test="outletCode != null and outletCode != ''">
                outlet_code,
            </if>
            <if test="recStat != null and recStat != ''">
                rec_stat,
            </if>
            <if test="createTimestamp != null">
                create_timestamp,
            </if>
            <if test="updateTimestamp != null">
                update_timestamp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dealNo != null">
                #{dealNo},
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                #{hkCustNo},
            </if>
            <if test="custChineseName != null and custChineseName != ''">
                #{custChineseName},
            </if>
            <if test="idType != null and idType != ''">
                #{idType},
            </if>
            <if test="idNoCipher != null and idNoCipher != ''">
                #{idNoCipher},
            </if>
            <if test="idNoDigest != null and idNoDigest != ''">
                #{idNoDigest},
            </if>
            <if test="idNoMask != null and idNoMask != ''">
                #{idNoMask},
            </if>
            <if test="invstType != null and invstType != ''">
                #{invstType},
            </if>
            <if test="qualificationType != null and qualificationType != ''">
                #{qualificationType},
            </if>
            <if test="custRiskLevel != null and custRiskLevel != ''">
                #{custRiskLevel},
            </if>
            <if test="cpAcctNo != null and cpAcctNo != ''">
                #{cpAcctNo},
            </if>
            <if test="bankAcctCipher != null and bankAcctCipher != ''">
                #{bankAcctCipher},
            </if>
            <if test="bankAcctDigest != null and bankAcctDigest != ''">
                #{bankAcctDigest},
            </if>
            <if test="bankAcctMask != null and bankAcctMask != ''">
                #{bankAcctMask},
            </if>
            <if test="swiftCode != null and swiftCode != ''">
                #{swiftCode},
            </if>
            <if test="bankCode != null and bankCode != ''">
                #{bankCode},
            </if>
            <if test="bankName != null and bankName != ''">
                #{bankName},
            </if>
            <if test="bankChineseName != null and bankChineseName != ''">
                #{bankChineseName},
            </if>
            <if test="txCode != null and txCode != ''">
                #{txCode},
            </if>
            <if test="businessType != null and businessType != ''">
                #{businessType},
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                #{middleBusiCode},
            </if>
            <if test="productName != null and productName != ''">
                #{productName},
            </if>
            <if test="productAbbr != null and productAbbr != ''">
                #{productAbbr},
            </if>
            <if test="productCode != null and productCode != ''">
                #{productCode},
            </if>
            <if test="appAmt != null">
                #{appAmt},
            </if>
            <if test="appVol != null">
                #{appVol},
            </if>
            <if test="currency != null and currency != ''">
                #{currency},
            </if>
            <if test="openDt != null and openDt != ''">
                #{openDt},
            </if>
            <if test="taTradeDt != null and taTradeDt != ''">
                #{taTradeDt},
            </if>
            <if test="appDt != null and appDt != ''">
                #{appDt},
            </if>
            <if test="appTm != null and appTm != ''">
                #{appTm},
            </if>
            <if test="orderStatus != null and orderStatus != ''">
                #{orderStatus},
            </if>
            <if test="paymentTypeList != null and paymentTypeList != ''">
                #{paymentTypeList},
            </if>
            <if test="payStatus != null and payStatus != ''">
                #{payStatus},
            </if>
            <if test="payVoucherStatus != null and payVoucherStatus != ''">
                #{payVoucherStatus},
            </if>
            <if test="actualPayAmt != null">
                #{actualPayAmt},
            </if>
            <if test="actualPayDt != null and actualPayDt != ''">
                #{actualPayDt},
            </if>
            <if test="actualPayTm != null and actualPayTm != ''">
                #{actualPayTm},
            </if>
            <if test="prebookDealNo != null and prebookDealNo != ''">
                #{prebookDealNo},
            </if>
            <if test="externalDealNo != null and externalDealNo != ''">
                #{externalDealNo},
            </if>
            <if test="firstBuyFlag != null and firstBuyFlag != ''">
                #{firstBuyFlag},
            </if>
            <if test="isAgreeCurrencyExchange != null and isAgreeCurrencyExchange != ''">
                #{isAgreeCurrencyExchange},
            </if>
            <if test="orderFormType != null and orderFormType != ''">
                #{orderFormType},
            </if>
            <if test="supportPrebookFlag != null and supportPrebookFlag != ''">
                #{supportPrebookFlag},
            </if>
            <if test="memo != null and memo != ''">
                #{memo},
            </if>
            <if test="tradeChannel != null and tradeChannel != ''">
                #{tradeChannel},
            </if>
            <if test="ipAddress != null and ipAddress != ''">
                #{ipAddress},
            </if>
            <if test="outletCode != null and outletCode != ''">
                #{outletCode},
            </if>
            <if test="recStat != null and recStat != ''">
                #{recStat},
            </if>
            <if test="createTimestamp != null">
                #{createTimestamp},
            </if>
            <if test="updateTimestamp != null">
                #{updateTimestamp},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into hw_deal_order(deal_no, hk_cust_no, cust_chinese_name, id_type, id_no_cipher, id_no_digest,
        id_no_mask, invst_type, qualification_type, cust_risk_level, cp_acct_no, bank_acct_cipher, bank_acct_digest,
        bank_acct_mask, swift_code, bank_code, bank_name, bank_chinese_name, tx_code, business_type, middle_busi_code,
        product_name, product_abbr, product_code, app_amt, app_vol, currency, open_dt, ta_trade_dt, app_dt, app_tm,
        order_status, payment_type_list, pay_status, pay_voucher_status, actual_pay_amt, actual_pay_dt, actual_pay_tm,
        prebook_deal_no, external_deal_no, first_buy_flag, is_agree_currency_exchange, order_form_type,
        support_prebook_flag, memo, trade_channel, ip_address, outlet_code, rec_stat, create_timestamp,
        update_timestamp)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.dealNo}, #{entity.hkCustNo}, #{entity.custChineseName}, #{entity.idType}, #{entity.idNoCipher},
            #{entity.idNoDigest}, #{entity.idNoMask}, #{entity.invstType}, #{entity.qualificationType},
            #{entity.custRiskLevel}, #{entity.cpAcctNo}, #{entity.bankAcctCipher}, #{entity.bankAcctDigest},
            #{entity.bankAcctMask}, #{entity.swiftCode}, #{entity.bankCode}, #{entity.bankName},
            #{entity.bankChineseName}, #{entity.txCode}, #{entity.businessType}, #{entity.middleBusiCode},
            #{entity.productName}, #{entity.productAbbr}, #{entity.productCode}, #{entity.appAmt}, #{entity.appVol},
            #{entity.currency}, #{entity.openDt}, #{entity.taTradeDt}, #{entity.appDt}, #{entity.appTm},
            #{entity.orderStatus}, #{entity.paymentTypeList}, #{entity.payStatus}, #{entity.payVoucherStatus},
            #{entity.actualPayAmt}, #{entity.actualPayDt}, #{entity.actualPayTm}, #{entity.prebookDealNo},
            #{entity.externalDealNo}, #{entity.firstBuyFlag}, #{entity.isAgreeCurrencyExchange},
            #{entity.orderFormType}, #{entity.supportPrebookFlag}, #{entity.memo}, #{entity.tradeChannel},
            #{entity.ipAddress}, #{entity.outletCode}, #{entity.recStat}, #{entity.createTimestamp},
            #{entity.updateTimestamp})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update hw_deal_order
        <set>
            <if test="dealNo != null">
                deal_no = #{dealNo},
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                hk_cust_no = #{hkCustNo},
            </if>
            <if test="custChineseName != null and custChineseName != ''">
                cust_chinese_name = #{custChineseName},
            </if>
            <if test="idType != null and idType != ''">
                id_type = #{idType},
            </if>
            <if test="idNoCipher != null and idNoCipher != ''">
                id_no_cipher = #{idNoCipher},
            </if>
            <if test="idNoDigest != null and idNoDigest != ''">
                id_no_digest = #{idNoDigest},
            </if>
            <if test="idNoMask != null and idNoMask != ''">
                id_no_mask = #{idNoMask},
            </if>
            <if test="invstType != null and invstType != ''">
                invst_type = #{invstType},
            </if>
            <if test="qualificationType != null and qualificationType != ''">
                qualification_type = #{qualificationType},
            </if>
            <if test="custRiskLevel != null and custRiskLevel != ''">
                cust_risk_level = #{custRiskLevel},
            </if>
            <if test="cpAcctNo != null and cpAcctNo != ''">
                cp_acct_no = #{cpAcctNo},
            </if>
            <if test="bankAcctCipher != null and bankAcctCipher != ''">
                bank_acct_cipher = #{bankAcctCipher},
            </if>
            <if test="bankAcctDigest != null and bankAcctDigest != ''">
                bank_acct_digest = #{bankAcctDigest},
            </if>
            <if test="bankAcctMask != null and bankAcctMask != ''">
                bank_acct_mask = #{bankAcctMask},
            </if>
            <if test="swiftCode != null and swiftCode != ''">
                swift_code = #{swiftCode},
            </if>
            <if test="bankCode != null and bankCode != ''">
                bank_code = #{bankCode},
            </if>
            <if test="bankName != null and bankName != ''">
                bank_name = #{bankName},
            </if>
            <if test="bankChineseName != null and bankChineseName != ''">
                bank_chinese_name = #{bankChineseName},
            </if>
            <if test="txCode != null and txCode != ''">
                tx_code = #{txCode},
            </if>
            <if test="businessType != null and businessType != ''">
                business_type = #{businessType},
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                middle_busi_code = #{middleBusiCode},
            </if>
            <if test="productName != null and productName != ''">
                product_name = #{productName},
            </if>
            <if test="productAbbr != null and productAbbr != ''">
                product_abbr = #{productAbbr},
            </if>
            <if test="productCode != null and productCode != ''">
                product_code = #{productCode},
            </if>
            <if test="appAmt != null">
                app_amt = #{appAmt},
            </if>
            <if test="appVol != null">
                app_vol = #{appVol},
            </if>
            <if test="currency != null and currency != ''">
                currency = #{currency},
            </if>
            <if test="openDt != null and openDt != ''">
                open_dt = #{openDt},
            </if>
            <if test="taTradeDt != null and taTradeDt != ''">
                ta_trade_dt = #{taTradeDt},
            </if>
            <if test="appDt != null and appDt != ''">
                app_dt = #{appDt},
            </if>
            <if test="appTm != null and appTm != ''">
                app_tm = #{appTm},
            </if>
            <if test="orderStatus != null and orderStatus != ''">
                order_status = #{orderStatus},
            </if>
            <if test="paymentTypeList != null and paymentTypeList != ''">
                payment_type_list = #{paymentTypeList},
            </if>
            <if test="payStatus != null and payStatus != ''">
                pay_status = #{payStatus},
            </if>
            <if test="payVoucherStatus != null and payVoucherStatus != ''">
                pay_voucher_status = #{payVoucherStatus},
            </if>
            <if test="actualPayAmt != null">
                actual_pay_amt = #{actualPayAmt},
            </if>
            <if test="actualPayDt != null and actualPayDt != ''">
                actual_pay_dt = #{actualPayDt},
            </if>
            <if test="actualPayTm != null and actualPayTm != ''">
                actual_pay_tm = #{actualPayTm},
            </if>
            <if test="prebookDealNo != null and prebookDealNo != ''">
                prebook_deal_no = #{prebookDealNo},
            </if>
            <if test="externalDealNo != null and externalDealNo != ''">
                external_deal_no = #{externalDealNo},
            </if>
            <if test="firstBuyFlag != null and firstBuyFlag != ''">
                first_buy_flag = #{firstBuyFlag},
            </if>
            <if test="isAgreeCurrencyExchange != null and isAgreeCurrencyExchange != ''">
                is_agree_currency_exchange = #{isAgreeCurrencyExchange},
            </if>
            <if test="orderFormType != null and orderFormType != ''">
                order_form_type = #{orderFormType},
            </if>
            <if test="supportPrebookFlag != null and supportPrebookFlag != ''">
                support_prebook_flag = #{supportPrebookFlag},
            </if>
            <if test="memo != null and memo != ''">
                memo = #{memo},
            </if>
            <if test="tradeChannel != null and tradeChannel != ''">
                trade_channel = #{tradeChannel},
            </if>
            <if test="ipAddress != null and ipAddress != ''">
                ip_address = #{ipAddress},
            </if>
            <if test="outletCode != null and outletCode != ''">
                outlet_code = #{outletCode},
            </if>
            <if test="recStat != null and recStat != ''">
                rec_stat = #{recStat},
            </if>
            <if test="createTimestamp != null">
                create_timestamp = #{createTimestamp},
            </if>
            <if test="updateTimestamp != null">
                update_timestamp = #{updateTimestamp},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from hw_deal_order
        where id = #{id}
    </delete>

    <!--分页查询，根据主键ID升序排序-->
    <select id="selectWithPage" parameterType="com.howbuy.dtms.manager.dao.query.HwDealOrderQuery"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_deal_order
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="dealNo != null">
                and deal_no = #{dealNo}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and hk_cust_no = #{hkCustNo}
            </if>
            <if test="custChineseName != null and custChineseName != ''">
                and cust_chinese_name = #{custChineseName}
            </if>
            <if test="idType != null and idType != ''">
                and id_type = #{idType}
            </if>
            <if test="idNoCipher != null and idNoCipher != ''">
                and id_no_cipher = #{idNoCipher}
            </if>
            <if test="idNoDigest != null and idNoDigest != ''">
                and id_no_digest = #{idNoDigest}
            </if>
            <if test="idNoMask != null and idNoMask != ''">
                and id_no_mask = #{idNoMask}
            </if>
            <if test="invstType != null and invstType != ''">
                and invst_type = #{invstType}
            </if>
            <if test="qualificationType != null and qualificationType != ''">
                and qualification_type = #{qualificationType}
            </if>
            <if test="custRiskLevel != null and custRiskLevel != ''">
                and cust_risk_level = #{custRiskLevel}
            </if>
            <if test="cpAcctNo != null and cpAcctNo != ''">
                and cp_acct_no = #{cpAcctNo}
            </if>
            <if test="bankAcctCipher != null and bankAcctCipher != ''">
                and bank_acct_cipher = #{bankAcctCipher}
            </if>
            <if test="bankAcctDigest != null and bankAcctDigest != ''">
                and bank_acct_digest = #{bankAcctDigest}
            </if>
            <if test="bankAcctMask != null and bankAcctMask != ''">
                and bank_acct_mask = #{bankAcctMask}
            </if>
            <if test="swiftCode != null and swiftCode != ''">
                and swift_code = #{swiftCode}
            </if>
            <if test="bankCode != null and bankCode != ''">
                and bank_code = #{bankCode}
            </if>
            <if test="bankName != null and bankName != ''">
                and bank_name = #{bankName}
            </if>
            <if test="bankChineseName != null and bankChineseName != ''">
                and bank_chinese_name = #{bankChineseName}
            </if>
            <if test="txCode != null and txCode != ''">
                and tx_code = #{txCode}
            </if>
            <if test="businessType != null and businessType != ''">
                and business_type = #{businessType}
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                and middle_busi_code = #{middleBusiCode}
            </if>
            <if test="productName != null and productName != ''">
                and product_name = #{productName}
            </if>
            <if test="productAbbr != null and productAbbr != ''">
                and product_abbr = #{productAbbr}
            </if>
            <if test="productCode != null and productCode != ''">
                and product_code = #{productCode}
            </if>
            <if test="appAmt != null">
                and app_amt = #{appAmt}
            </if>
            <if test="appVol != null">
                and app_vol = #{appVol}
            </if>
            <if test="currency != null and currency != ''">
                and currency = #{currency}
            </if>
            <if test="openDt != null and openDt != ''">
                and open_dt = #{openDt}
            </if>
            <if test="taTradeDt != null and taTradeDt != ''">
                and ta_trade_dt = #{taTradeDt}
            </if>
            <if test="appDt != null and appDt != ''">
                and app_dt = #{appDt}
            </if>
            <if test="appTm != null and appTm != ''">
                and app_tm = #{appTm}
            </if>
            <if test="orderStatus != null and orderStatus != ''">
                and order_status = #{orderStatus}
            </if>
            <if test="paymentTypeList != null and paymentTypeList != ''">
                and payment_type_list = #{paymentTypeList}
            </if>
            <if test="payStatus != null and payStatus != ''">
                and pay_status = #{payStatus}
            </if>
            <if test="payVoucherStatus != null and payVoucherStatus != ''">
                and pay_voucher_status = #{payVoucherStatus}
            </if>
            <if test="actualPayAmt != null">
                and actual_pay_amt = #{actualPayAmt}
            </if>
            <if test="actualPayDt != null and actualPayDt != ''">
                and actual_pay_dt = #{actualPayDt}
            </if>
            <if test="actualPayTm != null and actualPayTm != ''">
                and actual_pay_tm = #{actualPayTm}
            </if>
            <if test="prebookDealNo != null and prebookDealNo != ''">
                and prebook_deal_no = #{prebookDealNo}
            </if>
            <if test="externalDealNo != null and externalDealNo != ''">
                and external_deal_no = #{externalDealNo}
            </if>
            <if test="firstBuyFlag != null and firstBuyFlag != ''">
                and first_buy_flag = #{firstBuyFlag}
            </if>
            <if test="isAgreeCurrencyExchange != null and isAgreeCurrencyExchange != ''">
                and is_agree_currency_exchange = #{isAgreeCurrencyExchange}
            </if>
            <if test="orderFormType != null and orderFormType != ''">
                and order_form_type = #{orderFormType}
            </if>
            <if test="supportPrebookFlag != null and supportPrebookFlag != ''">
                and support_prebook_flag = #{supportPrebookFlag}
            </if>
            <if test="memo != null and memo != ''">
                and memo = #{memo}
            </if>
            <if test="tradeChannel != null and tradeChannel != ''">
                and trade_channel = #{tradeChannel}
            </if>
            <if test="ipAddress != null and ipAddress != ''">
                and ip_address = #{ipAddress}
            </if>
            <if test="outletCode != null and outletCode != ''">
                and outlet_code = #{outletCode}
            </if>
            <if test="recStat != null and recStat != ''">
                and rec_stat = #{recStat}
            </if>
            <if test="createTimestamp != null">
                and create_timestamp = #{createTimestamp}
            </if>
            <if test="updateTimestamp != null">
                and update_timestamp = #{updateTimestamp}
            </if>
            <if test="idList != null and idList.size > 0">
                and id in
                <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and rec_stat = '0'
        </where>
        order by id
    </select>

    <!--分页查询，根据主键ID升序排序-->
    <select id="queryFundsReceivedWithPage" parameterType="com.howbuy.dtms.manager.dao.query.FundsReceivedQuery"
            resultMap="UnionResultMap">
        select
        <include refid="Union_Column_List"/>
        from hw_deal_order a
        inner join hw_deal_order_dtl b on a.deal_no = b.deal_no
        where a.rec_stat = '0'
        and b.rec_stat = '0'
        AND (app_dt != '' AND app_dt IS NOT NULL)
        AND (app_tm != '' AND app_tm IS NOT NULL)
        <if test="hkCustNo != null and hkCustNo != ''">
            and a.hk_cust_no = #{hkCustNo}
        </if>
        <if test="payVoucherStatusList != null and payVoucherStatusList.size > 0">
            and a.pay_voucher_status in
            <foreach collection="payVoucherStatusList" item="payVoucherStatus" open="(" close=")" separator=",">
                #{payVoucherStatus}
            </foreach>
        </if>
        <if test="middleBusiCodelist != null and middleBusiCodelist.size > 0">
            and b.middle_busi_code in
            <foreach collection="middleBusiCodelist" item="middleBusiCode" open="(" close=")" separator=",">
                #{middleBusiCode}
            </foreach>
        </if>
        <if test="dealNo != null">
            and a.deal_no = #{dealNo}
        </if>
        <if test="payStatusList != null and payStatusList.size > 0">
            and a.pay_status in
            <foreach collection="payStatusList" item="payStatus" open="(" close=")" separator=",">
                #{payStatus}
            </foreach>
        </if>
        <if test="fundCodeList != null and fundCodeList.size() > 0">
            and b.fund_code in
            <foreach collection="fundCodeList" item="fundCode" open="(" close=")" separator=",">
                #{fundCode}
            </foreach>
        </if>
        <if test="orderStatusList != null and orderStatusList.size > 0">
            and a.order_status in
            <foreach collection="orderStatusList" item="orderStatus" open="(" close=")" separator=",">
                #{orderStatus}
            </foreach>
        </if>
        <if test="payTypeList != null and payTypeList.size > 0">
            and a.payment_type_list in
            <foreach collection="payTypeList" item="payType" open="(" close=")" separator=",">
                #{payType}
            </foreach>
        </if>
        <if test="payEndDtStart != null and payEndDtStart != ''">
            and b.pay_end_dt >= #{payEndDtStart}
        </if>
        <if test="payEndDtEnd != null and payEndDtEnd != ''">
            and b.pay_end_dt <![CDATA[ <= ]]> #{payEndDtEnd}
        </if>
        <if test="preSubmitTaDtStart != null and preSubmitTaDtStart != ''">
            and b.pre_submit_ta_dt >= #{preSubmitTaDtStart}
        </if>
        <if test="preSubmitTaDtEnd != null and preSubmitTaDtEnd != ''">
            and b.pre_submit_ta_dt <![CDATA[ <= ]]> #{preSubmitTaDtEnd}
        </if>
        <if test="appDtStart != null and appDtStart != ''">
            and a.app_dt >= #{appDtStart}
        </if>
        <if test="appDtEnd != null and appDtEnd != ''">
            and a.app_dt <![CDATA[ <= ]]> #{appDtEnd}
        </if>
        ORDER BY
        STR_TO_DATE(CONCAT(app_dt, app_tm), '%Y%m%d%H%i%s') DESC
    </select>

    <select id="getBoByDealNo" resultMap="UnionResultMap">
        select
        <include refid="Union_Column_List"/>
        from hw_deal_order a
        inner join hw_deal_order_dtl b on a.deal_no = b.deal_no
        where a.deal_no = #{dealNo}
        and a.rec_stat = '0'
        and b.rec_stat = '0'
    </select>
    <select id="queryTradeOrderByPreDealNo"
            resultType="com.howbuy.dtms.manager.dao.po.HwDealOrderPO">
        select
        a.deal_no
        from hw_deal_order a
        where a.prebook_deal_no = #{prebookDealNo}
        and a.rec_stat = '0'
    </select>

    <select id="queryByDealNoList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_deal_order
        where rec_stat = '0'
        <if test="dealNoList != null and dealNoList.size > 0">
            and deal_no in
            <foreach collection="dealNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryByExternalDealNoList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_deal_order
        where rec_stat = '0'
        <if test="externalDealNoList != null and externalDealNoList.size > 0">
            and external_deal_no in
            <foreach collection="externalDealNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>

