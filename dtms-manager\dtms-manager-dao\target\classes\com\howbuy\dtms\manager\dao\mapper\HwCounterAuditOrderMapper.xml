<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.HwCounterAuditOrderMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderPO">
    <!--@mbg.generated-->
    <!--@Table hw_counter_audit_order-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_serial_no" jdbcType="VARCHAR" property="appSerialNo" />
    <result column="hk_cust_no" jdbcType="VARCHAR" property="hkCustNo" />
    <result column="counter_biz_type" jdbcType="VARCHAR" property="counterBizType" />
    <result column="id_no_digest" jdbcType="VARCHAR" property="idNoDigest" />
    <result column="cust_chinese_name" jdbcType="VARCHAR" property="custChineseName" />
    <result column="fund_code" jdbcType="VARCHAR" property="fundCode" />
    <result column="fund_abbr" jdbcType="VARCHAR" property="fundAbbr" />
    <result column="app_amt" jdbcType="DECIMAL" property="appAmt" />
    <result column="app_vol" jdbcType="DECIMAL" property="appVol" />
    <result column="app_dt" jdbcType="VARCHAR" property="appDt" />
    <result column="app_tm" jdbcType="VARCHAR" property="appTm" />
    <result column="trade_channel" jdbcType="VARCHAR" property="tradeChannel" />
    <result column="outlet_code" jdbcType="VARCHAR" property="outletCode" />
    <result column="audit_pass_timestamp" jdbcType="TIMESTAMP" property="auditPassTimestamp" />
    <result column="audit_remark" jdbcType="VARCHAR" property="auditRemark" />
    <result column="audit_status" jdbcType="VARCHAR" property="auditStatus" />
    <result column="revisit_person" jdbcType="VARCHAR" property="revisitPerson" />
    <result column="revisit_timestamp" jdbcType="TIMESTAMP" property="revisitTimestamp" />
    <result column="revisit_file_url" jdbcType="VARCHAR" property="revisitFileUrl" />
    <result column="revisit_file_name" jdbcType="VARCHAR" property="revisitFileName" />
    <result column="revisit_reason" jdbcType="VARCHAR" property="revisitReason" />
    <result column="revisit" jdbcType="VARCHAR" property="revisit" />
    <result column="sub_total_amt" jdbcType="DECIMAL" property="subTotalAmt" />
    <result column="cum_paid_total_amt" jdbcType="DECIMAL" property="cumPaidTotalAmt" />
    <result column="rec_stat" jdbcType="VARCHAR" property="recStat" />
    <result column="invst_type" jdbcType="VARCHAR" property="invstType" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="reviewer" jdbcType="VARCHAR" property="reviewer" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_timestamp" jdbcType="TIMESTAMP" property="createTimestamp" />
    <result column="update_timestamp" jdbcType="TIMESTAMP" property="updateTimestamp" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, app_serial_no, hk_cust_no, counter_biz_type, id_no_digest, cust_chinese_name, 
    fund_code, fund_abbr, app_amt, app_vol, app_dt, app_tm, trade_channel, outlet_code, 
    audit_pass_timestamp, audit_remark, audit_status, revisit_person, revisit_timestamp, 
    revisit_file_url, revisit_file_name, revisit_reason, revisit, sub_total_amt, cum_paid_total_amt, 
    rec_stat, invst_type, creator, reviewer, modifier, create_timestamp, update_timestamp
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from hw_counter_audit_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from hw_counter_audit_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hw_counter_audit_order (app_serial_no, hk_cust_no, counter_biz_type, 
      id_no_digest, cust_chinese_name, fund_code, 
      fund_abbr, app_amt, app_vol, 
      app_dt, app_tm, trade_channel, 
      outlet_code, audit_pass_timestamp, audit_remark, 
      audit_status, revisit_person, revisit_timestamp, 
      revisit_file_url, revisit_file_name, revisit_reason, 
      revisit, sub_total_amt, cum_paid_total_amt, 
      rec_stat, invst_type, creator, 
      reviewer, modifier, create_timestamp, 
      update_timestamp)
    values (#{appSerialNo,jdbcType=VARCHAR}, #{hkCustNo,jdbcType=VARCHAR}, #{counterBizType,jdbcType=VARCHAR}, 
      #{idNoDigest,jdbcType=VARCHAR}, #{custChineseName,jdbcType=VARCHAR}, #{fundCode,jdbcType=VARCHAR}, 
      #{fundAbbr,jdbcType=VARCHAR}, #{appAmt,jdbcType=DECIMAL}, #{appVol,jdbcType=DECIMAL}, 
      #{appDt,jdbcType=VARCHAR}, #{appTm,jdbcType=VARCHAR}, #{tradeChannel,jdbcType=VARCHAR}, 
      #{outletCode,jdbcType=VARCHAR}, #{auditPassTimestamp,jdbcType=TIMESTAMP}, #{auditRemark,jdbcType=VARCHAR}, 
      #{auditStatus,jdbcType=VARCHAR}, #{revisitPerson,jdbcType=VARCHAR}, #{revisitTimestamp,jdbcType=TIMESTAMP}, 
      #{revisitFileUrl,jdbcType=VARCHAR}, #{revisitFileName,jdbcType=VARCHAR}, #{revisitReason,jdbcType=VARCHAR}, 
      #{revisit,jdbcType=VARCHAR}, #{subTotalAmt,jdbcType=DECIMAL}, #{cumPaidTotalAmt,jdbcType=DECIMAL}, 
      #{recStat,jdbcType=VARCHAR}, #{invstType,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{reviewer,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, #{createTimestamp,jdbcType=TIMESTAMP}, 
      #{updateTimestamp,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hw_counter_audit_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appSerialNo != null">
        app_serial_no,
      </if>
      <if test="hkCustNo != null">
        hk_cust_no,
      </if>
      <if test="counterBizType != null">
        counter_biz_type,
      </if>
      <if test="idNoDigest != null">
        id_no_digest,
      </if>
      <if test="custChineseName != null">
        cust_chinese_name,
      </if>
      <if test="fundCode != null">
        fund_code,
      </if>
      <if test="fundAbbr != null">
        fund_abbr,
      </if>
      <if test="appAmt != null">
        app_amt,
      </if>
      <if test="appVol != null">
        app_vol,
      </if>
      <if test="appDt != null">
        app_dt,
      </if>
      <if test="appTm != null">
        app_tm,
      </if>
      <if test="tradeChannel != null">
        trade_channel,
      </if>
      <if test="outletCode != null">
        outlet_code,
      </if>
      <if test="auditPassTimestamp != null">
        audit_pass_timestamp,
      </if>
      <if test="auditRemark != null">
        audit_remark,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="revisitPerson != null">
        revisit_person,
      </if>
      <if test="revisitTimestamp != null">
        revisit_timestamp,
      </if>
      <if test="revisitFileUrl != null">
        revisit_file_url,
      </if>
      <if test="revisitFileName != null">
        revisit_file_name,
      </if>
      <if test="revisitReason != null">
        revisit_reason,
      </if>
      <if test="revisit != null">
        revisit,
      </if>
      <if test="subTotalAmt != null">
        sub_total_amt,
      </if>
      <if test="cumPaidTotalAmt != null">
        cum_paid_total_amt,
      </if>
      <if test="recStat != null">
        rec_stat,
      </if>
      <if test="invstType != null">
        invst_type,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="reviewer != null">
        reviewer,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="createTimestamp != null">
        create_timestamp,
      </if>
      <if test="updateTimestamp != null">
        update_timestamp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appSerialNo != null">
        #{appSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="hkCustNo != null">
        #{hkCustNo,jdbcType=VARCHAR},
      </if>
      <if test="counterBizType != null">
        #{counterBizType,jdbcType=VARCHAR},
      </if>
      <if test="idNoDigest != null">
        #{idNoDigest,jdbcType=VARCHAR},
      </if>
      <if test="custChineseName != null">
        #{custChineseName,jdbcType=VARCHAR},
      </if>
      <if test="fundCode != null">
        #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="fundAbbr != null">
        #{fundAbbr,jdbcType=VARCHAR},
      </if>
      <if test="appAmt != null">
        #{appAmt,jdbcType=DECIMAL},
      </if>
      <if test="appVol != null">
        #{appVol,jdbcType=DECIMAL},
      </if>
      <if test="appDt != null">
        #{appDt,jdbcType=VARCHAR},
      </if>
      <if test="appTm != null">
        #{appTm,jdbcType=VARCHAR},
      </if>
      <if test="tradeChannel != null">
        #{tradeChannel,jdbcType=VARCHAR},
      </if>
      <if test="outletCode != null">
        #{outletCode,jdbcType=VARCHAR},
      </if>
      <if test="auditPassTimestamp != null">
        #{auditPassTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="auditRemark != null">
        #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="revisitPerson != null">
        #{revisitPerson,jdbcType=VARCHAR},
      </if>
      <if test="revisitTimestamp != null">
        #{revisitTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="revisitFileUrl != null">
        #{revisitFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="revisitFileName != null">
        #{revisitFileName,jdbcType=VARCHAR},
      </if>
      <if test="revisitReason != null">
        #{revisitReason,jdbcType=VARCHAR},
      </if>
      <if test="revisit != null">
        #{revisit,jdbcType=VARCHAR},
      </if>
      <if test="subTotalAmt != null">
        #{subTotalAmt,jdbcType=DECIMAL},
      </if>
      <if test="cumPaidTotalAmt != null">
        #{cumPaidTotalAmt,jdbcType=DECIMAL},
      </if>
      <if test="recStat != null">
        #{recStat,jdbcType=VARCHAR},
      </if>
      <if test="invstType != null">
        #{invstType,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="reviewer != null">
        #{reviewer,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTimestamp != null">
        #{updateTimestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderPO">
    <!--@mbg.generated-->
    update hw_counter_audit_order
    <set>
      <if test="appSerialNo != null">
        app_serial_no = #{appSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="hkCustNo != null">
        hk_cust_no = #{hkCustNo,jdbcType=VARCHAR},
      </if>
      <if test="counterBizType != null">
        counter_biz_type = #{counterBizType,jdbcType=VARCHAR},
      </if>
      <if test="idNoDigest != null">
        id_no_digest = #{idNoDigest,jdbcType=VARCHAR},
      </if>
      <if test="custChineseName != null">
        cust_chinese_name = #{custChineseName,jdbcType=VARCHAR},
      </if>
      <if test="fundCode != null">
        fund_code = #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="fundAbbr != null">
        fund_abbr = #{fundAbbr,jdbcType=VARCHAR},
      </if>
      <if test="appAmt != null">
        app_amt = #{appAmt,jdbcType=DECIMAL},
      </if>
      <if test="appVol != null">
        app_vol = #{appVol,jdbcType=DECIMAL},
      </if>
      <if test="appDt != null">
        app_dt = #{appDt,jdbcType=VARCHAR},
      </if>
      <if test="appTm != null">
        app_tm = #{appTm,jdbcType=VARCHAR},
      </if>
      <if test="tradeChannel != null">
        trade_channel = #{tradeChannel,jdbcType=VARCHAR},
      </if>
      <if test="outletCode != null">
        outlet_code = #{outletCode,jdbcType=VARCHAR},
      </if>
      <if test="auditPassTimestamp != null">
        audit_pass_timestamp = #{auditPassTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="auditRemark != null">
        audit_remark = #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="revisitPerson != null">
        revisit_person = #{revisitPerson,jdbcType=VARCHAR},
      </if>
      <if test="revisitTimestamp != null">
        revisit_timestamp = #{revisitTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="revisitFileUrl != null">
        revisit_file_url = #{revisitFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="revisitFileName != null">
        revisit_file_name = #{revisitFileName,jdbcType=VARCHAR},
      </if>
      <if test="revisitReason != null">
        revisit_reason = #{revisitReason,jdbcType=VARCHAR},
      </if>
      <if test="revisit != null">
        revisit = #{revisit,jdbcType=VARCHAR},
      </if>
      <if test="subTotalAmt != null">
        sub_total_amt = #{subTotalAmt,jdbcType=DECIMAL},
      </if>
      <if test="cumPaidTotalAmt != null">
        cum_paid_total_amt = #{cumPaidTotalAmt,jdbcType=DECIMAL},
      </if>
      <if test="recStat != null">
        rec_stat = #{recStat,jdbcType=VARCHAR},
      </if>
      <if test="invstType != null">
        invst_type = #{invstType,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="reviewer != null">
        reviewer = #{reviewer,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTimestamp != null">
        update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderPO">
    <!--@mbg.generated-->
    update hw_counter_audit_order
    set app_serial_no = #{appSerialNo,jdbcType=VARCHAR},
      hk_cust_no = #{hkCustNo,jdbcType=VARCHAR},
      counter_biz_type = #{counterBizType,jdbcType=VARCHAR},
      id_no_digest = #{idNoDigest,jdbcType=VARCHAR},
      cust_chinese_name = #{custChineseName,jdbcType=VARCHAR},
      fund_code = #{fundCode,jdbcType=VARCHAR},
      fund_abbr = #{fundAbbr,jdbcType=VARCHAR},
      app_amt = #{appAmt,jdbcType=DECIMAL},
      app_vol = #{appVol,jdbcType=DECIMAL},
      app_dt = #{appDt,jdbcType=VARCHAR},
      app_tm = #{appTm,jdbcType=VARCHAR},
      trade_channel = #{tradeChannel,jdbcType=VARCHAR},
      outlet_code = #{outletCode,jdbcType=VARCHAR},
      audit_pass_timestamp = #{auditPassTimestamp,jdbcType=TIMESTAMP},
      audit_remark = #{auditRemark,jdbcType=VARCHAR},
      audit_status = #{auditStatus,jdbcType=VARCHAR},
      revisit_person = #{revisitPerson,jdbcType=VARCHAR},
      revisit_timestamp = #{revisitTimestamp,jdbcType=TIMESTAMP},
      revisit_file_url = #{revisitFileUrl,jdbcType=VARCHAR},
      revisit_file_name = #{revisitFileName,jdbcType=VARCHAR},
      revisit_reason = #{revisitReason,jdbcType=VARCHAR},
      revisit = #{revisit,jdbcType=VARCHAR},
      sub_total_amt = #{subTotalAmt,jdbcType=DECIMAL},
      cum_paid_total_amt = #{cumPaidTotalAmt,jdbcType=DECIMAL},
      rec_stat = #{recStat,jdbcType=VARCHAR},
      invst_type = #{invstType,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      reviewer = #{reviewer,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
      update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>