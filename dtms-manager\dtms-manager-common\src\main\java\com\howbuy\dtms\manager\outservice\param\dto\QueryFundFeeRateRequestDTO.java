/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.manager.outservice.param.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * @description: 费率查询接口
 * @author: jinqing.rao
 * @date: 2025/5/28 9:46
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryFundFeeRateRequestDTO {

    /**
     * 基金代码, 必填
     */
    private String fundCode;

    /**
     * 业务代码，必填
     * 020-认购、022-申购、024-赎回
     */
    private String busiCode;

    /**
     * 投资者类型，必填
     * 0-机构，1-个人，2-产品
     */
    private String invstType;

    /**
     * 收取对象， 必填
     * 1-好买、2-管理人
     */
    private String collectRecipient;

}
