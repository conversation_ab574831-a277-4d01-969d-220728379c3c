package com.howbuy.dtms.manager.outservice.settle.dto;

import com.howbuy.dtms.settle.client.manager.hwcusttradeappsettle.HwCustTradeAppSettleVO;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:23:43+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class HwCustTradeAppSettleDTOConvertMapperImpl implements HwCustTradeAppSettleDTOConvertMapper {

    @Override
    public HwCustTradeAppSettleDTO convert(HwCustTradeAppSettleVO vo) {
        if ( vo == null ) {
            return null;
        }

        HwCustTradeAppSettleDTO hwCustTradeAppSettleDTO = new HwCustTradeAppSettleDTO();

        hwCustTradeAppSettleDTO.setAppAmt( vo.getAppAmt() );
        hwCustTradeAppSettleDTO.setAppDt( vo.getAppDt() );
        hwCustTradeAppSettleDTO.setAppSheetNo( vo.getAppSheetNo() );
        hwCustTradeAppSettleDTO.setAppStatus( vo.getAppStatus() );
        hwCustTradeAppSettleDTO.setAppTm( vo.getAppTm() );
        hwCustTradeAppSettleDTO.setAppVol( vo.getAppVol() );
        hwCustTradeAppSettleDTO.setBusiCode( vo.getBusiCode() );
        hwCustTradeAppSettleDTO.setCpAcctNo( vo.getCpAcctNo() );
        hwCustTradeAppSettleDTO.setCreateTime( vo.getCreateTime() );
        hwCustTradeAppSettleDTO.setCurrency( vo.getCurrency() );
        hwCustTradeAppSettleDTO.setCustNameCn( vo.getCustNameCn() );
        hwCustTradeAppSettleDTO.setDealDtlNo( vo.getDealDtlNo() );
        hwCustTradeAppSettleDTO.setDiscountRate( vo.getDiscountRate() );
        hwCustTradeAppSettleDTO.setEbrokerId( vo.getEbrokerId() );
        hwCustTradeAppSettleDTO.setEsitmateFee( vo.getEsitmateFee() );
        hwCustTradeAppSettleDTO.setFeeRate( vo.getFeeRate() );
        hwCustTradeAppSettleDTO.setFundAbbr( vo.getFundAbbr() );
        hwCustTradeAppSettleDTO.setFundCode( vo.getFundCode() );
        hwCustTradeAppSettleDTO.setFundDivMode( vo.getFundDivMode() );
        hwCustTradeAppSettleDTO.setFundManCode( vo.getFundManCode() );
        hwCustTradeAppSettleDTO.setFundManName( vo.getFundManName() );
        hwCustTradeAppSettleDTO.setHkCustNo( vo.getHkCustNo() );
        hwCustTradeAppSettleDTO.setId( vo.getId() );
        hwCustTradeAppSettleDTO.setIdNoDigest( vo.getIdNoDigest() );
        hwCustTradeAppSettleDTO.setIdNoMask( vo.getIdNoMask() );
        hwCustTradeAppSettleDTO.setInvstType( vo.getInvstType() );
        hwCustTradeAppSettleDTO.setIsDeleted( vo.getIsDeleted() );
        hwCustTradeAppSettleDTO.setMainFundCode( vo.getMainFundCode() );
        hwCustTradeAppSettleDTO.setMemo( vo.getMemo() );
        hwCustTradeAppSettleDTO.setNetAppAmt( vo.getNetAppAmt() );
        hwCustTradeAppSettleDTO.setOpenDt( vo.getOpenDt() );
        hwCustTradeAppSettleDTO.setPreSubmitTaDt( vo.getPreSubmitTaDt() );
        hwCustTradeAppSettleDTO.setPreSubmitTaTm( vo.getPreSubmitTaTm() );
        hwCustTradeAppSettleDTO.setRedeemType( vo.getRedeemType() );
        hwCustTradeAppSettleDTO.setSerialNumber( vo.getSerialNumber() );
        hwCustTradeAppSettleDTO.setSettleReceivedDt( vo.getSettleReceivedDt() );
        hwCustTradeAppSettleDTO.setSettleStatus( vo.getSettleStatus() );
        hwCustTradeAppSettleDTO.setShareRegDt( vo.getShareRegDt() );
        hwCustTradeAppSettleDTO.setSubmitTaDt( vo.getSubmitTaDt() );
        hwCustTradeAppSettleDTO.setUpdateTime( vo.getUpdateTime() );
        hwCustTradeAppSettleDTO.setVersion( vo.getVersion() );

        return hwCustTradeAppSettleDTO;
    }
}
