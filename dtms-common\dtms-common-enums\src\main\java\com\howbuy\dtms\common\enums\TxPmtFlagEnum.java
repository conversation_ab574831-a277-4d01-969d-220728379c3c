/*
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

/**
 * @description: 交易支付标记枚举
 * <AUTHOR>
 * @date 2025-07-02 16:04:22
 * @since JDK 1.8
 */
public enum TxPmtFlagEnum {

    /**
     * 无需付款
     */
    NO_NEED_PAY("0", "无需付款"),
    
    /**
     * 未付款
     */
    UN_PAY("1", "未付款"),
    
    /**
     * 付款成功
     */
    PAY_SUCCESS("2", "付款成功"),
    
    /**
     * 付款失败
     */
    PAY_FAIL("3", "付款失败"),
    
    /**
     * 付款中
     */
    PAYING("4", "付款中");

    /**
     * 枚举code
     */
    private final String code;
    
    /**
     * 枚举的中文意义
     */
    private final String desc;

    TxPmtFlagEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     * @param code 枚举code
     * @return 枚举对象
     */
    public static TxPmtFlagEnum getEnumByCode(String code) {
        for (TxPmtFlagEnum txPmtFlagEnum : TxPmtFlagEnum.values()) {
            if (txPmtFlagEnum.getCode().equals(code)) {
                return txPmtFlagEnum;
            }
        }
        return null;
    }

    /**
     * 根据code获取描述
     * @param code 枚举code
     * @return 描述
     */
    public static String getDescByCode(String code) {
        TxPmtFlagEnum txPmtFlagEnum = getEnumByCode(code);
        return txPmtFlagEnum != null ? txPmtFlagEnum.getDesc() : null;
    }
}
