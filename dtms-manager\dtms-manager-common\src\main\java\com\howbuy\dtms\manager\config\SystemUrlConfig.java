package com.howbuy.dtms.manager.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 系统URL配置
 * @date 2024/8/5 10:21
 * @since JDK 1.8
 */
@Getter
@Component
public class SystemUrlConfig {

    @Value("${url.dtmsManagerRemote}")
    private String dtmsManagerRemoteUrl;

    @Value("${url.dtmsProductWeb}")
    private String dtmsProductWebUrl;

    @Value("${url.hkAccConsole}")
    private String hkAccConsoleUrl;

    @Value("${url.pdfGenerateUrl}")
    private String pdfGenerateUrl;

    @Value("${url.hkFinUi}")
    private String hkFinUi;

}
