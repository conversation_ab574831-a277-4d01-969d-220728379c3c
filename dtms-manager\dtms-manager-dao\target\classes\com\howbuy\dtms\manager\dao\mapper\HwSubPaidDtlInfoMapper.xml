<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.HwSubPaidDtlInfoMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwSubPaidDtlInfoPO">
    <!--@mbg.generated-->
    <!--@Table hw_sub_paid_dtl_info-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dtl_serial_no" jdbcType="BIGINT" property="dtlSerialNo" />
    <result column="serial_no" jdbcType="BIGINT" property="serialNo" />
    <result column="deal_no" jdbcType="BIGINT" property="dealNo" />
    <result column="hk_cust_no" jdbcType="VARCHAR" property="hkCustNo" />
    <result column="fund_tx_acct_no" jdbcType="VARCHAR" property="fundTxAcctNo" />
    <result column="fund_code" jdbcType="VARCHAR" property="fundCode" />
    <result column="middle_busi_code" jdbcType="VARCHAR" property="middleBusiCode" />
    <result column="sub_amt" jdbcType="DECIMAL" property="subAmt" />
    <result column="paid_amt" jdbcType="DECIMAL" property="paidAmt" />
    <result column="rec_stat" jdbcType="VARCHAR" property="recStat" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="create_timestamp" jdbcType="TIMESTAMP" property="createTimestamp" />
    <result column="update_timestamp" jdbcType="TIMESTAMP" property="updateTimestamp" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, dtl_serial_no, serial_no, deal_no, hk_cust_no, fund_tx_acct_no, fund_code, middle_busi_code, 
    sub_amt, paid_amt, rec_stat, version, create_timestamp, update_timestamp
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from hw_sub_paid_dtl_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from hw_sub_paid_dtl_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.HwSubPaidDtlInfoPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hw_sub_paid_dtl_info (dtl_serial_no, serial_no, deal_no, 
      hk_cust_no, fund_tx_acct_no, fund_code, 
      middle_busi_code, sub_amt, paid_amt, 
      rec_stat, version, create_timestamp, 
      update_timestamp)
    values (#{dtlSerialNo,jdbcType=BIGINT}, #{serialNo,jdbcType=BIGINT}, #{dealNo,jdbcType=BIGINT}, 
      #{hkCustNo,jdbcType=VARCHAR}, #{fundTxAcctNo,jdbcType=VARCHAR}, #{fundCode,jdbcType=VARCHAR}, 
      #{middleBusiCode,jdbcType=VARCHAR}, #{subAmt,jdbcType=DECIMAL}, #{paidAmt,jdbcType=DECIMAL}, 
      #{recStat,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{createTimestamp,jdbcType=TIMESTAMP}, 
      #{updateTimestamp,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.HwSubPaidDtlInfoPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hw_sub_paid_dtl_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dtlSerialNo != null">
        dtl_serial_no,
      </if>
      <if test="serialNo != null">
        serial_no,
      </if>
      <if test="dealNo != null">
        deal_no,
      </if>
      <if test="hkCustNo != null">
        hk_cust_no,
      </if>
      <if test="fundTxAcctNo != null">
        fund_tx_acct_no,
      </if>
      <if test="fundCode != null">
        fund_code,
      </if>
      <if test="middleBusiCode != null">
        middle_busi_code,
      </if>
      <if test="subAmt != null">
        sub_amt,
      </if>
      <if test="paidAmt != null">
        paid_amt,
      </if>
      <if test="recStat != null">
        rec_stat,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createTimestamp != null">
        create_timestamp,
      </if>
      <if test="updateTimestamp != null">
        update_timestamp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dtlSerialNo != null">
        #{dtlSerialNo,jdbcType=BIGINT},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=BIGINT},
      </if>
      <if test="dealNo != null">
        #{dealNo,jdbcType=BIGINT},
      </if>
      <if test="hkCustNo != null">
        #{hkCustNo,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAcctNo != null">
        #{fundTxAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="fundCode != null">
        #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="middleBusiCode != null">
        #{middleBusiCode,jdbcType=VARCHAR},
      </if>
      <if test="subAmt != null">
        #{subAmt,jdbcType=DECIMAL},
      </if>
      <if test="paidAmt != null">
        #{paidAmt,jdbcType=DECIMAL},
      </if>
      <if test="recStat != null">
        #{recStat,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createTimestamp != null">
        #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTimestamp != null">
        #{updateTimestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.dtms.manager.dao.po.HwSubPaidDtlInfoPO">
    <!--@mbg.generated-->
    update hw_sub_paid_dtl_info
    <set>
      <if test="dtlSerialNo != null">
        dtl_serial_no = #{dtlSerialNo,jdbcType=BIGINT},
      </if>
      <if test="serialNo != null">
        serial_no = #{serialNo,jdbcType=BIGINT},
      </if>
      <if test="dealNo != null">
        deal_no = #{dealNo,jdbcType=BIGINT},
      </if>
      <if test="hkCustNo != null">
        hk_cust_no = #{hkCustNo,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAcctNo != null">
        fund_tx_acct_no = #{fundTxAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="fundCode != null">
        fund_code = #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="middleBusiCode != null">
        middle_busi_code = #{middleBusiCode,jdbcType=VARCHAR},
      </if>
      <if test="subAmt != null">
        sub_amt = #{subAmt,jdbcType=DECIMAL},
      </if>
      <if test="paidAmt != null">
        paid_amt = #{paidAmt,jdbcType=DECIMAL},
      </if>
      <if test="recStat != null">
        rec_stat = #{recStat,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createTimestamp != null">
        create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTimestamp != null">
        update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.dtms.manager.dao.po.HwSubPaidDtlInfoPO">
    <!--@mbg.generated-->
    update hw_sub_paid_dtl_info
    set dtl_serial_no = #{dtlSerialNo,jdbcType=BIGINT},
      serial_no = #{serialNo,jdbcType=BIGINT},
      deal_no = #{dealNo,jdbcType=BIGINT},
      hk_cust_no = #{hkCustNo,jdbcType=VARCHAR},
      fund_tx_acct_no = #{fundTxAcctNo,jdbcType=VARCHAR},
      fund_code = #{fundCode,jdbcType=VARCHAR},
      middle_busi_code = #{middleBusiCode,jdbcType=VARCHAR},
      sub_amt = #{subAmt,jdbcType=DECIMAL},
      paid_amt = #{paidAmt,jdbcType=DECIMAL},
      rec_stat = #{recStat,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
      update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>