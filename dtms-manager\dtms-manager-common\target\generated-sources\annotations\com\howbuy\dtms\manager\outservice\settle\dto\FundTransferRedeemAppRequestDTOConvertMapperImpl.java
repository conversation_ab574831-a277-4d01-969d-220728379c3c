package com.howbuy.dtms.manager.outservice.settle.dto;

import com.howbuy.dtms.settle.client.facade.trade.transfer.FundTransferRedeemAppRequest;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:23:43+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class FundTransferRedeemAppRequestDTOConvertMapperImpl implements FundTransferRedeemAppRequestDTOConvertMapper {

    @Override
    public FundTransferRedeemAppRequest convert(FundTransferRedeemAppRequestDTO dto) {
        if ( dto == null ) {
            return null;
        }

        FundTransferRedeemAppRequest fundTransferRedeemAppRequest = new FundTransferRedeemAppRequest();

        fundTransferRedeemAppRequest.setTraceId( dto.getTraceId() );
        fundTransferRedeemAppRequest.setAppAmt( dto.getAppAmt() );
        fundTransferRedeemAppRequest.setAppDt( dto.getAppDt() );
        fundTransferRedeemAppRequest.setAppStatus( dto.getAppStatus() );
        fundTransferRedeemAppRequest.setAppTm( dto.getAppTm() );
        fundTransferRedeemAppRequest.setAppVol( dto.getAppVol() );
        fundTransferRedeemAppRequest.setCpAcctNo( dto.getCpAcctNo() );
        fundTransferRedeemAppRequest.setCurrency( dto.getCurrency() );
        fundTransferRedeemAppRequest.setDealDtlNo( dto.getDealDtlNo() );
        fundTransferRedeemAppRequest.setFeeRate( dto.getFeeRate() );
        fundTransferRedeemAppRequest.setFundCode( dto.getFundCode() );
        fundTransferRedeemAppRequest.setFundTxAcctNo( dto.getFundTxAcctNo() );
        fundTransferRedeemAppRequest.setHkCustNo( dto.getHkCustNo() );
        fundTransferRedeemAppRequest.setMiddleOrderNo( dto.getMiddleOrderNo() );
        fundTransferRedeemAppRequest.setOpenDt( dto.getOpenDt() );
        fundTransferRedeemAppRequest.setPreSubmitTaDt( dto.getPreSubmitTaDt() );
        fundTransferRedeemAppRequest.setPreSubmitTaTm( dto.getPreSubmitTaTm() );
        fundTransferRedeemAppRequest.setRedeemType( dto.getRedeemType() );
        fundTransferRedeemAppRequest.setSerialNumber( dto.getSerialNumber() );
        fundTransferRedeemAppRequest.setShareRegDt( dto.getShareRegDt() );
        fundTransferRedeemAppRequest.setSubmitTaDt( dto.getSubmitTaDt() );

        return fundTransferRedeemAppRequest;
    }
}
