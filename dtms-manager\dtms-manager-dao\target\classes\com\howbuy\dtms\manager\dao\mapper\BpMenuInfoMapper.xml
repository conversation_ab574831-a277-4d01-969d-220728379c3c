<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.BpMenuInfoMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.BpMenuInfoPO">
        <!--@Table bp_menu_info-->
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="menuId" column="menu_id" jdbcType="VARCHAR"/>
        <result property="parentMenuId" column="parent_menu_id" jdbcType="VARCHAR"/>
        <result property="menuName" column="menu_name" jdbcType="VARCHAR"/>
        <result property="systemId" column="system_id" jdbcType="VARCHAR"/>
        <result property="privCode" column="priv_code" jdbcType="VARCHAR"/>
        <result property="routerPath" column="router_path" jdbcType="VARCHAR"/>
        <result property="componentPath" column="component_path" jdbcType="VARCHAR"/>
        <result property="functionUrl" column="function_url" jdbcType="VARCHAR"/>
        <result property="visibleFlag" column="visible_flag" jdbcType="VARCHAR"/>
        <result property="menuLevel" column="menu_level" jdbcType="INTEGER"/>
        <result property="orderIndex" column="order_index" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
        <result property="menuSystemId" column="menu_system_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
        menu_id, 
        parent_menu_id, 
        menu_name, 
        system_id, 
        priv_code, 
        router_path, 
        component_path, 
        function_url, 
        visible_flag, 
        menu_level, 
        order_index, 
        creator, 
        modifier, 
        create_time, 
        update_time, 
        is_deleted, 
        version, 
        menu_system_id
    </sql>

    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bp_menu_info
        where id = #{id}
    </select>

    <!-- 查询符合条件的数据 -->
    <select id="selectBySelective" parameterType="com.howbuy.dtms.manager.dao.po.BpMenuInfoPO"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bp_menu_info
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="menuId != null and menuId != ''">
                and menu_id = #{menuId}
            </if>
            <if test="parentMenuId != null and parentMenuId != ''">
                and parent_menu_id = #{parentMenuId}
            </if>
            <if test="menuName != null and menuName != ''">
                and menu_name = #{menuName}
            </if>
            <if test="systemId != null and systemId != ''">
                and system_id = #{systemId}
            </if>
            <if test="privCode != null and privCode != ''">
                and priv_code = #{privCode}
            </if>
            <if test="routerPath != null and routerPath != ''">
                and router_path = #{routerPath}
            </if>
            <if test="componentPath != null and componentPath != ''">
                and component_path = #{componentPath}
            </if>
            <if test="functionUrl != null and functionUrl != ''">
                and function_url = #{functionUrl}
            </if>
            <if test="visibleFlag != null and visibleFlag != ''">
                and visible_flag = #{visibleFlag}
            </if>
            <if test="menuLevel != null">
                and menu_level = #{menuLevel}
            </if>
            <if test="orderIndex != null">
                and order_index = #{orderIndex}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="modifier != null and modifier != ''">
                and modifier = #{modifier}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
            <if test="version != null">
                and version = #{version}
            </if>
            <if test="menuSystemId != null and menuSystemId != ''">
                and menu_system_id = #{menuSystemId}
            </if>
        </where>
        order by menu_level, order_index, id
    </select>


    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(*)
        from bp_menu_info
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="menuId != null and menuId != ''">
                and menu_id = #{menuId}
            </if>
            <if test="parentMenuId != null and parentMenuId != ''">
                and parent_menu_id = #{parentMenuId}
            </if>
            <if test="menuName != null and menuName != ''">
                and menu_name = #{menuName}
            </if>
            <if test="systemId != null and systemId != ''">
                and system_id = #{systemId}
            </if>
            <if test="privCode != null and privCode != ''">
                and priv_code = #{privCode}
            </if>
            <if test="routerPath != null and routerPath != ''">
                and router_path = #{routerPath}
            </if>
            <if test="componentPath != null and componentPath != ''">
                and component_path = #{componentPath}
            </if>
            <if test="functionUrl != null and functionUrl != ''">
                and function_url = #{functionUrl}
            </if>
            <if test="visibleFlag != null and visibleFlag != ''">
                and visible_flag = #{visibleFlag}
            </if>
            <if test="menuLevel != null">
                and menu_level = #{menuLevel}
            </if>
            <if test="orderIndex != null">
                and order_index = #{orderIndex}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="modifier != null and modifier != ''">
                and modifier = #{modifier}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
            <if test="version != null">
                and version = #{version}
            </if>
            <if test="menuSystemId != null and menuSystemId != ''">
                and menu_system_id = #{menuSystemId}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into bp_menu_info(menu_id, parent_menu_id, menu_name, system_id, priv_code, router_path, component_path,
                                 function_url, visible_flag, menu_level, order_index, creator, modifier, create_time,
                                 update_time, is_deleted, version, menu_system_id)
        values (#{menuId}, #{parentMenuId}, #{menuName}, #{systemId}, #{privCode}, #{routerPath}, #{componentPath},
                #{functionUrl}, #{visibleFlag}, #{menuLevel}, #{orderIndex}, #{creator}, #{modifier}, #{createTime},
                #{updateTime}, #{isDeleted}, #{version}, #{menuSystemId})
    </insert>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true">
        insert into bp_menu_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="menuId != null and menuId != ''">
                menu_id,
            </if>
            <if test="parentMenuId != null and parentMenuId != ''">
                parent_menu_id,
            </if>
            <if test="menuName != null and menuName != ''">
                menu_name,
            </if>
            <if test="systemId != null and systemId != ''">
                system_id,
            </if>
            <if test="privCode != null and privCode != ''">
                priv_code,
            </if>
            <if test="routerPath != null and routerPath != ''">
                router_path,
            </if>
            <if test="componentPath != null and componentPath != ''">
                component_path,
            </if>
            <if test="functionUrl != null and functionUrl != ''">
                function_url,
            </if>
            <if test="visibleFlag != null and visibleFlag != ''">
                visible_flag,
            </if>
            <if test="menuLevel != null">
                menu_level,
            </if>
            <if test="orderIndex != null">
                order_index,
            </if>
            <if test="creator != null and creator != ''">
                creator,
            </if>
            <if test="modifier != null and modifier != ''">
                modifier,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="menuSystemId != null and menuSystemId != ''">
                menu_system_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="menuId != null and menuId != ''">
                #{menuId},
            </if>
            <if test="parentMenuId != null and parentMenuId != ''">
                #{parentMenuId},
            </if>
            <if test="menuName != null and menuName != ''">
                #{menuName},
            </if>
            <if test="systemId != null and systemId != ''">
                #{systemId},
            </if>
            <if test="privCode != null and privCode != ''">
                #{privCode},
            </if>
            <if test="routerPath != null and routerPath != ''">
                #{routerPath},
            </if>
            <if test="componentPath != null and componentPath != ''">
                #{componentPath},
            </if>
            <if test="functionUrl != null and functionUrl != ''">
                #{functionUrl},
            </if>
            <if test="visibleFlag != null and visibleFlag != ''">
                #{visibleFlag},
            </if>
            <if test="menuLevel != null">
                #{menuLevel},
            </if>
            <if test="orderIndex != null">
                #{orderIndex},
            </if>
            <if test="creator != null and creator != ''">
                #{creator},
            </if>
            <if test="modifier != null and modifier != ''">
                #{modifier},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="isDeleted != null">
                #{isDeleted},
            </if>
            <if test="version != null">
                #{version},
            </if>
            <if test="menuSystemId != null and menuSystemId != ''">
                #{menuSystemId},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into bp_menu_info(menu_id, parent_menu_id, menu_name, system_id, priv_code, router_path, component_path,
        function_url, visible_flag, menu_level, order_index, creator, modifier, create_time, update_time, is_deleted,
        version, menu_system_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.menuId}, #{entity.parentMenuId}, #{entity.menuName}, #{entity.systemId}, #{entity.privCode},
            #{entity.routerPath}, #{entity.componentPath}, #{entity.functionUrl}, #{entity.visibleFlag},
            #{entity.menuLevel}, #{entity.orderIndex}, #{entity.creator}, #{entity.modifier}, #{entity.createTime},
            #{entity.updateTime}, #{entity.isDeleted}, #{entity.version}, #{entity.menuSystemId})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update bp_menu_info
        <set>
            <if test="menuId != null and menuId != ''">
                menu_id = #{menuId},
            </if>
            <if test="parentMenuId != null and parentMenuId != ''">
                parent_menu_id = #{parentMenuId},
            </if>
            <if test="menuName != null and menuName != ''">
                menu_name = #{menuName},
            </if>
            <if test="systemId != null and systemId != ''">
                system_id = #{systemId},
            </if>
            <if test="privCode != null and privCode != ''">
                priv_code = #{privCode},
            </if>
            <if test="routerPath != null and routerPath != ''">
                router_path = #{routerPath},
            </if>
            <if test="componentPath != null and componentPath != ''">
                component_path = #{componentPath},
            </if>
            <if test="functionUrl != null and functionUrl != ''">
                function_url = #{functionUrl},
            </if>
            <if test="visibleFlag != null and visibleFlag != ''">
                visible_flag = #{visibleFlag},
            </if>
            <if test="menuLevel != null">
                menu_level = #{menuLevel},
            </if>
            <if test="orderIndex != null">
                order_index = #{orderIndex},
            </if>
            <if test="creator != null and creator != ''">
                creator = #{creator},
            </if>
            <if test="modifier != null and modifier != ''">
                modifier = #{modifier},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="menuSystemId != null and menuSystemId != ''">
                menu_system_id = #{menuSystemId},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from bp_menu_info
        where id = #{id}
    </delete>

    <!--分页查询，根据主键ID升序排序-->
    <select id="selectWithPage" parameterType="com.howbuy.dtms.manager.dao.query.BpMenuInfoQuery"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bp_menu_info
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="menuId != null and menuId != ''">
                and menu_id = #{menuId}
            </if>
            <if test="parentMenuId != null and parentMenuId != ''">
                and parent_menu_id = #{parentMenuId}
            </if>
            <if test="menuName != null and menuName != ''">
                and menu_name = #{menuName}
            </if>
            <if test="systemId != null and systemId != ''">
                and system_id = #{systemId}
            </if>
            <if test="privCode != null and privCode != ''">
                and priv_code = #{privCode}
            </if>
            <if test="routerPath != null and routerPath != ''">
                and router_path = #{routerPath}
            </if>
            <if test="componentPath != null and componentPath != ''">
                and component_path = #{componentPath}
            </if>
            <if test="functionUrl != null and functionUrl != ''">
                and function_url = #{functionUrl}
            </if>
            <if test="visibleFlag != null and visibleFlag != ''">
                and visible_flag = #{visibleFlag}
            </if>
            <if test="menuLevel != null">
                and menu_level = #{menuLevel}
            </if>
            <if test="orderIndex != null">
                and order_index = #{orderIndex}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="modifier != null and modifier != ''">
                and modifier = #{modifier}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="menuSystemId != null and menuSystemId != ''">
                and menu_system_id = #{menuSystemId}
            </if>
            <if test="idList != null and idList.size > 0">
                and id in
                <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and is_deleted = 0
        </where>
        order by menu_level, order_index, id
    </select>
</mapper>

