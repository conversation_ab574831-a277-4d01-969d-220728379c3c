package com.howbuy.dtms.manager.counter.vo.payment;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.howbuy.dtms.manager.excel.BaseExportVo;
import com.howbuy.dtms.manager.excel.converter.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 支付对账结果导出VO
 *
 * <AUTHOR>
 * @date 2025-07-23 16:24:39
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ColumnWidth(25)
public class PaymentCheckResultExportVO extends BaseExportVo {

    private static final long serialVersionUID = 1L;

    /**
     * 支付订单号
     */
    @ExcelProperty(value = "支付订单号")
    private String pmtDealNo;

    /**
     * 订单号
     */
    @ExcelProperty(value = "订单号")
    private String dealNo;

    /**
     * 中台业务代码
     */
    @ExcelProperty(value = "中台业务代码", converter = MiddleBusiCodeConverter.class)
    private String middleBusiCode;

    /**
     * 支付方式列表
     */
    @ExcelProperty(value = "支付方式", converter = PayMethodConverter.class)
    private String paymentTypeList;

    /**
     * 香港客户号
     */
    @ExcelProperty(value = "香港客户号")
    private String hkCustNo;

    /**
     * 客户姓名
     */
    @ExcelProperty(value = "客户姓名")
    private String custName;

    /**
     * 资金账号
     */
    @ExcelProperty(value = "资金账号")
    private String cpAcctNo;

    /**
     * 基金交易账号
     */
    @ExcelProperty(value = "基金交易账号")
    private String fundTxAcctNo;

    /**
     * 基金代码
     */
    @ExcelProperty(value = "基金代码")
    private String fundCode;

    /**
     * 基金简称
     */
    @ExcelProperty(value = "基金简称")
    private String fundAddr;

    /**
     * 币种
     */
    @ExcelProperty(value = "币种", converter = CurrencyConverter.class)
    private String currency;

    /**
     * 申请时间
     */
    @ExcelProperty(value = "申请时间")
    private String appDtm;

    /**
     * 支付金额
     */
    @ExcelProperty(value = "支付金额")
    private String pmtAmt;

    /**
     * 支付对账日期
     */
    @ExcelProperty(value = "支付对账日期")
    private String pmtCheckDt;

    /**
     * 外部支付订单号
     */
    @ExcelProperty(value = "外部支付订单号")
    private String outPmtDealNo;

    /**
     * 外部支付金额
     */
    @ExcelProperty(value = "外部支付金额")
    private String outPmtAmt;

    /**
     * 外部币种
     */
    @ExcelProperty(value = "外部币种", converter = CurrencyConverter.class)
    private String outCurrency;

    /**
     * 外部支付标识
     */
    @ExcelProperty(value = "外部支付标识", converter = TxPmtFlagConverter.class)
    private String outPmtFlag;

    /**
     * 交易支付标识
     */
    @ExcelProperty(value = "交易支付标识", converter = TxPmtFlagConverter.class)
    private String txPmtFlag;

    /**
     * 支付对账标记
     */
    @ExcelProperty(value = "支付对账标记", converter = PmtCompFlagConverter.class)
    private String pmtCompFlag;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String memo;
}
