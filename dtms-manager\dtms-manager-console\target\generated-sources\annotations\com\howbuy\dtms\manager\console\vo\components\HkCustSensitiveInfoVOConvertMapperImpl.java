package com.howbuy.dtms.manager.console.vo.components;

import com.howbuy.dtms.manager.outservice.hkacc.dto.HkCustInfoDTO;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T16:12:59+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class HkCustSensitiveInfoVOConvertMapperImpl implements HkCustSensitiveInfoVOConvertMapper {

    @Override
    public HkCustSensitiveInfoVO convert(HkCustInfoDTO dto) {
        if ( dto == null ) {
            return null;
        }

        HkCustSensitiveInfoVO hkCustSensitiveInfoVO = new HkCustSensitiveInfoVO();

        hkCustSensitiveInfoVO.setAssetCertExpiredDate( dto.getAssetCertExpiredDate() );
        hkCustSensitiveInfoVO.setBirthday( dto.getBirthday() );
        hkCustSensitiveInfoVO.setCustChineseName( dto.getCustChineseName() );
        hkCustSensitiveInfoVO.setCustEnName( dto.getCustEnName() );
        hkCustSensitiveInfoVO.setCustState( dto.getCustState() );
        hkCustSensitiveInfoVO.setDerivativeKnowledge( dto.getDerivativeKnowledge() );
        hkCustSensitiveInfoVO.setEbrokerId( dto.getEbrokerId() );
        hkCustSensitiveInfoVO.setHboneNo( dto.getHboneNo() );
        hkCustSensitiveInfoVO.setHkCustNo( dto.getHkCustNo() );
        hkCustSensitiveInfoVO.setIdNoMask( dto.getIdNoMask() );
        hkCustSensitiveInfoVO.setIdType( dto.getIdType() );
        hkCustSensitiveInfoVO.setInvestorQualification( dto.getInvestorQualification() );
        hkCustSensitiveInfoVO.setInvstType( dto.getInvstType() );
        hkCustSensitiveInfoVO.setRiskToleranceLevel( dto.getRiskToleranceLevel() );

        return hkCustSensitiveInfoVO;
    }
}
