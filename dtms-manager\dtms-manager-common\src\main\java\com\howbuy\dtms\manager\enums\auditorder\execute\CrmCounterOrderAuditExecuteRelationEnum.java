package com.howbuy.dtms.manager.enums.auditorder.execute;

import com.howbuy.dtms.common.enums.BusinessCodeEnum;
import com.howbuy.dtms.common.enums.CounterOrderAuditStatusEnum;
import com.howbuy.dtms.manager.enums.auditorder.CounterBizTypeEnum;
import org.apache.commons.lang3.StringUtils;

public enum CrmCounterOrderAuditExecuteRelationEnum {
     /**************************************************CRM  直接审核不通过********************************************************************/
    CRM_PURCHASE_TO_NOT_AUDIT_PASS(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_FIRST_AUDIT_NOT_PASS_EXECUTE, null, CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.PURCHASE.getCode()),

    CRM_SUBS_TO_NOT_AUDIT_PASS(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_FIRST_AUDIT_NOT_PASS_EXECUTE, null, CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.SUBS.getCode()),

    CRM_112A_TO_NOT_AUDIT_PASS(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_FIRST_AUDIT_NOT_PASS_EXECUTE, null, CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum._112A.getCode()),

    CRM_112B_TO_NOT_AUDIT_PASS(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_FIRST_AUDIT_NOT_PASS_EXECUTE, null, CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum._112B.getCode()),
    CRM_SUB_AND_FIRST_PAID_TO_NOT_AUDIT_PASS(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_FIRST_AUDIT_NOT_PASS_EXECUTE, null, CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),

    CRM_REDEEM_TO_NOT_AUDIT_PASS(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_FIRST_AUDIT_NOT_PASS_EXECUTE, null, CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.REDEEM.getCode()),

    CRM_REVOKE_TO_NOT_AUDIT_PASS(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_FIRST_AUDIT_NOT_PASS_EXECUTE, null, CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.REVOKE.getCode()),


    /**************************************************等待复核--->审核通过执行器********************************************************************/
    CRM_PURCHASE_AUDIT_PASS(CounterOrderExecuteEnum.CRM_SUBS_FS_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.PURCHASE.getCode()),
    CRM_SUBS_AUDIT_PASS(CounterOrderExecuteEnum.CRM_SUBS_FS_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.SUBS.getCode()),

    CRM_112A_AUDIT_PASS(CounterOrderExecuteEnum.CRM_SUBS_FS_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum._112A.getCode()),
    CRM_112B_AUDIT_PASS(CounterOrderExecuteEnum.CRM_SUBS_FS_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum._112B.getCode()),
    CRM_SUB_AND_FIRST_PAID_AUDIT_PASS(CounterOrderExecuteEnum.CRM_SUBS_FS_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),

    CRM_REDEEM_AUDIT_PASS(CounterOrderExecuteEnum.CRM_REDEEM_FS_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.REDEEM.getCode()),

    CRM_REVOKE_AUDIT_PASS(CounterOrderExecuteEnum.CRM_REVOKE_FS_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.REVOKE.getCode()),


    /**************************************************等待复核--->审核不通过执行器********************************************************************/
    CRM_PURCHASE_NOT_AUDIT_PASS_WAIT_REVIEW_TO_FAILURE(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_FS_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.PURCHASE.getCode()),
    CRM_SUBS_NOT_AUDIT_PASS_WAIT_REVIEW_TO_FAILURE(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_FS_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.SUBS.getCode()),

    CRM_112A_NOT_AUDIT_PASS_WAIT_REVIEW_TO_FAILURE(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_FS_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum._112A.getCode()),

    CRM_112B_NOT_AUDIT_PASS_WAIT_REVIEW_TO_FAILURE(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_FS_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum._112B.getCode()),

    CRM_SUB_AND_FIRST_PAID_NOT_AUDIT_PASS_WAIT_REVIEW_TO_FAILURE(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_FS_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),
    CRM_REDEEM_NOT_AUDIT_PASS_WAIT_REVIEW_TO_FAILURE(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_FS_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.REDEEM.getCode()),

    CRM_REVOKE_NOT_AUDIT_PASS_WAIT_REVIEW_TO_FAILURE(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_FS_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.REVOKE.getCode()),


    /**************************************************等待回访--->驳回至经办执行器********************************************************************/
    CRM_PURCHASE_WAIT_REVISIT_TO_REJECT_TO_FAILURE(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_REVISIT_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.PURCHASE.getCode()),
    CRM_SUBS_WAIT_REVISIT_TO_REJECT_TO_FAILURE(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_REVISIT_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.SUBS.getCode()),

    CRM_112A_WAIT_REVISIT_TO_REJECT_TO_FAILURE(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_REVISIT_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum._112A.getCode()),
    CRM_112B_WAIT_REVISIT_TO_REJECT_TO_FAILURE(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_REVISIT_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum._112B.getCode()),
    CRM_SUB_AND_FIRST_PAID_WAIT_REVISIT_TO_REJECT_TO_FAILURE(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_REVISIT_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),

    CRM_REDEEM_WAIT_REVISIT_TO_REJECT_TO_FAILURE(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_REVISIT_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.REDEEM.getCode()),

    CRM_REVOKE_WAIT_REVISIT_TO_REJECT_TO_FAILURE(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_REVISIT_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.REVOKE.getCode()),


    /**************************************************等待回访--->审核通过执行器********************************************************************/
    CRM_PURCHASE_WAIT_REVISIT_TO_APPROVE(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_REVISIT_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.PURCHASE.getCode()),
    CRM_SUBS_WAIT_REVISIT_TO_APPROVE(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_REVISIT_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.SUBS.getCode()),

    CRM_112A_WAIT_REVISIT_TO_APPROVE(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_REVISIT_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum._112A.getCode()),

    CRM_112B_WAIT_REVISIT_TO_APPROVE(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_REVISIT_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum._112B.getCode()),
    CRM_SUB_AND_FIRST_PAID_WAIT_REVISIT_TO_APPROVE(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_REVISIT_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),

    CRM_REDEEM_WAIT_REVISIT_TO_APPROVE(CounterOrderExecuteEnum.CRM_COUNTER_ORDER_REDEEM_REVISIT_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.REDEEM.getCode()),

    CRM_REVOKE_WAIT_REVISIT_TO_APPROVE(CounterOrderExecuteEnum.CRM_REVOKE_REVISIT_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.REVOKE.getCode()),

    ;


    private final CounterOrderExecuteEnum executeEnum;

    private final String oldAuditStatus;

    private final String auditStatus;

    private final String bizType;

    CrmCounterOrderAuditExecuteRelationEnum(CounterOrderExecuteEnum executeEnum, String oldAuditStatus, String auditStatus, String bizType) {
        this.executeEnum = executeEnum;
        this.oldAuditStatus = oldAuditStatus;
        this.auditStatus = auditStatus;
        this.bizType = bizType;
    }

    /**
     * 通过 老审核状态，新审核状态，业务类型获取对应的审核规则
     * @return
     */
    public static String getByOldAuditStatusAndAuditStatusAndBizType(String oldAuditStatus, String auditStatus, String bizType) {
        for (CrmCounterOrderAuditExecuteRelationEnum auditEnum : CrmCounterOrderAuditExecuteRelationEnum.values()) {
            if(null == oldAuditStatus){
                if(null == auditEnum.getOldAuditStatus() && auditEnum.getAuditStatus().equals(auditStatus) && auditEnum.getBizType().equals(bizType)){
                    return auditEnum.getExecuteEnum().getCode();
                }
            }
            if (StringUtils.isNotBlank(auditEnum.getOldAuditStatus()) && auditEnum.getOldAuditStatus().equals(oldAuditStatus) && auditEnum.getAuditStatus().equals(auditStatus) && auditEnum.getBizType().equals(bizType)) {
                return auditEnum.getExecuteEnum().getCode();
            }
        }
        return null;
    }



    public CounterOrderExecuteEnum getExecuteEnum() {
        return executeEnum;
    }

    public String getOldAuditStatus() {
        return oldAuditStatus;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public String getBizType() {
        return bizType;
    }
}
