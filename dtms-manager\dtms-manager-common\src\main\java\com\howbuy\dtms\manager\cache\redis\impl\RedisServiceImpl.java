package com.howbuy.dtms.manager.cache.redis.impl;


import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.dtms.manager.cache.redis.IRedisService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * @Description: 缓存实现类
 * <AUTHOR> jiangwei.ji
 * @Date: 2024/8/15
*/
@Slf4j
@Component
public class RedisServiceImpl implements IRedisService {

    private final static String DEFAULT_LOCK_VALUE  = "lock";

    @Override
    public String getString(String key) {
        return ops().getStr(key);
    }

    @Override
    public Object get(String key) {
        return ops().get(key);
    }

    @Override
    public void set(String key, String value, long timeout, TimeUnit unit) {
        Long seconds = unit.toSeconds(timeout);
        ops().putStr(seconds.intValue(), key, value);
    }

    @Override
    public void set(String key, String value) {
        ops().putStr(key, value);
    }

    @Override
    public boolean lock(String key, long timeout, TimeUnit unit) {
        Long seconds = unit.toSeconds(timeout);
        log.info("加锁，key:{}",key);
        boolean result = ops().put(key,DEFAULT_LOCK_VALUE,"NX",seconds.intValue());
        return result;
    }

    @Override
    public boolean lock(String key, String v,long timeout, TimeUnit unit) {
        Long seconds = unit.toSeconds(timeout);
        log.info("加锁，key:{}",key);
        boolean result = ops().put(key, v,"NX",seconds.intValue());
        return result;
    }

    /**
     * 自旋锁
     */
    @Override
    @SneakyThrows
    public boolean lockAcquire(String key, long timeout, TimeUnit unit, Integer acquireSeconds) {
        long start = System.currentTimeMillis();
        log.info("加锁等待，key:{}", key);
        while (System.currentTimeMillis() - start < 1000L * acquireSeconds) {
            boolean ok = lock(key, timeout, unit);
            if (ok) {
                return true;
            }
            log.warn("再次尝试获取锁，key:{}", key);
            Thread.sleep(1000);
        }
        return false;
    }

    @Override
    public void delete(String key) {
        log.warn("删除缓存，key:{}", key);
        ops().remove(key);
    }

    @Override
    public void set(String key, Object value, long timeout, TimeUnit unit) {
        if (null == value) {
            log.warn("缓存的对象为null，不执行");
            return;
        }
        Long seconds = unit.toSeconds(timeout);
        ops().put(seconds.intValue(), key, value);
    }

    @Override
    public void set(String key, Object value) {
        ops().put(key, value);
    }

    @Override
    public long incr(String key) {
        return ops().incrBy(key, 1L);
    }

    @Override
    public boolean exists(String key) {
        return ops().exists(key);
    }


    /**
     * 获取操作类
     * @return
     */
    private CacheService ops() {
       return CacheServiceImpl.getInstance();
    }
}
