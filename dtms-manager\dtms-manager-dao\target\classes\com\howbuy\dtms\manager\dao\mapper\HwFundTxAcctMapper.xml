<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.HwFundTxAcctMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwFundTxAcctPO">
    <!--@mbg.generated-->
    <!--@Table hw_fund_tx_acct-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="hk_cust_no" jdbcType="VARCHAR" property="hkCustNo" />
    <result column="fund_tx_acc_type" jdbcType="CHAR" property="fundTxAccType" />
    <result column="fund_code" jdbcType="VARCHAR" property="fundCode" />
    <result column="fund_tx_acct_no" jdbcType="VARCHAR" property="fundTxAcctNo" />
    <result column="fund_tx_acct_stat" jdbcType="VARCHAR" property="fundTxAcctStat" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="rec_stat" jdbcType="CHAR" property="recStat" />
    <result column="create_timestamp" jdbcType="TIMESTAMP" property="createTimestamp" />
    <result column="update_timestamp" jdbcType="TIMESTAMP" property="updateTimestamp" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, hk_cust_no, fund_tx_acc_type, fund_code, fund_tx_acct_no, fund_tx_acct_stat, 
    version, rec_stat, create_timestamp, update_timestamp
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from hw_fund_tx_acct
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from hw_fund_tx_acct
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.howbuy.dtms.manager.dao.po.HwFundTxAcctPO">
    <!--@mbg.generated-->
    insert into hw_fund_tx_acct (id, hk_cust_no, fund_tx_acc_type, 
      fund_code, fund_tx_acct_no, fund_tx_acct_stat, 
      version, rec_stat, create_timestamp, 
      update_timestamp)
    values (#{id,jdbcType=BIGINT}, #{hkCustNo,jdbcType=VARCHAR}, #{fundTxAccType,jdbcType=CHAR}, 
      #{fundCode,jdbcType=VARCHAR}, #{fundTxAcctNo,jdbcType=VARCHAR}, #{fundTxAcctStat,jdbcType=VARCHAR}, 
      #{version,jdbcType=BIGINT}, #{recStat,jdbcType=CHAR}, #{createTimestamp,jdbcType=TIMESTAMP}, 
      #{updateTimestamp,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.dtms.manager.dao.po.HwFundTxAcctPO">
    <!--@mbg.generated-->
    insert into hw_fund_tx_acct
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="hkCustNo != null">
        hk_cust_no,
      </if>
      <if test="fundTxAccType != null">
        fund_tx_acc_type,
      </if>
      <if test="fundCode != null">
        fund_code,
      </if>
      <if test="fundTxAcctNo != null">
        fund_tx_acct_no,
      </if>
      <if test="fundTxAcctStat != null">
        fund_tx_acct_stat,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="recStat != null">
        rec_stat,
      </if>
      <if test="createTimestamp != null">
        create_timestamp,
      </if>
      <if test="updateTimestamp != null">
        update_timestamp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="hkCustNo != null">
        #{hkCustNo,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAccType != null">
        #{fundTxAccType,jdbcType=CHAR},
      </if>
      <if test="fundCode != null">
        #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAcctNo != null">
        #{fundTxAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAcctStat != null">
        #{fundTxAcctStat,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
      <if test="recStat != null">
        #{recStat,jdbcType=CHAR},
      </if>
      <if test="createTimestamp != null">
        #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTimestamp != null">
        #{updateTimestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.dtms.manager.dao.po.HwFundTxAcctPO">
    <!--@mbg.generated-->
    update hw_fund_tx_acct
    <set>
      <if test="hkCustNo != null">
        hk_cust_no = #{hkCustNo,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAccType != null">
        fund_tx_acc_type = #{fundTxAccType,jdbcType=CHAR},
      </if>
      <if test="fundCode != null">
        fund_code = #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAcctNo != null">
        fund_tx_acct_no = #{fundTxAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAcctStat != null">
        fund_tx_acct_stat = #{fundTxAcctStat,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="recStat != null">
        rec_stat = #{recStat,jdbcType=CHAR},
      </if>
      <if test="createTimestamp != null">
        create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTimestamp != null">
        update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.dtms.manager.dao.po.HwFundTxAcctPO">
    <!--@mbg.generated-->
    update hw_fund_tx_acct
    set hk_cust_no = #{hkCustNo,jdbcType=VARCHAR},
      fund_tx_acc_type = #{fundTxAccType,jdbcType=CHAR},
      fund_code = #{fundCode,jdbcType=VARCHAR},
      fund_tx_acct_no = #{fundTxAcctNo,jdbcType=VARCHAR},
      fund_tx_acct_stat = #{fundTxAcctStat,jdbcType=VARCHAR},
      version = #{version,jdbcType=BIGINT},
      rec_stat = #{recStat,jdbcType=CHAR},
      create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
      update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getNotFullFundTxAcct" parameterType="map" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List"/>
    from hw_fund_tx_acct
    where hk_cust_no = #{hkCustNo,jdbcType=VARCHAR}
    and fund_tx_acc_type = '0'
    AND fund_tx_acct_stat != '2'
    and rec_stat = '1'
  </select>
  <select id="selectByFundTxAcctNo" parameterType="map" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List"/>
    from hw_fund_tx_acct
    where fund_tx_acct_no = #{fundTxAcctNo,jdbcType=VARCHAR}
    AND fund_tx_acct_stat != '2'
    and rec_stat = '1'
  </select>
  <select id="selectList" parameterType="com.howbuy.dtms.manager.dao.query.HwFundTxAcctQuery" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/> from hw_fund_tx_acct
    <where>
      <if test="fundTxAccType != null and fundTxAccType != ''">
        and fund_tx_acc_type = #{fundTxAccType,jdbcType=VARCHAR}
      </if>
      <if test="fundCode != null and fundCode != ''">
        and fund_code = #{fundCode,jdbcType=VARCHAR}
      </if>
      <if test="hkCustNoList != null and hkCustNoList.size() > 0">
        and hk_cust_no in
        <foreach item="item" index="index" collection="hkCustNoList" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="fundTxAcctStatList != null and fundTxAcctStatList.size() > 0">
        and fund_tx_acct_stat in
        <foreach item="item" index="index" collection="fundTxAcctStatList" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      and rec_stat = '1'
    </where>
  </select>
</mapper>