package com.howbuy.dtms.manager.config;

import com.howbuy.dtms.manager.constant.Constants;
import org.keycloak.adapters.KeycloakConfigResolver;
import org.keycloak.adapters.KeycloakDeployment;
import org.keycloak.adapters.KeycloakDeploymentBuilder;
import org.keycloak.adapters.spi.HttpFacade;
import org.keycloak.adapters.springsecurity.KeycloakConfiguration;
import org.keycloak.adapters.springsecurity.authentication.KeycloakAuthenticationProvider;
import org.keycloak.adapters.springsecurity.config.KeycloakWebSecurityConfigurerAdapter;
import org.keycloak.representations.adapters.config.AdapterConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.session.SessionRegistryImpl;
import org.springframework.security.web.authentication.session.RegisterSessionAuthenticationStrategy;
import org.springframework.security.web.authentication.session.SessionAuthenticationStrategy;

import java.util.stream.Stream;

/**
 * guangyao.wu 2024-05-07 14:12
 */
@KeycloakConfiguration
public class KeycloakSecurityConfiguration extends KeycloakWebSecurityConfigurerAdapter {

    @Autowired
    public void configureGlobal(AuthenticationManagerBuilder auth) {
        KeycloakAuthenticationProvider keycloakAuthenticationProvider = keycloakAuthenticationProvider();
        auth.authenticationProvider(keycloakAuthenticationProvider);
    }

    @Bean
    @Override
    protected SessionAuthenticationStrategy sessionAuthenticationStrategy() {
        return new RegisterSessionAuthenticationStrategy(new SessionRegistryImpl());
    }

    @Bean
    @ConfigurationProperties(prefix = "keycloak")
    public AdapterConfig adapterConfig() {
        return new AdapterConfig();
    }

    @Bean
    public KeycloakConfigResolver keycloakConfigResolver(AdapterConfig adapterConfig) {
        //return new KeycloakSpringBootConfigResolver() ;
        return new KeycloakConfigResolver() {

            private KeycloakDeployment keycloakDeployment;

            @Override
            public KeycloakDeployment resolve(HttpFacade.Request facade) {
                if (keycloakDeployment != null) {
                    return keycloakDeployment;
                }

                keycloakDeployment = KeycloakDeploymentBuilder.build(adapterConfig);

                return keycloakDeployment;
            }
        };
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        super.configure(http);
        String[] whiteUrls = Stream.of(Constants.WHITE_URLS)
                .map(whiteUrl -> String.format("%s/**", whiteUrl))
                .toArray(String[]::new);
        //过滤无需授权的请求URI,
        http.authorizeRequests()
                .antMatchers(whiteUrls).permitAll()
                .anyRequest().authenticated()
                .and().csrf().disable();
    }
}
