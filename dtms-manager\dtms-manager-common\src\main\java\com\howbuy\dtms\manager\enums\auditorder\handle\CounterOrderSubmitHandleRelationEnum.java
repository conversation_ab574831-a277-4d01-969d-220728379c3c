package com.howbuy.dtms.manager.enums.auditorder.handle;

import com.howbuy.dtms.manager.enums.ExceptionCodeEnum;
import com.howbuy.dtms.manager.enums.auditorder.CounterBizTypeEnum;
import com.howbuy.dtms.manager.exception.BusinessException;

public enum CounterOrderSubmitHandleRelationEnum {

    /************************************业务提交的处理器*************************************/
    SUBS_COUNTER_ORDER_SUBMIT(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_SUBMIT_HANDLE, CounterBizTypeEnum.SUBS.getCode()),

    PURCHASE_COUNTER_ORDER_SUBMIT(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_SUBMIT_HANDLE, CounterBizTypeEnum.PURCHASE.getCode()),

    /**
     * 认缴提交处理器
     */
    _112A_COUNTER_ORDER_SUBMIT(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_SUBMIT_HANDLE, CounterBizTypeEnum._112A.getCode()),
    /**
     * 实缴提交处理器
     */
    _112B_COUNTER_ORDER_SUBMIT(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_SUBMIT_HANDLE, CounterBizTypeEnum._112B.getCode()),
    /**
     * 认缴和首次实缴提交处理器
     */
    SUB_AND_FIRST_PAID_COUNTER_ORDER_SUBMIT(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_SUBMIT_HANDLE, CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),
    /**
     * 赎回提交处理器
     */
    REDEEM_COUNTER_ORDER_SUBMIT(CounterOrderHandleEnum.REDEEM_COUNTER_ORDER_SUBMIT_HANDLE, CounterBizTypeEnum.REDEEM.getCode()),

    /**
     * 撤单提交处理器
     */
    REVOKE_COUNTER_ORDER_SUBMIT(CounterOrderHandleEnum.REVOKE_COUNTER_ORDER_SUBMIT_HANDLE, CounterBizTypeEnum.REVOKE.getCode()),


    /**
     * 展期修改校验器
     */
    EXTENSION_COUNTER_ORDER_SUBMIT(CounterOrderHandleEnum.EXTENSION_COUNTER_ORDER_SUBMIT_HANDLE, CounterBizTypeEnum.EXTENSION.getCode()),
    /**
     * 基金转换提交处理器
     */
    FUND_TRANSFER_COUNTER_ORDER_SUBMIT(CounterOrderHandleEnum.FUND_TRANSFER_COUNTER_ORDER_SUBMIT_HANDLE, CounterBizTypeEnum.FUND_TRANSFER.getCode()),
    /**
     * 强减提交处理器
     */
    FORCE_SUBTRACT_COUNTER_ORDER_SUBMIT(CounterOrderHandleEnum.FORCE_SUBTRACT_COUNTER_ORDER_SUBMIT_HANDLE, CounterBizTypeEnum.FORCE_SUBTRACT.getCode()),
    /**
     * 强增提交处理器
     */
    FORCE_ADD_COUNTER_ORDER_SUBMIT(CounterOrderHandleEnum.FORCE_ADD_COUNTER_ORDER_SUBMIT_HANDLE, CounterBizTypeEnum.FORCE_ADD.getCode()),

    /**
     * 批量实缴提交处理器
     */
    BATCH_PAID_COUNTER_ORDER_SUBMIT_HANDLE(CounterOrderHandleEnum.BATCH_PAID_COUNTER_ORDER_SUBMIT_HANDLE, CounterBizTypeEnum.BATCH_PAID.getCode()),

    /**
     * 全委批量认申购提交处理器
     */
    FULL_BATCH_SUBSCRIBE_COUNTER_ORDER_SUBMIT(CounterOrderHandleEnum.FULL_BATCH_SUBSCRIBE_COUNTER_ORDER_SUBMIT_HANDLE, CounterBizTypeEnum.FULL_BATCH_SUBSCRIBE.getCode()),

    /**
     * 全委批量赎回提交处理器
     */
    FULL_BATCH_REDEEM_COUNTER_ORDER_SUBMIT(CounterOrderHandleEnum.FULL_BATCH_REDEEM_COUNTER_ORDER_SUBMIT_HANDLE, CounterBizTypeEnum.FULL_BATCH_REDEEM.getCode()),

    /**
     * 基金交易账户开通处理器
     */
    FUND_TX_ACCT_NO_OPEN_COUNTER_ORDER_SUBMIT(CounterOrderHandleEnum.FUND_TX_ACCT_NO_OPEN_COUNTER_ORDER_SUBMIT_HANDLE, CounterBizTypeEnum.FUND_TX_ACCT_NO_OPEN.getCode()),

    ;

    private final CounterOrderHandleEnum handleEnum;

    private final String bizType;

    CounterOrderSubmitHandleRelationEnum(CounterOrderHandleEnum handleEnum, String bizType) {
        this.handleEnum = handleEnum;
        this.bizType = bizType;
    }

    public static String getBizTypeByBizType(String bizType) {
        for (CounterOrderSubmitHandleRelationEnum counterOrderAuditHandleRelationEnum : CounterOrderSubmitHandleRelationEnum.values()) {
            if (counterOrderAuditHandleRelationEnum.getBizType().equals(bizType)) {
                return counterOrderAuditHandleRelationEnum.getHandleEnum().getCode();
            }
        }
        throw new BusinessException(ExceptionCodeEnum.COUNTER_ORDER_AUDIT_LOGIC_HANDLER_NOT_EXISTS);
    }


    public CounterOrderHandleEnum getHandleEnum() {
        return handleEnum;
    }

    public String getBizType() {
        return bizType;
    }
}
