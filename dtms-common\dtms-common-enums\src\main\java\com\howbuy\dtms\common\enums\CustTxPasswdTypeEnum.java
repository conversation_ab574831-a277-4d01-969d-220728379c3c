package com.howbuy.dtms.common.enums;

public enum CustTxPasswdTypeEnum {

    // 交易密码设置标识	 0正常状态 1重置状态 2-未设置
    NORMAL("0", "正常状态"),
    RESET("1", "重置状态"),
    UNSETTLED("2", "未设置");

    private final String code;

    private final String desc;

    private CustTxPasswdTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * @description: 通过code获取枚举
     * @param code
     * @return com.howbuy.dtms.common.enums.CustTxPasswdTypeEnum
     * @author: jinqing.rao
     * @date: 2025/4/14 16:18
     * @since JDK 1.8
     */
    public static CustTxPasswdTypeEnum getEnumByCode(String code) {
        for (CustTxPasswdTypeEnum e : CustTxPasswdTypeEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }
    

    public String getDesc() {
        return desc;
    }
}
