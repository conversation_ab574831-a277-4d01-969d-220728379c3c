package com.howbuy.dtms.manager.common;


import com.howbuy.dtms.manager.enums.ExceptionCodeEnum;

import java.io.Serializable;

/**
 * 通用返回结果封装类
 * <AUTHOR>
 * @date 2024/7/19
 */
public class Response<T> implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 状态码
     * 状态码0000表示成功
     */
    private String code;
    /**
     * 描述信息
     */
    private String description;
    /**
     * 数据封装
     */
    private T data;

    public Response() {
    }

    public Response(String code, String description, T data) {
        this.code = code;
        this.description = description;
        this.data = data;
    }

    public static <T> Response<T> ok() {
        return ok(null);
    }

    /**
     * 成功返回结果
     * @param data 获取的数据
     */
    public static <T> Response<T> ok(T data) {
        return new Response<T>(ExceptionCodeEnum.SUCCESS.getCode(), ExceptionCodeEnum.SUCCESS.getDesc(), data);
    }

    /**
     * 成功返回结果
     * @param data 获取的数据
     */
    public static <T> Response<T> ok(String description, T data) {
        return new Response<T>(ExceptionCodeEnum.SUCCESS.getCode(), description, data);
    }

    /**
     * 失败返回结果
     * @param code 状态码
     * @param description 描述信息
     * @param data 数据封装
     */
    public static <T> Response<T> fail(String code, String description, T data) {
        return new Response<T>(code, description, data);
    }
    /**
     * 失败返回结果
     * @param code 状态码
     * @param description 描述信息
     */
    public static <T> Response<T> fail(String code, String description) {
        return new Response<T>(code, description, null);
    }

    /**
     * 失败返回结果
     * @param description 描述信息
     */
    public static <T> Response<T> fail(String description) {
        return fail(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), description, null);
    }

    /**
     * 失败返回结果
     * @return
     */
    public static <T> Response<T> fail() {
        return fail(ExceptionCodeEnum.SYSTEM_ERROR.getCode());
    }

    public boolean isSuccess(){
        return ExceptionCodeEnum.SUCCESS.getCode().equals(this.getCode());
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
