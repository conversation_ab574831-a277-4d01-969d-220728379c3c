<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.HwPayVoucherOrderMapper">
    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwPayVoucherOrderPO">
        <!--@Table hw_pay_voucher_order-->
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="voucherNo" column="voucher_no" jdbcType="BIGINT"/>
        <result property="hkCustNo" column="hk_cust_no" jdbcType="VARCHAR"/>
        <result property="tradeOrderNo" column="trade_order_no" jdbcType="VARCHAR"/>
        <result property="externalDealNo" column="external_deal_no" jdbcType="VARCHAR"/>
        <result property="tradeCode" column="trade_code" jdbcType="VARCHAR"/>
        <result property="remitCpAcctNo" column="remit_cp_acct_no" jdbcType="VARCHAR"/>
        <result property="remitCurrency" column="remit_currency" jdbcType="VARCHAR"/>
        <result property="remitAmt" column="remit_amt" jdbcType="NUMERIC"/>
        <result property="voucherType" column="voucher_type" jdbcType="VARCHAR"/>
        <result property="agreeSwap" column="agree_swap" jdbcType="VARCHAR"/>
        <result property="appDt" column="app_dt" jdbcType="VARCHAR"/>
        <result property="appTm" column="app_tm" jdbcType="VARCHAR"/>
        <result property="actualPayAmt" column="actual_pay_amt" jdbcType="NUMERIC"/>
        <result property="actualPayCurrency" column="actual_pay_currency" jdbcType="VARCHAR"/>
        <result property="actualPayDt" column="actual_pay_dt" jdbcType="VARCHAR"/>
        <result property="actualPayTm" column="actual_pay_tm" jdbcType="VARCHAR"/>
        <result property="actualPayAccount" column="actual_pay_account" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
        <result property="auditStatus" column="audit_status" jdbcType="VARCHAR"/>
        <result property="recStat" column="rec_stat" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="outletCode" column="outlet_code" jdbcType="VARCHAR"/>
        <result property="tradeChannel" column="trade_channel" jdbcType="VARCHAR"/>
        <result property="bankAcctCipher" column="bank_acct_cipher" jdbcType="VARCHAR"/>
        <result property="bankAcctDigest" column="bank_acct_digest" jdbcType="VARCHAR"/>
        <result property="bankAcctMask" column="bank_acct_mask" jdbcType="VARCHAR"/>
        <result property="swiftCode" column="swift_code" jdbcType="VARCHAR"/>
        <result property="bankCode" column="bank_code" jdbcType="VARCHAR"/>
        <result property="bankName" column="bank_name" jdbcType="VARCHAR"/>
        <result property="bankChineseName" column="bank_chinese_name" jdbcType="VARCHAR"/>
        <result property="fileSource" column="file_source" jdbcType="VARCHAR"/>
    </resultMap>


    <resultMap id="BaseResultBOMap" type="com.howbuy.dtms.manager.dao.bo.HwPayVoucherOrderBO">
        <!--@Table hw_pay_voucher_order-->
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="voucherNo" column="voucher_no" jdbcType="BIGINT"/>
        <result property="hkCustNo" column="hk_cust_no" jdbcType="VARCHAR"/>
        <result property="tradeOrderNo" column="trade_order_no" jdbcType="VARCHAR"/>
        <result property="externalDealNo" column="external_deal_no" jdbcType="VARCHAR"/>
        <result property="tradeCode" column="trade_code" jdbcType="VARCHAR"/>
        <result property="remitCpAcctNo" column="remit_cp_acct_no" jdbcType="VARCHAR"/>
        <result property="remitCurrency" column="remit_currency" jdbcType="VARCHAR"/>
        <result property="remitAmt" column="remit_amt" jdbcType="NUMERIC"/>
        <result property="voucherType" column="voucher_type" jdbcType="VARCHAR"/>
        <result property="agreeSwap" column="agree_swap" jdbcType="VARCHAR"/>
        <result property="appDt" column="app_dt" jdbcType="VARCHAR"/>
        <result property="appTm" column="app_tm" jdbcType="VARCHAR"/>
        <result property="actualPayAmt" column="actual_pay_amt" jdbcType="NUMERIC"/>
        <result property="actualPayCurrency" column="actual_pay_currency" jdbcType="VARCHAR"/>
        <result property="actualPayDt" column="actual_pay_dt" jdbcType="VARCHAR"/>
        <result property="actualPayTm" column="actual_pay_tm" jdbcType="VARCHAR"/>
        <result property="actualPayAccount" column="actual_pay_account" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
        <result property="auditStatus" column="audit_status" jdbcType="VARCHAR"/>
        <result property="recStat" column="rec_stat" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="outletCode" column="outlet_code" jdbcType="VARCHAR"/>
        <result property="tradeChannel" column="trade_channel" jdbcType="VARCHAR"/>
        <result property="bankAcctCipher" column="bank_acct_cipher" jdbcType="VARCHAR"/>
        <result property="bankAcctDigest" column="bank_acct_digest" jdbcType="VARCHAR"/>
        <result property="bankAcctMask" column="bank_acct_mask" jdbcType="VARCHAR"/>
        <result property="swiftCode" column="swift_code" jdbcType="VARCHAR"/>
        <result property="bankCode" column="bank_code" jdbcType="VARCHAR"/>
        <result property="bankName" column="bank_name" jdbcType="VARCHAR"/>
        <result property="bankChineseName" column="bank_chinese_name" jdbcType="VARCHAR"/>
        <result property="fileSource" column="file_source" jdbcType="VARCHAR"/>
        <result property="fundAddr" column="product_abbr" jdbcType="VARCHAR"/>
        <result property="fundCode" column="product_code" jdbcType="VARCHAR"/>
    </resultMap>


    <sql id="Base_BO_Column_List">
        a.id,
        a.voucher_no,
        a.hk_cust_no,
        a.trade_order_no,
        a.external_deal_no,
        a.trade_code,
        a.remit_cp_acct_no,
        a.remit_currency,
        a.remit_amt,
        a.voucher_type,
        a.agree_swap,
        a.app_dt,
        a.app_tm,
        a.actual_pay_amt,
        a.actual_pay_currency,
        a.actual_pay_dt,
        a.actual_pay_tm,
        a.actual_pay_account,
        a.remark,
        a.audit_time,
        a.audit_status,
        a.rec_stat,
        a.creator,
        a.modifier,
        a.create_time,
        a.update_time,
        a.outlet_code,
        a.trade_channel,
        a.bank_acct_cipher,
        a.bank_acct_digest,
        a.bank_acct_mask,
        a.swift_code,
        a.bank_code,
        a.bank_name,
        a.bank_chinese_name,
        a.file_source,
        b.product_code,
        b.product_abbr,
        a.duplicate_voucher,
        a.cus_delete
    </sql>

    <sql id="Base_Column_List">
        id,
        voucher_no,
        hk_cust_no,
        trade_order_no,
        external_deal_no,
        trade_code,
        remit_cp_acct_no,
        remit_currency,
        remit_amt,
        voucher_type,
        agree_swap,
        app_dt,
        app_tm,
        actual_pay_amt,
        actual_pay_currency,
        actual_pay_dt,
        actual_pay_tm,
        actual_pay_account,
        remark,
        audit_time,
        audit_status,
        rec_stat,
        creator,
        modifier,
        create_time,
        update_time,
        outlet_code,
        trade_channel,
        bank_acct_cipher,
        bank_acct_digest,
        bank_acct_mask,
        swift_code,
        bank_code,
        bank_name,
        bank_chinese_name,
        file_source
    </sql>

    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_pay_voucher_order
        where id = #{id}
    </select>

    <!-- 查询符合条件的数据 -->
    <select id="selectBySelective" parameterType="com.howbuy.dtms.manager.dao.po.HwPayVoucherOrderPO"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_pay_voucher_order
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="voucherNo != null and voucherNo != ''">
                and voucher_no = #{voucherNo}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and hk_cust_no = #{hkCustNo}
            </if>
            <if test="tradeOrderNo != null and tradeOrderNo != ''">
                and trade_order_no = #{tradeOrderNo}
            </if>
            <if test="externalDealNo != null and externalDealNo != ''">
                and external_deal_no = #{externalDealNo}
            </if>
            <if test="tradeCode != null and tradeCode != ''">
                and trade_code = #{tradeCode}
            </if>
            <if test="remitCpAcctNo != null and remitCpAcctNo != ''">
                and remit_cp_acct_no = #{remitCpAcctNo}
            </if>
            <if test="remitCurrency != null and remitCurrency != ''">
                and remit_currency = #{remitCurrency}
            </if>
            <if test="remitAmt != null">
                and remit_amt = #{remitAmt}
            </if>
            <if test="voucherType != null and voucherType != ''">
                and voucher_type = #{voucherType}
            </if>
            <if test="agreeSwap != null and agreeSwap != ''">
                and agree_swap = #{agreeSwap}
            </if>
            <if test="appDt != null and appDt != ''">
                and app_dt = #{appDt}
            </if>
            <if test="appTm != null and appTm != ''">
                and app_tm = #{appTm}
            </if>
            <if test="actualPayAmt != null">
                and actual_pay_amt = #{actualPayAmt}
            </if>
            <if test="actualPayCurrency != null and actualPayCurrency != ''">
                and actual_pay_currency = #{actualPayCurrency}
            </if>
            <if test="actualPayDt != null">
                and actual_pay_dt = #{actualPayDt}
            </if>
            <if test="actualPayTm != null">
                and actual_pay_tm = #{actualPayTm}
            </if>
            <if test="actualPayAccount != null and actualPayAccount != ''">
                and actual_pay_account = #{actualPayAccount}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="auditTime != null">
                and audit_time = #{auditTime}
            </if>
            <if test="auditStatus != null and auditStatus != ''">
                and audit_status = #{auditStatus}
            </if>
            <if test="recStat != null and recStat != ''">
                and rec_stat = #{recStat}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="modifier != null and modifier != ''">
                and modifier = #{modifier}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="outletCode != null and outletCode != ''">
                and outlet_code = #{outletCode}
            </if>
            <if test="tradeChannel != null and tradeChannel != ''">
                and trade_channel = #{tradeChannel}
            </if>
            <if test="bankAcctCipher != null and bankAcctCipher != ''">
                and bank_acct_cipher = #{bankAcctCipher}
            </if>
            <if test="bankAcctDigest != null and bankAcctDigest != ''">
                and bank_acct_digest = #{bankAcctDigest}
            </if>
            <if test="bankAcctMask != null and bankAcctMask != ''">
                and bank_acct_mask = #{bankAcctMask}
            </if>
            <if test="swiftCode != null and swiftCode != ''">
                and swift_code = #{swiftCode}
            </if>
            <if test="bankCode != null and bankCode != ''">
                and bank_code = #{bankCode}
            </if>
            <if test="bankName != null and bankName != ''">
                and bank_name = #{bankName}
            </if>
            <if test="bankChineseName != null and bankChineseName != ''">
                and bank_chinese_name = #{bankChineseName}
            </if>
            <if test="fileSource != null and fileSource != ''">
                and file_source = #{fileSource}
            </if>
        </where>
        order by id
    </select>


    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(*)
        from hw_pay_voucher_order
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="voucherNo != null and voucherNo != ''">
                and voucher_no = #{voucherNo}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and hk_cust_no = #{hkCustNo}
            </if>
            <if test="tradeOrderNo != null and tradeOrderNo != ''">
                and trade_order_no = #{tradeOrderNo}
            </if>
            <if test="externalDealNo != null and externalDealNo != ''">
                and external_deal_no = #{externalDealNo}
            </if>
            <if test="tradeCode != null and tradeCode != ''">
                and trade_code = #{tradeCode}
            </if>
            <if test="remitCpAcctNo != null and remitCpAcctNo != ''">
                and remit_cp_acct_no = #{remitCpAcctNo}
            </if>
            <if test="remitCurrency != null and remitCurrency != ''">
                and remit_currency = #{remitCurrency}
            </if>
            <if test="remitAmt != null">
                and remit_amt = #{remitAmt}
            </if>
            <if test="voucherType != null and voucherType != ''">
                and voucher_type = #{voucherType}
            </if>
            <if test="agreeSwap != null and agreeSwap != ''">
                and agree_swap = #{agreeSwap}
            </if>
            <if test="appDt != null and appDt != ''">
                and app_dt = #{appDt}
            </if>
            <if test="appTm != null and appTm != ''">
                and app_tm = #{appTm}
            </if>
            <if test="actualPayAmt != null">
                and actual_pay_amt = #{actualPayAmt}
            </if>
            <if test="actualPayCurrency != null and actualPayCurrency != ''">
                and actual_pay_currency = #{actualPayCurrency}
            </if>
            <if test="actualPayDt != null">
                and actual_pay_dt = #{actualPayDt}
            </if>
            <if test="actualPayDt != null">
                and actual_pay_dt = #{actualPayDt}
            </if>

            <if test="actualPayAccount != null and actualPayAccount != ''">
                and actual_pay_account = #{actualPayAccount}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="auditTime != null">
                and audit_time = #{auditTime}
            </if>
            <if test="auditStatus != null and auditStatus != ''">
                and audit_status = #{auditStatus}
            </if>
            <if test="recStat != null and recStat != ''">
                and rec_stat = #{recStat}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="modifier != null and modifier != ''">
                and modifier = #{modifier}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="outletCode != null and outletCode != ''">
                and outlet_code = #{outletCode}
            </if>
            <if test="tradeChannel != null and tradeChannel != ''">
                and trade_channel = #{tradeChannel}
            </if>
            <if test="bankAcctCipher != null and bankAcctCipher != ''">
                and bank_acct_cipher = #{bankAcctCipher}
            </if>
            <if test="bankAcctDigest != null and bankAcctDigest != ''">
                and bank_acct_digest = #{bankAcctDigest}
            </if>
            <if test="bankAcctMask != null and bankAcctMask != ''">
                and bank_acct_mask = #{bankAcctMask}
            </if>
            <if test="swiftCode != null and swiftCode != ''">
                and swift_code = #{swiftCode}
            </if>
            <if test="bankCode != null and bankCode != ''">
                and bank_code = #{bankCode}
            </if>
            <if test="bankName != null and bankName != ''">
                and bank_name = #{bankName}
            </if>
            <if test="bankChineseName != null and bankChineseName != ''">
                and bank_chinese_name = #{bankChineseName}
            </if>
            <if test="fileSource != null and fileSource != ''">
                and file_source = #{fileSource}
            </if>
        </where>
    </select>

    <!--分页查询，申请日期+申请时间 倒序排-->
    <select id="selectWithPage" parameterType="com.howbuy.dtms.manager.dao.query.HwPayVoucherOrderQuery"
            resultMap="BaseResultBOMap">
        select
        <include refid="Base_BO_Column_List"/>
        from hw_pay_voucher_order a left join hw_deal_order b on a.trade_order_no = b.deal_no
        <where>
            <if test="voucherNo != null and voucherNo != ''">
                and a.voucher_no = #{voucherNo}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and a.hk_cust_no = #{hkCustNo}
            </if>

            <if test="startAppDt != null and startAppDt != ''">
                and a.app_dt <![CDATA[>=]]> #{startAppDt}
            </if>
            <if test="endAppDt != null and endAppDt != ''">
                and a.app_dt <![CDATA[<=]]> #{endAppDt}
            </if>
            <if test="voucherTypeList != null and voucherTypeList.size > 0">
                and a.voucher_type in
                <foreach collection="voucherTypeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="auditStatusList != null and auditStatusList.size > 0">
                and a.audit_status in
                <foreach collection="auditStatusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="tradeOrderNo != null and tradeOrderNo != ''">
                and a.trade_order_no = #{tradeOrderNo}
            </if>
            <if test="externalDealNo != null and externalDealNo != ''">
                and a.external_deal_no = #{externalDealNo}
            </if>
            <if test="tradeCode != null and tradeCode != ''">
                and a.trade_code = #{tradeCode}
            </if>
            <if test="remitCpAcctNo != null and remitCpAcctNo != ''">
                and a.remit_cp_acct_no = #{remitCpAcctNo}
            </if>
            <if test="remitCurrency != null and remitCurrency != ''">
                and a.remit_currency = #{remitCurrency}
            </if>
            <if test="remitAmt != null">
                and a.remit_amt = #{remitAmt}
            </if>
            <if test="voucherType != null and voucherType != ''">
                and a.voucher_type = #{voucherType}
            </if>
            <if test="agreeSwap != null and agreeSwap != ''">
                and a.agree_swap = #{agreeSwap}
            </if>
            <if test="appDt != null and appDt != ''">
                and a.app_dt = #{appDt}
            </if>
            <if test="appTm != null and appTm != ''">
                and a.app_tm = #{appTm}
            </if>
            <if test="actualPayAmt != null">
                and a.actual_pay_amt = #{actualPayAmt}
            </if>
            <if test="actualPayCurrency != null and actualPayCurrency != ''">
                and a.actual_pay_currency = #{actualPayCurrency}
            </if>
            <if test="actualPayDt != null">
                and a.actual_pay_dt = #{actualPayDt}
            </if>
            <if test="actualPayTm != null">
                and a.actual_pay_tm = #{actualPayTm}
            </if>
            <if test="actualPayAccount != null and actualPayAccount != ''">
                and a.actual_pay_account = #{actualPayAccount}
            </if>
            <if test="auditTime != null">
                and a.audit_time = #{auditTime}
            </if>
            <if test="auditStatus != null and auditStatus != ''">
                and a.audit_status = #{auditStatus}
            </if>
            <if test="recStat != null and recStat != ''">
                and a.rec_stat = #{recStat}
            </if>
            <if test="creator != null and creator != ''">
                and a.creator = #{creator}
            </if>
            <if test="createTime != null">
                and a.create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and a.update_time = #{updateTime}
            </if>
            <if test="outletCode != null and outletCode != ''">
                and a.outlet_code = #{outletCode}
            </if>
            <if test="tradeChannel != null and tradeChannel != ''">
                and a.trade_channel = #{tradeChannel}
            </if>
            <if test="fileSource != null and fileSource != ''">
                and a.file_source = #{fileSource}
            </if>
            <if test="idList != null and idList.size > 0">
                and a.id in
                <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="duplicateVoucher != null and duplicateVoucher != ''">
                and a.duplicate_voucher = #{duplicateVoucher}
            </if>
            <if test="cusDelete != null and cusDelete != ''">
                and a.cus_delete = #{cusDelete}
            </if>
            and a.rec_stat = '0'
        </where>
        order by app_dt desc, app_tm desc
    </select>

    <select id="queryByVoucherNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_pay_voucher_order
        where voucher_no = #{voucherNo}
        and rec_stat = '0'
    </select>

    <select id="queryByTradeDealNoList" resultType="com.howbuy.dtms.manager.dao.bo.HwPayVoucherBO">
        select
        voucher_no as voucherNo,
        trade_order_no as tradeDealNo,
        actual_pay_amt as voucherActualPayAmt,
        actual_pay_currency as voucherActualPayCurrency,
        actual_pay_dt as voucherActualPayDt,
        actual_pay_tm as voucherActualPayTm,
        audit_status as voucherAuditStatus,
        create_time as voucherCreateTime,
        actual_pay_account as actualPayAccount
        from hw_pay_voucher_order
        where rec_stat = '0'
        and trade_order_no in
        <foreach item="item" index="index" collection="tradeDealNoList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="queryByActualPayAccount" resultType="com.howbuy.dtms.manager.dao.po.HwPayVoucherOrderPO">
        select
        <include refid="Base_Column_List"/>
        from hw_pay_voucher_order
        where rec_stat = '0'
        and actual_pay_account = #{actualPayAccount}
        order by app_dt desc, app_tm desc
    </select>

</mapper>

