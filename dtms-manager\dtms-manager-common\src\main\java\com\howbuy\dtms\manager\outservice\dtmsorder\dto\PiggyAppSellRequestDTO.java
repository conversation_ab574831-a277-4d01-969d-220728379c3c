package com.howbuy.dtms.manager.outservice.dtmsorder.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/14 17:48
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PiggyAppSellRequestDTO extends DtmsOrderBaseRequestDTO{

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 赎回方式 1-按份额赎回；2-按金额赎回
     */
    private String redeemMethod;

    /**
     * 赎回方向 1-回银行卡|电汇、2-留账好买香港账户、3-回海外储蓄罐、4-基金转投
     */
    private String redeemDirection;

    /**
     * 申请金额
     */
    private BigDecimal appAmt;

    /**
     * 申请份额
     */
    private BigDecimal appVol;

    /**
     * 关联订单号,订单表的订单号
     */
    private Long relationalDealNo;

    /**
     * 操作人
     */
    private String operator;

}
