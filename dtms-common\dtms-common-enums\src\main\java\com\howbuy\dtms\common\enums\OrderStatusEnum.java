/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 订单状态 1-申请成功；2-部分确认；3-确认成功；4-确认失败；5-自行撤销；6-强制取消；
 * @date 2024/4/17 20:28
 * @since JDK 1.8
 */
public enum OrderStatusEnum {

    /**
     * 申请成功
     */
    APPLY_SUCCESS("1", "申请成功"),
    /**
     * 部分确认
     */
    PART_CONFIRM("2", "部分确认"),
    /**
     * 确认成功
     */
    CONFIRM_SUCCESS("3", "确认成功"),
    /**
     * 确认失败
     */
    CONFIRM_FAIL("4", "确认失败"),
    /**
     * 自行撤销
     */
    SELF_CANCEL("5", "自行撤销"),
    /**
     * 强制取消
     */
    FORCE_CANCEL("6", "强制取消"),
    ;

    /**
     * 枚举code
     */
    private String code;
    /**
     * 枚举的中文意义
     */
    private String desc;

    OrderStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public static OrderStatusEnum getEnumByCode(String code) {
        for (OrderStatusEnum orderStatusEnum : OrderStatusEnum.values()) {
            if (orderStatusEnum.getCode().equals(code)) {
                return orderStatusEnum;
            }
        }
        return null;
    }

    /**
     * 根据code获取描述
     * @param code 枚举code
     * @return 描述
     */
    public static String getDescByCode(String code) {
        OrderStatusEnum orderStatusEnum = getEnumByCode(code);
        return orderStatusEnum != null ? orderStatusEnum.getDesc() : null;
    }

    /**
     * 获取确认状态的预约类型
     * @return
     */
    public static List<String> getAckPrebookStatus() {
        return Arrays.asList(APPLY_SUCCESS.getCode(), PART_CONFIRM.getCode(),CONFIRM_SUCCESS.getCode(),CONFIRM_FAIL.getCode());
    }

    /**
     * 获取撤销的业务类型code
     */
    public static List<String> getCancelPrebookStatus() {
        return Arrays.asList(SELF_CANCEL.getCode(), FORCE_CANCEL.getCode());
    }

}
