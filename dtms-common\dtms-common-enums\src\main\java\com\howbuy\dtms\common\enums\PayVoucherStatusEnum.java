/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

/**
 * <AUTHOR>
 * @description: 打款凭证状态 0-无需上传、1-未上传、2-已上传、3-审核通过、4-审核不通过
 * @date 2024/4/18 14:24
 * @since JDK 1.8
 */
public enum PayVoucherStatusEnum {

    NO_NEED_UPLOAD("0", "无需上传"),
    UN_UPLOAD("1", "未上传"),
    UPLOADED("2", "已上传"),
    AUDIT_PASS("3", "审核通过"),
    AUDIT_FAIL("4", "审核不通过");

    private String code;
    private String desc;

    PayVoucherStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static PayVoucherStatusEnum getEnumByCode(String code) {
        for (PayVoucherStatusEnum payVoucherStatusEnum : PayVoucherStatusEnum.values()) {
            if (payVoucherStatusEnum.getCode().equals(code)) {
                return payVoucherStatusEnum;
            }
        }
        return null;
    }

}
