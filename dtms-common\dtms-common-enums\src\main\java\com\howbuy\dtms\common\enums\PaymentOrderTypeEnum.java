/*
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

/**
 * @description: 支付订单类型枚举
 * <AUTHOR>
 * @date 2025-07-02 16:04:22
 * @since JDK 1.8
 */
public enum PaymentOrderTypeEnum {

    /**
     * 交易订单
     */
    TRADE("1", "交易"),
    
    /**
     * EDDA入金订单
     */
    EDDA_DEPOSIT("2", "edda入金");

    /**
     * 枚举code
     */
    private final String code;
    
    /**
     * 枚举的中文意义
     */
    private final String desc;

    PaymentOrderTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     * @param code 枚举code
     * @return 枚举对象
     */
    public static PaymentOrderTypeEnum getEnumByCode(String code) {
        for (PaymentOrderTypeEnum orderTypeEnum : PaymentOrderTypeEnum.values()) {
            if (orderTypeEnum.getCode().equals(code)) {
                return orderTypeEnum;
            }
        }
        return null;
    }

    /**
     * 根据code获取描述
     * @param code 枚举code
     * @return 描述
     */
    public static String getDescByCode(String code) {
        PaymentOrderTypeEnum orderTypeEnum = getEnumByCode(code);
        return orderTypeEnum != null ? orderTypeEnum.getDesc() : null;
    }
}
