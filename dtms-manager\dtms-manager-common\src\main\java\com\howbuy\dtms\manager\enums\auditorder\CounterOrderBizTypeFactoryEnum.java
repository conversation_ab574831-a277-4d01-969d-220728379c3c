package com.howbuy.dtms.manager.enums.auditorder;

public enum CounterOrderBizTypeFactoryEnum {

    COUNTER_ORDER_AUDIT_PASS("COUNTER_ORDER_AUDIT_PASS", "柜台订单审核通过工厂"),

    FUND_TX_ACCT_NO_OPEN_COUNTER_ORDER_AUDIT_PASS("FUND_TX_ACCT_NO_OPEN_COUNTER_ORDER_AUDIT_PASS", "柜台订单审核通过工厂"),

    COUNTER_ORDER_AUDIT_NOT_PASS("COUNTER_ORDER_AUDIT_NOT_PASS", "柜台订单审核不通过工厂"),
    COUNTER_ORDER_PURCHASE_FACTORY("COUNTER_ORDER_PURCHASE_FACTORY", "认申购下单工厂"),

    REDEEM_ORDER_PURCHASE_FACTORY("REDEEM_ORDER_PURCHASE_FACTORY", "赎回柜台订单工厂"),

    REVOKE_ORDER_PURCHASE_FACTORY("REVOKE_ORDER_PURCHASE_FACTORY", "柜台交易撤单工厂"),

    EXTENSION_ORDER_PURCHASE_FACTORY("EXTENSION_ORDER_PURCHASE_FACTORY", "柜台展期修改工厂"),

    FUND_TRANSFER_ORDER_FACTORY("FUND_TRANSFER_ORDER_FACTORY", "基金转换工厂"),

    FORCE_SUBTRACT_ORDER_FACTORY("FORCE_SUBTRACT_ORDER_FACTORY", "柜台强减订单工厂"),

    FORCE_ADD_ORDER_FACTORY("FORCE_ADD_ORDER_FACTORY", "柜台强增订单工厂"),

    BATCH_PAID_ORDER_FACTORY("BATCH_PAID_ORDER_FACTORY", "柜台批量订单工厂"),

    FULL_BATCH_SUBSCRIBE_ORDER_FACTORY("FULL_BATCH_SUBSCRIBE_ORDER_PURCHASE_FACTORY", "柜台全委专户批量认申购工厂"),

    FULL_BATCH_REDEEM_ORDER_FACTORY("FULL_BATCH_REDEEM_ORDER_FACTORY", "柜台全委专户批量赎回工厂"),

    /**
     * 基金交易账号开通工厂
     */
    FUND_TX_ACCT_NO_OPEN_FACTORY("FUND_TX_ACCT_NO_OPEN_FACTORY", "柜台基金交易账号开通工厂"),

    ;
    private final String code;

    private final String value;

     CounterOrderBizTypeFactoryEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }
    public String getValue() {
        return value;
    }
}
