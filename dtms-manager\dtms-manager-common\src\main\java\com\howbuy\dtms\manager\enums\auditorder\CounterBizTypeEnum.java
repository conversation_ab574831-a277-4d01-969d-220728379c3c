package com.howbuy.dtms.manager.enums.auditorder;

import com.howbuy.dtms.common.enums.BusinessCodeEnum;
import com.howbuy.dtms.manager.common.Response;

public enum CounterBizTypeEnum {
    SUBS("1120", BusinessCodeEnum.SUBS, "认购"),
    PURCHASE("1122", BusinessCodeEnum.PURCHASE, "申购"),
    _112A("112A", BusinessCodeEnum._112A, "认缴"),
    _112B("112B", BusinessCodeEnum._112B, "实缴"),
    REDEEM("1124", BusinessCodeEnum.REDEEM, "赎回"),

    REVOKE("revoke", null, "交易撤单"),

    EXTENSION("119F", BusinessCodeEnum._119F, "展期修改"),

    FUND_TRANSFER("1136", BusinessCodeEnum._1136, "基金转换"),

    FORCE_ADD("1144", BusinessCodeEnum.FORCE_ADD, "强增"),

    FORCE_SUBTRACT("1145", BusinessCodeEnum.FORCE_SUBTRACT, "强减"),

    BATCH_PAID("batchPaid", null, "批量实缴"),

    SUB_AND_FIRST_PAID("subAndFirstPaid", null, "认缴和首次实缴"),

    FULL_BATCH_SUBSCRIBE("fullBatchSubscribe", null, "全委专户批量认申购"),

    FULL_BATCH_REDEEM("fullBatchRedeem", null, "全委专户批量赎回"),

    FUND_TX_ACCT_NO_OPEN("fundTxAcctNoOpen",null,"基金交易账号开通"),
    ;

    private final String code;

    private final BusinessCodeEnum businessCodeEnum;

    private final String desc;

    CounterBizTypeEnum(String code, BusinessCodeEnum businessCodeEnum, String desc) {
        this.code = code;
        this.businessCodeEnum = businessCodeEnum;
        this.desc = desc;
    }

    /**
     * 通过Code获取业务类型
     *
     * @return
     */
    public static String getBusinessCodeByCode(String code) {
        for (CounterBizTypeEnum counterBizTypeEnum : CounterBizTypeEnum.values()) {
            if (counterBizTypeEnum.getCode().equals(code)) {
                return counterBizTypeEnum.getBusinessCodeEnum().getMCode();
            }
        }
        return null;
    }

    public static BusinessCodeEnum getBusinessCodeEnumByCode(String code) {
        for (CounterBizTypeEnum counterBizTypeEnum : CounterBizTypeEnum.values()) {
            if (counterBizTypeEnum.getCode().equals(code)) {
                return counterBizTypeEnum.getBusinessCodeEnum();
            }
        }
        return null;
    }

    /**
     * 通过Code获取描述
     * @return
     */
    public static String getDescByCode(String code) {
        for (CounterBizTypeEnum counterBizTypeEnum : CounterBizTypeEnum.values()) {
            if (counterBizTypeEnum.getCode().equals(code)) {
                return counterBizTypeEnum.getDesc();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public BusinessCodeEnum getBusinessCodeEnum() {
        return businessCodeEnum;
    }

    public String getDesc() {
        return desc;
    }
}
