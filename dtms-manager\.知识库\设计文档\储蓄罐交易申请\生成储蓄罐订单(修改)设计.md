## 生成储蓄罐订单(修改)

- 请求地址

|                                 |      |                                              |                |
| ------------------------------- | ---- | -------------------------------------------- | -------------- |
| ImportHwPiggyTradeAppController | http | /counter/importHwPiggyTradeApp/generateOrder | 生成储蓄罐订单 |

- 修改逻辑(GeneratePiggyTradeAppOrderService#process)：
  - 申购业务
    - 储蓄罐申请来源为可用余额、excel的才进行查询并校验基金交易账号、现金余额校验
      - 查询并校验基金交易账号
        - HwPiggyTradeAppImportPO的基金交易账号非空才进行查询并校验基金交易账号
      - 现金余额校验
        - 查询在途订单根据打款截止日期
          - 支付状态条件 删除5-失败
          - 子查询查询条件改为：打款截止日期<=下一个跑批工作日（workdayVO.getNextWorkday()）
        - 计算在途订单对应储蓄罐已赎回金额
          - 订单状态条件 改为不等于 5-自行撤销；6-强制取消
    - 去掉updateGenerateResult
  - 赎回业务
    - 关联订单号不为空，则根据关联订单号查询交易订单表记录，判断订单状态是否为自行撤单、强制撤单，如果则调用doFail，异常信息为关联订单号已撤单。
    - 去掉updateGenerateResult