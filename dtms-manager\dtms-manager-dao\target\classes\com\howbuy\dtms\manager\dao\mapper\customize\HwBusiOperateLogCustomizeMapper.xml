<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.customize.HwBusiOperateLogCustomizeMapper">

    <select id="queryOperateLogByAppId" resultMap="com.howbuy.dtms.manager.dao.mapper.HwBusiOperateLogMapper.BaseResultMap">
        SELECT 
            <include refid="com.howbuy.dtms.manager.dao.mapper.HwBusiOperateLogMapper.Base_Column_List"/>
        FROM 
            hw_busi_operate_log
        WHERE
            app_id = #{busiId,jdbcType=BIGINT}
        ORDER BY
        create_timestamp DESC
    </select>

</mapper> 