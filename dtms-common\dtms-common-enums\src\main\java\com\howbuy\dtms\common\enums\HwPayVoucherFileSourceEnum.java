/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

/**
 * <AUTHOR>
 * @description: 海外打款凭证文件来源 1-中台 2-CRM
 * @date 2024/4/18 19:28
 * @since JDK 1.8
 */
public enum HwPayVoucherFileSourceEnum {

    /**
     * DTMS_ORDER
     */
    DTMS_ORDER("1", "DTMS_ORDER"),

    /**
     * CRM
     */
    CRM("2", "CRM"),

    ;

    private String code;
    private String desc;

    HwPayVoucherFileSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static HwPayVoucherFileSourceEnum getEnumByCode(String code) {
        for (HwPayVoucherFileSourceEnum tradeChannelEnum : HwPayVoucherFileSourceEnum.values()) {
            if (tradeChannelEnum.getCode().equals(code)) {
                return tradeChannelEnum;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


}
