/**
 *Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.dtms.manager.constant;

/**
 * @description:常量类
 * @reason:
 * <AUTHOR>
 * @date 2024/7/22
 * @since JDK 1.8
 */
public class Constants {

    /**
     * 默认字符串分隔符为逗号
     */
    public static final String SPLIT_DEFAULT = ",";

    /**
     * 是否是储蓄罐基金: 1-是
     */
    public static final String IS_PIGGY_FUND_YES = "1";
    public static final String HW_OUTLET_CODE = "A20150420";
    /**
     * 柜台订单业务字典编码
     */
    public static final String COUNTER_ORDER_BUSI_DIC_CODE = "COUNTER_ORDER_00000001";
    public static final String HKCUSTNO = "111111";
    public static final String HK_APP_CUST_NO_KEY = "222";

    /**
     * 柜台回访文件上传配置
     */
    public static final String COUNTER_ORDER_REVIEW_FILE_STORE_CONFIG = "counter_order_revisit_storeConfig";

    /**
     * 柜台补签协议文件上传配置
     */
    public static final String COUNTER_ORDER_SUPPLEMENT_FILE_STORE_CONFIG = "counter_order_supplement_storeConfig";
    /**
     * REQ_ID
     */
    public static final String REQ_ID = "reqId";
    /**
     * 日志调用链id
     */
    public static final String TRACE_ID = "traceId";
    /**
     * 账户状态 0-正常
     */
    public final static String ACCOUNT_STATUS_NORMAL = "0";
    /**
     * 手动标记 1-手动
     */
    public static final String HAND_FLAG_YES = "1";


    /**
     * 批量查询每批次大小
     */
    public static final int BATCH_SIZE_200 = 200;

    /**
     * 默认页码
     */
    public static final int PAGE_NO_1 = 1;

    /**
     * 每批次处理500
     */
    public static int BATCH_SIZE_500 = 500;

    /**
     * 每批次处理1000
     */
    public static int BATCH_SIZE_1000 = 1000;

    /**
     * 默认时区
     */
    public static final String DEFAULT_TIME_ZONE = "+8";

    /**
     * 默认日期时间格式
     */
    public static final String DEFAULT_LOCAL_DATE_TIME_FORMATTER = "yyyy-MM-dd HH:mm:ss";

    /**
     * 版本号字段名称
     */
    public static final String FIELD_VERSION = "version";


    /**
     * 系统
     */
    public static final String SYSTEM = "system";

    /**
     * 缓存Key分隔符
     */
    public static final String CACHE_KEY_SEPARATOR = "|";

    /**
     *     投资者资质PRO-投资者资质专业
     */
    public static final String INVESTOR_QUALIFICATION_PRO = "PRO";
    /**
     * 投资者资质P NORMAL-投资者资质普通
     */
    public static final String INVESTOR_QUALIFICATION_NORMAL = "NORMAL";

    /**
     * 确认标记 1-确认
     */
    public static final String CONFIRM_FLAG_YES = "1";
    /**
     * 确认标记 0-取消
     */
    public static final String CONFIRM_FLAG_CANCEL = "0";

    /**
     * 冒号
     */
    public static final String SPLIT_COLON = ":";

    /**
     * 中划线
     */
    public static final String SPLIT_STRIKE = "-";

    /**
     * 间隔号
     */
    public static final String DOT_COLON = ".";

    /**
     * html 换行标签
     */
    public static final String LINE_BREAK_LABEL = "<br>";

    /**
     * 好买邮箱后缀
     */
    public static final String HOWBUY_EMAIL_SUFFIX = "@howbuy.com";

    /**
     * 白名单地址配置
     */
    public static final String[] WHITE_URLS = { "/api", "/actuator", "/console/bpMenuInfo/queryAllMenuInfo",
            "/console/components/queryHkCustInfo", "/static", "/images", "/css","/js" };

    /**
     * PH2312W02-好买香港
     */
    public static final String HK_OUTLET_CODE = "PH2312W02";

    /**
     * 金额小数位
     */
    public final static int AMOUNT_SCALE = 2;

    /**
     * 日元金额小数位
     */
    public final static int JPY_AMOUNT_SCALE = 0;

    /**
     * 通用告警 to all
     */
    public static final String MSG_TYPE_SETTLE_ALL = "0";
    /**
     * 业务告警（运营）
     */
    public static final String MSG_TYPE_SETTLE_BIZ = "1";

    /**
     * 系统告警（开发，产品）
     */
    public static final String MSG_TYPE_SETTLE_ALERT = "2";

    /**
     * 海外中台系统-操作人
     */
    public final static String DEFAULT_OPERATOR = "hwztsys";
    /**
     * 结单邮件模板id
     */
    public final static String STATEMENT_EMAIL_ID = "60336";


    public final static String TRUE = "true";


    /**
     * 日期类型 1-工作日，0-自然日
     */
    public static final String WORK_DAY = "1";

    /**
     * 批处理交易线程池
     */
    public static final String BATCH_TRADE_POOL = "BATCH_TRADE_POOL";

    /**
     * 批量接口超时时间
     */
    public static final int BATCH_INTERFACE_TIMEOUT = 60000;

    /**
     * 文件分隔符
     */
    public static final String FILE_SEPARATOR = "/";

    /**
     * 零间隔天数
     */
    public static final int ZERO_INTERVAL = 0;

}
