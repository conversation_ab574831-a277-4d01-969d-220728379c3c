/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.manager.counter.vo.payment;

import lombok.Getter;
import lombok.Setter;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/7/23 15:03
 * @since JDK 1.8
 */
@Getter
@Setter
public class PaymentCheckResultVO {
    /**
     * 支付订单号
     */
    private String pmtDealNo;

    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 中台业务代码
     */
    private String middleBusiCode;

    /**
     * 支付方式列表
     * 111；第一位：电汇；第二位：支票；第三位：海外储蓄罐
     */
    private String paymentTypeList;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 客户姓名
     */
    private String custName;

    /**
     * 资金账号
     */
    private String cpAcctNo;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金简称
     */
    private String fundAddr;

    /**
     * 币种
     */
    private String currency;

    /**
     * 申请时间
     */
    private String appDtm;

    /**
     * 支付金额
     */
    private String pmtAmt;

    /**
     * 支付对账日期
     */
    private String pmtCheckDt;

    /**
     * 外部支付订单号
     */
    private String outPmtDealNo;

    /**
     * 外部支付金额
     */
    private String outPmtAmt;

    /**
     * 外部币种
     */
    private String outCurrency;

    /**
     * 外部支付标识
     * 1-待支付 2-支付成功 3-支付失败 4-支付中
     */
    private String outPmtFlag;

    /**
     * 交易支付标识
     * 0-无需付款；1-未付款；2-付款成功；3-付款失败；4-付款中；5-已退款;6-等待付款；7-撤单成功；
     */
    private String txPmtFlag;

    /**
     * 支付对账标记
     * 0-无需对账；1-未对账；2-对账完成；3-对账不平;
     */
    private String pmtCompFlag;

    /**
     * 备注
     */
    private String memo;
}
