package com.howbuy.dtms.manager.console.service.hwdealorderdtl;

import com.google.common.collect.Lists;
import com.howbuy.dtms.common.annotation.DataSource;
import com.howbuy.dtms.common.enums.BusinessCodeEnum;
import com.howbuy.dtms.manager.common.BatchResponse;
import com.howbuy.dtms.manager.common.ConfirmVO;
import com.howbuy.dtms.manager.console.request.hwdealorderdtl.BatchUpdatePreSubmitTaDtRequest;
import com.howbuy.dtms.manager.console.vo.hwdealorderdtl.DealOrderDtlSubmitConfirmVO;
import com.howbuy.dtms.manager.constant.Constants;
import com.howbuy.dtms.manager.dao.bo.CustDealOrderAppBO;
import com.howbuy.dtms.manager.dao.po.HwDealOrderDtlPO;
import com.howbuy.dtms.manager.dao.po.HwDealOrderPO;
import com.howbuy.dtms.manager.dao.repository.hwdealorder.HwDealOrderRepository;
import com.howbuy.dtms.manager.dao.repository.hwdealorderdtl.HwDealOrderDtlRepository;
import com.howbuy.dtms.manager.enums.*;
import com.howbuy.dtms.manager.exception.BusinessException;
import com.howbuy.dtms.manager.exception.ExceptionUtils;
import com.howbuy.dtms.manager.outservice.param.ParamOuterService;
import com.howbuy.dtms.manager.outservice.param.dto.*;
import com.howbuy.dtms.manager.outservice.settle.SettleOuterService;
import com.howbuy.dtms.manager.outservice.settle.dto.CounterCloseStatusDTO;
import com.howbuy.dtms.manager.utils.AssertUtil;
import com.howbuy.dtms.manager.utils.DateUtils;
import com.howbuy.dtms.manager.utils.LoggerUtils;
import com.howbuy.dtms.product.client.enums.FundStatEnum;
import com.howbuy.dtms.settle.client.facade.query.vo.WorkdayVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 批量修改预计上报日
 * @date 2024/7/30 18:40
 * @since JDK 1.8
 */
@Service
@Slf4j
public class BatchUpdatePreSubmitTaDtService {
    @Autowired
    private HwDealOrderRepository hwDealOrderRepository;
    @Autowired
    private HwDealOrderDtlRepository hwDealOrderDtlRepository;

    @Autowired
    private ParamOuterService paramOuterService;

    @Resource
    private SettleOuterService dtmsSettleOuterService;

    /**
     * 批量修改预计上报日
     *
     * @param request
     * @return
     */
    @Transactional
    @DataSource("mysql")
    public BatchResponse execute(BatchUpdatePreSubmitTaDtRequest request) {

        List<CustDealOrderAppBO> custDealOrderAppList = hwDealOrderDtlRepository.queryByIdList(request.getIdList());
        AssertUtil.notEmpty(custDealOrderAppList, "订单明细不存在");
        validate(custDealOrderAppList);
        // 前置校验了必须是统一批次的产品
        HwDealOrderDtlPO po = custDealOrderAppList.get(0);
        // 查询产品信息
        FundBasicInfoDTO fundBasicInfoDTO = paramOuterService.queryByFundCode(po.getFundCode());
        // 是否支持预约，2025-02-11 新增每日开放产品的批量修改上报日逻辑
        BatchResponse batchResponse = new BatchResponse();
        boolean openDailyTrade;
        if (MiddleBusiCodeEnum.isBuyCategory(po.getMiddleBusiCode())) {
            openDailyTrade = IsScheduledTradeEnum.isSupportedBuyScheduledTrade(fundBasicInfoDTO.getIsScheduledTrade());
        } else {
            openDailyTrade = IsScheduledTradeEnum.isSupportedRedeemScheduledTrade(fundBasicInfoDTO.getIsScheduledTrade());
        }
        if (openDailyTrade) {
            // 每日开放产品批量更新上报日
            openDailyTradeUpdatePreSubmitTaDt(request, custDealOrderAppList, fundBasicInfoDTO);
            return batchResponse;
        }

        List<ConfirmVO> confirmList = confirmValidate(request, custDealOrderAppList, fundBasicInfoDTO, po);
        if (!Objects.equals(Constants.CONFIRM_FLAG_YES, request.getConfirmFlag()) && !CollectionUtils.isEmpty(confirmList)) {
            Map<String, List<ConfirmVO>> collect = confirmList.stream().collect(Collectors.groupingBy(ConfirmVO::getConfirmCode));
            List<ConfirmVO> confirmListGroupByCode = Lists.newArrayList();
            collect.forEach((key,value)-> {
                ConfirmVO vo = new ConfirmVO();
                vo.setConfirmCode(key);
                vo.setConfirmMsg(value.get(0).getConfirmMsg());
                vo.setList(value);
                confirmListGroupByCode.add(vo);
            });
            batchResponse.setConfirmList(confirmListGroupByCode);
            return batchResponse;
        }
        List<Long> dealNoList = custDealOrderAppList.stream().map(CustDealOrderAppBO::getDealNo).collect(Collectors.toList());
        Map<Long, HwDealOrderPO> dealOrderPOMap = hwDealOrderRepository.queryByDealNoList(dealNoList).stream()
                .collect(Collectors.toMap(HwDealOrderPO::getDealNo, item -> item));

        for (CustDealOrderAppBO custDealOrderAppBO : custDealOrderAppList) {
            MiddleBusiCodeEnum middleBusiCodeEnum = MiddleBusiCodeEnum.getByCode(custDealOrderAppBO.getMiddleBusiCode());
            HwDealOrderDtlPO updateItem = new HwDealOrderDtlPO();
            updateItem.setId(custDealOrderAppBO.getId());
            updateItem.setUpdateTimestamp(LocalDateTime.now());

            // 购买类
            if (MiddleBusiCodeEnum.isBuyCategory(custDealOrderAppBO.getMiddleBusiCode())) {
                String[] paymentTypeArray = custDealOrderAppBO.getPaymentTypeList().split("");
                // 储蓄罐支付
                if (Objects.equals(paymentTypeArray[2], YesOrNoEnum.YES.getCode())) {
                    updateBuyCategoryOrderWithPaymentByPiggy(request, custDealOrderAppBO, fundBasicInfoDTO, middleBusiCodeEnum, updateItem);
                } else {
                    // 非储蓄罐支付，电汇
                    updateBuyCategoryOrderWithPaymentByWireTransfer(request, custDealOrderAppBO, fundBasicInfoDTO, middleBusiCodeEnum, updateItem);
                }
            }
            if (MiddleBusiCodeEnum.isRedeemCategory(custDealOrderAppBO.getMiddleBusiCode())) {
                updateRedeemOrder(request, custDealOrderAppBO, fundBasicInfoDTO, middleBusiCodeEnum, updateItem);
            }
            //构建UpdateOrderItem
            HwDealOrderPO updateOrderItem = buildUpdateOrderItem(dealOrderPOMap, updateItem, custDealOrderAppBO.getDealNo());
            //更新订单及订单明细
            hwDealOrderRepository.updateOrderAndDtl(updateOrderItem, updateItem);
            LoggerUtils.recordDelaySubmitLog(custDealOrderAppBO.getDealDtlNo(), custDealOrderAppBO.getPreSubmitTaDt(), updateItem.getPreSubmitTaDt());
        }

        return batchResponse;

    }

    /**
     * @param request
     * @param custDealOrderAppList
     * @param fundBasicInfoDTO
     * @return void
     * @description: 每日开放的批量修改上报日
     * @author: jinqing.rao
     * @date: 2025/2/11 17:17
     * @since JDK 1.8
     */
    private void openDailyTradeUpdatePreSubmitTaDt(BatchUpdatePreSubmitTaDtRequest request, List<CustDealOrderAppBO> custDealOrderAppList, FundBasicInfoDTO fundBasicInfoDTO) {
        List<Long> dealNoList = custDealOrderAppList.stream().map(CustDealOrderAppBO::getDealNo).collect(Collectors.toList());
        Map<Long, HwDealOrderPO> dealOrderPOMap = hwDealOrderRepository.queryByDealNoList(dealNoList).stream()
                .collect(Collectors.toMap(HwDealOrderPO::getDealNo, item -> item));
        // 每日开放的前置校验
        openDailyTradeValidate(fundBasicInfoDTO, request, custDealOrderAppList);
        // 封装更新数据
        for (CustDealOrderAppBO custDealOrderAppBO : custDealOrderAppList) {

            // 构建订单明细
            HwDealOrderDtlPO updateItemDtl = buildUpdateItemDtl(request, fundBasicInfoDTO, custDealOrderAppBO);

            //构建UpdateOrder
            HwDealOrderPO updateOrder = buildUpdateOrderItem(dealOrderPOMap, updateItemDtl, custDealOrderAppBO.getDealNo());

            //更新订单及订单明细
            hwDealOrderRepository.updateOrderAndDtl(updateOrder, updateItemDtl);
            LoggerUtils.recordDelaySubmitLog(custDealOrderAppBO.getDealDtlNo(), custDealOrderAppBO.getPreSubmitTaDt(), updateItemDtl.getPreSubmitTaDt());
        }
    }

    /**
     * @param request            请求
     * @param fundBasicInfoDTO   基金代码
     * @param custDealOrderAppBO 订单明细
     * @return com.howbuy.dtms.manager.dao.po.HwDealOrderDtlPO
     * @description: 构建订单明细
     * @author: jinqing.rao
     * @date: 2025/2/11 17:25
     * @since JDK 1.8
     */
    private HwDealOrderDtlPO buildUpdateItemDtl(BatchUpdatePreSubmitTaDtRequest request, FundBasicInfoDTO fundBasicInfoDTO, CustDealOrderAppBO custDealOrderAppBO) {
        HwDealOrderDtlPO updateItem = new HwDealOrderDtlPO();
        updateItem.setId(custDealOrderAppBO.getId());
        updateItem.setUpdateTimestamp(LocalDateTime.now());

        MiddleBusiCodeEnum middleBusiCodeEnum = MiddleBusiCodeEnum.getByCode(custDealOrderAppBO.getMiddleBusiCode());
        AssertUtil.notNull(fundBasicInfoDTO.getOrderEndTm(), "未获取到基金下单结束时间");
        String fundAppTime = DateUtils.adjustTimeByMinutes(fundBasicInfoDTO.getOrderEndTm(), -1);
        // 查询开放日信息
        OpenDtDTO openDtDTO = paramOuterService.queryOpenDtInfoRequest(custDealOrderAppBO.getFundCode(),
                middleBusiCodeEnum.getBusiCode(), request.getNewPreSubmitTaDt(), fundAppTime, Constants.ZERO_INTERVAL);
        AssertUtil.notNull(openDtDTO, String.format("基金【%s】不在开放期内", fundBasicInfoDTO.getFundCode()));

        // 非购买类
        if (!MiddleBusiCodeEnum.isBuyCategory(custDealOrderAppBO.getMiddleBusiCode())) {
            updateItem.setOpenDt(openDtDTO.getOpenDt());
            updateItem.setPreSubmitTaDt(openDtDTO.getPreSubmitTaDt());
            updateItem.setPreSubmitTaTm(openDtDTO.getPreSubmitTaTm());
            return updateItem;
        }

        // 购买类
        updateItem.setOpenDt(openDtDTO.getOpenDt());
        updateItem.setPreSubmitTaDt(openDtDTO.getPreSubmitTaDt());
        updateItem.setPreSubmitTaTm(openDtDTO.getPreSubmitTaTm());
        updateItem.setProductPayEndDt(openDtDTO.getPaymentDeadlineDt());
        updateItem.setProductPayEndDm(openDtDTO.getPaymentDeadlineTime());
        updateItem.setPayEndDt(openDtDTO.getPaymentDeadlineDt());
        updateItem.setPayEndTm(openDtDTO.getPaymentDeadlineTime());

        // 购买类-付款为成功-储蓄罐支付
        String[] paymentTypeArray = custDealOrderAppBO.getPaymentTypeList().split("");
        // 储蓄罐支付
        if (Objects.equals(paymentTypeArray[2], YesOrNoEnum.YES.getCode())) {
            // 储蓄罐支付 查询储蓄罐支付开放日
            FundBasicInfoDTO piggyFundInfo = paramOuterService.getPiggyFundInfo();
            OpenDtDTO cxgReedemOpenDtDTO = getPiggyRedeemOpenDtDTO(openDtDTO.getPaymentDeadlineDt(), piggyFundInfo);
            AssertUtil.notNull(cxgReedemOpenDtDTO, "新预计上报日期不支持储蓄罐支付(储蓄罐赎回间隔日)");

            updateItem.setPayEndDt(cxgReedemOpenDtDTO.getPreSubmitTaDt());
            updateItem.setPayEndTm(piggyFundInfo.getOrderEndTm());
            // 付款状态 付款中
            List<String> payStateList = Arrays.asList(PayStatusEnum.UN_PAY.getCode(), PayStatusEnum.PAYING.getCode());
            if (payStateList.contains(custDealOrderAppBO.getPayStatus())) {
                // 校验当前时间>=订单打款截止时间
                String requestDateTime = DateUtils.getCurrentDateStr(DateUtils.YYYYMMDDHHMMSS);
                String payEndDtTime = updateItem.getPayEndDt() + updateItem.getPayEndTm();
                if (requestDateTime.compareTo(payEndDtTime) >= 0) {
                    String message = String.format("订单号: %s当前时间大于新的订单打款截止时间，当前时间 : %s,订单打款截止时间 : %s",
                            custDealOrderAppBO.getDealNo(), requestDateTime, payEndDtTime);
                    log.error(message);
                    throw new BusinessException(ExceptionCodeEnum.PARAM_VALIDATE_ERROR.getCode(), message);
                }
            }
        }
        return updateItem;
    }

    /**
     * @param fundBasicInfoDTO
     * @param updateItem
     * @return java.lang.String
     * @description: 查询储蓄罐赎回的交易日
     * @author: jinqing.rao
     * @date: 2025/2/18 14:33
     * @since JDK 1.8
     */
    private String getPiggyFundReedemTradeDt(FundBasicInfoDTO fundBasicInfoDTO, HwDealOrderDtlPO updateItem) {
        ArrayList<String> fundStatList = Lists.newArrayList(FundStatEnum.TRADE.getCode(), FundStatEnum.STOP_SUBS.getCode());
        String interval = "-1";
        return getPiggyFundTrade(updateItem.getOpenDt(), fundStatList, interval);
    }

    /**
     * @param paymentDeadlineDt
     * @param piggyFundInfo
     * @return com.howbuy.dtms.manager.outservice.param.dto.OpenDtDTO
     * @description:(获取储蓄罐赎回开放日信息)
     * <AUTHOR>
     * @date 2025/7/30 15:20
     * @since JDK 1.8
     */
    private OpenDtDTO getPiggyRedeemOpenDtDTO(String paymentDeadlineDt, FundBasicInfoDTO piggyFundInfo) {
        AssertUtil.notNull(piggyFundInfo, "未获取到有效的储蓄罐基金配置");
        AssertUtil.notNull(piggyFundInfo.getOrderEndTm(), "未获取到储蓄罐基金下单结束时间");
        AssertUtil.notNull(piggyFundInfo.getPiggyAutoRedeemIntervalDays(), "储蓄罐自动赎回间隔天数为空");

        String cxgAppTime = DateUtils.adjustTimeByMinutes(piggyFundInfo.getOrderEndTm(), -1);
        // 查询赎回储蓄罐开放日信息
        OpenDtDTO cxgRedeemOpenDtDTO = paramOuterService.queryOpenDtInfoRequest(
                piggyFundInfo.getFundCode(),
                BusinessCodeEnum.REDEEM.getCode(),
                paymentDeadlineDt,
                cxgAppTime,
                -piggyFundInfo.getPiggyAutoRedeemIntervalDays()
        );

        return cxgRedeemOpenDtDTO;
    }

    /**
     * @param fundBasicInfoDTO
     * @param request
     * @return void
     * @description: 每日开放产品校验
     * @author: jinqing.rao
     * @date: 2025/2/11 14:20
     * @since JDK 1.8
     */
    private void openDailyTradeValidate(FundBasicInfoDTO fundBasicInfoDTO, BatchUpdatePreSubmitTaDtRequest request,
                                        List<CustDealOrderAppBO> custDealOrderAppList) {
        String preSubmitTaDt = request.getNewPreSubmitTaDt();
        // 当前基金已柜台收市，不允许修改为当前交易日
        checkCounterCloseStatus(fundBasicInfoDTO, preSubmitTaDt);
        WorkdayVO workdayVO = dtmsSettleOuterService.queryWorkday();
        // 预计上报日不允许选择历史日期！
        checkWorkdayVO(preSubmitTaDt, workdayVO);
        // 存在储蓄罐支付的订单，不允许修改为当前工作日！
        checkPiggyPayStatus(custDealOrderAppList, fundBasicInfoDTO, preSubmitTaDt, workdayVO);
        // 【基金{基金代码}不在开放期内】
        checkFundNavStatus(fundBasicInfoDTO, request.getNewPreSubmitTaDt(), BusinessCodeEnum.getByMCode(custDealOrderAppList.get(0).getMiddleBusiCode()));
    }

    /**
     * @param fundBasicInfoDTO
     * @param preSubmitTaDt
     * @return void
     * @description: 【基金{基金代码}不在开放期内】
     * @author: jinqing.rao
     * @date: 2025/2/11 17:21
     * @since JDK 1.8
     */
    private void checkFundNavStatus(FundBasicInfoDTO fundBasicInfoDTO, String preSubmitTaDt, BusinessCodeEnum businessCodeEnum) {

        List<String> fundStatList = getFundStatList(businessCodeEnum);

        // 查询指定日期的基金净值状态是否存在
        FundNavStatusDTO fundNavStatusDTO = getFundNavStatusDTO(fundBasicInfoDTO, preSubmitTaDt);
        if (!fundStatList.contains(fundNavStatusDTO.getFundStat())) {
            String msg = String.format("基金{%s}不在开放期内", fundBasicInfoDTO.getFundCode());
            ExceptionUtils.throwException(com.howbuy.dtms.settle.client.enums.ExceptionCodeEnum.PARAM_VALIDATE_ERROR.getCode(), msg);
        }
    }

    /**
     * @param businessCodeEnum
     * @return java.util.List<java.lang.String>
     * @description: 获取基金状态
     * @author: jinqing.rao
     * @date: 2025/2/12 18:24
     * @since JDK 1.8
     */
    public static List<String> getFundStatList(BusinessCodeEnum businessCodeEnum) {
        if (BusinessCodeEnum.SUBS.equals(businessCodeEnum)) {
            return Lists.newArrayList(FundStatEnum.IPO.getCode());
        } else if (BusinessCodeEnum.PURCHASE.equals(businessCodeEnum)
                || BusinessCodeEnum._112A.equals(businessCodeEnum)
                || BusinessCodeEnum._112B.equals(businessCodeEnum)) {
            return Lists.newArrayList(FundStatEnum.TRADE.getCode(), FundStatEnum.STOP_REDEEM.getCode());
        } else {
            return Lists.newArrayList(FundStatEnum.TRADE.getCode(), FundStatEnum.STOP_SUBS.getCode());
        }
    }

    /**
     * @param custDealOrderAppList
     * @return void
     * @description: 储蓄罐支付需要时支付成功状态
     * @author: jinqing.rao
     * @date: 2025/2/11 17:21
     * @since JDK 1.8
     */
    private void checkPiggyPayStatus(List<CustDealOrderAppBO> custDealOrderAppList, FundBasicInfoDTO fundBasicInfoDTO, String preSubmitTaDt, WorkdayVO workdayVO) {
        custDealOrderAppList.stream()
                .filter(order -> {
                    if (MiddleBusiCodeEnum.isBuyCategory(order.getMiddleBusiCode())) {
                        String[] paymentTypeArray = order.getPaymentTypeList().split("");
                        // 储蓄罐支付 + 支付不成功的
                        return Objects.equals(paymentTypeArray[2], YesOrNoEnum.YES.getCode()) && PayStatusEnum.isNotPaySuccess(order.getPayStatus());
                    }
                    return false;
                }).filter(piggyOrder -> {
                    if (MiddleBusiCodeEnum.isBuyCategory(piggyOrder.getMiddleBusiCode())) {
                        // 获取当前时间
                        boolean orderEndTimeFlag = DateUtils.getNowTm().compareTo(fundBasicInfoDTO.getOrderEndTm()) >= 0;
                        if (orderEndTimeFlag) {
                            return Objects.requireNonNull(DateUtils.addDay(workdayVO.getWorkday(), 1)).compareTo(preSubmitTaDt) >= 0;
                        }
                        return workdayVO.getWorkday().compareTo(preSubmitTaDt) == 0;
                    }
                    return false;
                }).findAny().ifPresent(order -> {
                    String msg = String.format("存在储蓄罐支付的订单【%s】，不允许选择<=当前工作日！", order.getDealDtlNo());
                    ExceptionUtils.throwException(ExceptionCodeEnum.PARAM_VALIDATE_ERROR.getCode(), msg);
                });
    }

    /**
     * @param preSubmitTaDt
     * @return void
     * @description: 检查是否是工作日
     * @author: jinqing.rao
     * @date: 2025/2/11 17:22
     * @since JDK 1.8
     */
    private void checkWorkdayVO(String preSubmitTaDt, WorkdayVO workdayVO) {

        if (preSubmitTaDt.compareTo(workdayVO.getWorkday()) < 0) {
            String msg = "【预计上报日】不允许选择历史日期！";
            ExceptionUtils.throwException(ExceptionCodeEnum.PARAM_VALIDATE_ERROR.getCode(), msg);
        }
    }

    /**
     * @param fundBasicInfoDTO 基金信息
     * @param preSubmitTaDt    预约上报日期
     * @return void
     * @description: 检查柜台收市状态
     * @author: jinqing.rao
     * @date: 2025/2/11 17:22
     * @since JDK 1.8
     */
    private void checkCounterCloseStatus(FundBasicInfoDTO fundBasicInfoDTO, String preSubmitTaDt) {
        CounterCloseStatusDTO counterCloseStatusDTO = dtmsSettleOuterService.queryCounterCloseStatus(fundBasicInfoDTO.getFundCode());
        // 已柜台收市
        if (YesOrNoEnum.YES.getCode().equals(counterCloseStatusDTO.getStat())) {
            // 【预计上报日】<= 当前香港交易日
            if (preSubmitTaDt.compareTo(counterCloseStatusDTO.getTradeDt()) <= 0) {
                String msg = "当前基金已柜台收市，不允许修改为当前交易日";
                ExceptionUtils.throwException(ExceptionCodeEnum.PARAM_VALIDATE_ERROR.getCode(), msg);
            }
        }
    }

    /**
     * @param dealOrderPOMap
     * @param updateItem
     * @param dealNo
     * @return com.howbuy.dtms.manager.dao.po.HwDealOrderPO
     * @description:(构建UpdateOrderItem)
     * <AUTHOR>
     * @date 2024/12/23 17:45
     * @since JDK 1.8
     */
    private HwDealOrderPO buildUpdateOrderItem(Map<Long, HwDealOrderPO> dealOrderPOMap, HwDealOrderDtlPO updateItem, Long dealNo) {
        HwDealOrderPO hwDealOrderPO = dealOrderPOMap.get(dealNo);
        if (Objects.isNull(hwDealOrderPO)) {
            ExceptionUtils.throwException(ExceptionCodeEnum.PARAM_VALIDATE_ERROR.getCode(), String.format("订单不存在【%s】", dealNo));
        }

        HwDealOrderPO updateOrderItem = new HwDealOrderPO();
        updateOrderItem.setId(hwDealOrderPO.getId());
        updateOrderItem.setOpenDt(updateItem.getOpenDt());
        updateOrderItem.setUpdateTimestamp(LocalDateTime.now());
        return updateOrderItem;
    }

    /**
     * 赎回订单更新处理
     *
     * @param request
     * @param hwDealOrderDtlPO
     * @param fundBasicInfoDTO
     * @param middleBusiCodeEnum
     * @param updateItem
     */
    private void updateRedeemOrder(BatchUpdatePreSubmitTaDtRequest request, HwDealOrderDtlPO hwDealOrderDtlPO, FundBasicInfoDTO fundBasicInfoDTO, MiddleBusiCodeEnum middleBusiCodeEnum, HwDealOrderDtlPO updateItem) {
        if (!MiddleBusiCodeEnum.isRedeemCategory(hwDealOrderDtlPO.getMiddleBusiCode())) {
            return;
        }
        List<String> fundStatList = Lists.newArrayList();
        fundStatList.add(FundStatEnum.TRADE.getCode());
        fundStatList.add(FundStatEnum.STOP_SUBS.getCode());

        if (IsScheduledTradeEnum.isSupportedRedeemScheduledTrade(fundBasicInfoDTO.getIsScheduledTrade())) {
            FundTradeCalendarInfoDTO fundTradeCalendarInfoDTO = getFundTradeCalendarInfoDTO(request, hwDealOrderDtlPO, middleBusiCodeEnum);
            updateItem.setPreSubmitTaDt(request.getNewPreSubmitTaDt());
            updateItem.setPreSubmitTaTm(fundTradeCalendarInfoDTO.getAdvanceEndTm());
            updateItem.setOpenDt(fundTradeCalendarInfoDTO.getOpenEndDt());
        }
    }

    /**
     * 非储蓄罐（电汇）支付订单更新处理
     *
     * @param request
     * @param hwDealOrderDtlPO
     * @param fundBasicInfoDTO
     * @param middleBusiCodeEnum
     * @param updateItem
     */
    private void updateBuyCategoryOrderWithPaymentByWireTransfer(BatchUpdatePreSubmitTaDtRequest request, HwDealOrderDtlPO hwDealOrderDtlPO, FundBasicInfoDTO fundBasicInfoDTO, MiddleBusiCodeEnum middleBusiCodeEnum, HwDealOrderDtlPO updateItem) {
        String interval = "0";
        List<String> fundStatList = Lists.newArrayList();
        fundStatList.add(FundStatEnum.TRADE.getCode());
        fundStatList.add(FundStatEnum.STOP_REDEEM.getCode());

        if (IsScheduledTradeEnum.isSupportedBuyScheduledTrade(fundBasicInfoDTO.getIsScheduledTrade())) {
            FundTradeCalendarInfoDTO fundTradeCalendarInfoDTO = getFundTradeCalendarInfoDTO(request, hwDealOrderDtlPO, middleBusiCodeEnum);
            updateItem.setPreSubmitTaDt(request.getNewPreSubmitTaDt());
            updateItem.setPreSubmitTaTm(fundTradeCalendarInfoDTO.getAdvanceEndTm());
            updateItem.setOpenDt(fundTradeCalendarInfoDTO.getOpenEndDt());

            updateItem.setPayEndDt(fundTradeCalendarInfoDTO.getPaymentDeadlineDt());
            updateItem.setPayEndTm(fundTradeCalendarInfoDTO.getPaymentDeadlineTime());
        }
    }

    /**
     * 储蓄罐支付订单更新处理
     *
     * @param request
     * @param hwDealOrderDtlPO
     * @param fundBasicInfoDTO
     * @param middleBusiCodeEnum
     * @param updateItem
     */
    private void updateBuyCategoryOrderWithPaymentByPiggy(BatchUpdatePreSubmitTaDtRequest request, HwDealOrderDtlPO hwDealOrderDtlPO, FundBasicInfoDTO fundBasicInfoDTO, MiddleBusiCodeEnum middleBusiCodeEnum, HwDealOrderDtlPO updateItem) {
        String interval = "0";
        List<String> fundStatList = Lists.newArrayList();
        fundStatList.add(FundStatEnum.TRADE.getCode());
        fundStatList.add(FundStatEnum.STOP_SUBS.getCode());

        FundBasicInfoDTO piggyFundInfo = paramOuterService.getPiggyFundInfo();
        AssertUtil.notNull(piggyFundInfo, "未获取到有效的储蓄罐基金配置");
        AssertUtil.notNull(piggyFundInfo.getOrderEndTm(), "未获取到储蓄罐基金下单结束时间");
        if (IsScheduledTradeEnum.isSupportedBuyScheduledTrade(fundBasicInfoDTO.getIsScheduledTrade())) {
            FundTradeCalendarInfoDTO fundTradeCalendarInfoDTO = getFundTradeCalendarInfoDTO(request, hwDealOrderDtlPO, middleBusiCodeEnum);
            updateItem.setPreSubmitTaDt(request.getNewPreSubmitTaDt());
            updateItem.setPreSubmitTaTm(fundTradeCalendarInfoDTO.getAdvanceEndTm());
            updateItem.setOpenDt(fundTradeCalendarInfoDTO.getOpenEndDt());
            // 开发日历打款截止日期 - 1T
            interval = "-1";
            FundNavStatusDTO fundNavStatusDTO = getFundNavStatusDTO(piggyFundInfo, fundTradeCalendarInfoDTO.getPaymentDeadlineDt(), fundStatList, interval);
            // 当前日期 > 预约起始日期时，用当前日期 与净值日期进行比较，否则取预约起始日期与净值日期比较
            String nowDt = DateUtils.getNowDt();
            String validateDt = nowDt.compareTo(fundTradeCalendarInfoDTO.getAdvanceStartDt()) > 0 ? nowDt : fundTradeCalendarInfoDTO.getAdvanceStartDt();

            // 比较日期大于净值日期时， 报错提醒：基金【%s】不在开放期内
            if (validateDt.compareTo(fundNavStatusDTO.getTradeDt()) > 0) {
                AssertUtil.notNull(null, String.format("储蓄罐基金【%s】不在开放期内", piggyFundInfo.getFundCode()));
            }

            updateItem.setPayEndDt(fundNavStatusDTO.getTradeDt());
            updateItem.setPayEndTm(piggyFundInfo.getOrderEndTm());
        }
    }

    public String getPiggyFundTrade(String openDt, List<String> fundStatList, String interval) {
        FundBasicInfoDTO piggyFundInfo = paramOuterService.getPiggyFundInfo();
        AssertUtil.notNull(piggyFundInfo, "未获取到有效的储蓄罐基金配置");
        AssertUtil.notNull(piggyFundInfo.getOrderEndTm(), "未获取到储蓄罐基金下单结束时间");
        FundNavStatusDTO fundNavStatusDTO = getFundNavStatusDTO(piggyFundInfo, openDt, fundStatList, interval);
        return fundNavStatusDTO.getTradeDt();
    }


    private FundNavStatusDTO getFundNavStatusDTO(FundBasicInfoDTO fundBasicInfoDTO, String appDt) {
        FundNavStatusByAppDtRequestDTO requestDTO = new FundNavStatusByAppDtRequestDTO();
        requestDTO.setFundCode(fundBasicInfoDTO.getFundCode());
        requestDTO.setAppDt(appDt);
        FundNavStatusDTO fundNavStatusDTO = paramOuterService.queryFundNavStatus(requestDTO);
        if (Objects.equals(fundBasicInfoDTO.getIsPiggyFund(), YesOrNoEnum.YES.getCode())) {
            AssertUtil.notNull(fundNavStatusDTO, String.format("储蓄罐基金【%s】不在开放期内", fundBasicInfoDTO.getFundCode()));
        } else {
            AssertUtil.notNull(fundNavStatusDTO, String.format("基金【%s】不在开放期内", fundBasicInfoDTO.getFundCode()));
        }
        return fundNavStatusDTO;
    }

    private FundNavStatusDTO getFundNavStatusDTO(FundBasicInfoDTO fundBasicInfoDTO, String appDt, List<String> fundStatList, String interval) {
        FundNavStatusByAppDtRequestDTO requestDTO = new FundNavStatusByAppDtRequestDTO();
        requestDTO.setFundCode(fundBasicInfoDTO.getFundCode());
        requestDTO.setAppDt(appDt);
        requestDTO.setInterval(interval);
        requestDTO.setFundStatList(fundStatList);
        FundNavStatusDTO fundNavStatusDTO = paramOuterService.queryFundNavStatusByAppDt(requestDTO);
        if (Objects.equals(fundBasicInfoDTO.getIsPiggyFund(), YesOrNoEnum.YES.getCode())) {
            AssertUtil.notNull(fundNavStatusDTO, String.format("储蓄罐基金【%s】不在开放期内", fundBasicInfoDTO.getFundCode()));
        } else {
            AssertUtil.notNull(fundNavStatusDTO, String.format("基金【%s】不在开放期内", fundBasicInfoDTO.getFundCode()));
        }
        return fundNavStatusDTO;
    }

    private List<ConfirmVO> confirmValidate(BatchUpdatePreSubmitTaDtRequest request, List<CustDealOrderAppBO> custDealOrderAppList, FundBasicInfoDTO fundBasicInfoDTO, HwDealOrderDtlPO po) {
        List<ConfirmVO> confirmVOList = Lists.newArrayList();
        for (HwDealOrderDtlPO hwDealOrderDtlPO : custDealOrderAppList) {
            MiddleBusiCodeEnum middleBusiCodeEnum = MiddleBusiCodeEnum.getByCode(hwDealOrderDtlPO.getMiddleBusiCode());
            if (IsScheduledTradeEnum.isSupportedRedeemScheduledTrade(fundBasicInfoDTO.getIsScheduledTrade())) {
                AssertUtil.notNull(request.getNewPreSubmitTaDt(), "预计上报日期不能为空");
                FundTradeCalendarInfoDTO fundTradeCalendarInfoDTO = getFundTradeCalendarInfoDTO(request, hwDealOrderDtlPO, middleBusiCodeEnum);
                AssertUtil.notNull(fundTradeCalendarInfoDTO, "未获取到有效的预约交易日历");
                if (fundTradeCalendarInfoDTO != null) {
                    String compareDt = DateUtils.addDay(fundTradeCalendarInfoDTO.getAdvanceEndDt(), 2);
                    assert compareDt != null;
                    if (compareDt.compareTo(request.getNewPreSubmitTaDt()) < 0
                            && hwDealOrderDtlPO.getOpenDt().compareTo(request.getNewPreSubmitTaDt()) >= 0) {
                        String msg = String.format("预约结束日期+2D<预计上报日<=订单的开放日，预约结束日期+2D=%s,预计上报日=%s,订单的开放日=%s",
                                compareDt,
                                request.getNewPreSubmitTaDt(),
                                hwDealOrderDtlPO.getOpenDt());
                        log.info(msg);
                        DealOrderDtlSubmitConfirmVO confirmVO = buildConfirmVO(po);
                        confirmVO.setPreSubmitTaDt(request.getNewPreSubmitTaDt());
                        confirmVO.setConfirmCode(ExceptionCodeEnum.NEW_PRE_SUBMIT_TA_DT_EXCEEDS_TWO_DAYS.getCode());
                        confirmVO.setConfirmMsg("修改的预计上报日超过预约结束日期2天，是否继续修改？");
                        confirmVOList.add(confirmVO);
                    }
                }
            }
        }
        return confirmVOList;
    }

    private FundTradeCalendarInfoDTO getFundTradeCalendarInfoDTO(BatchUpdatePreSubmitTaDtRequest request, HwDealOrderDtlPO hwDealOrderDtlPO, MiddleBusiCodeEnum middleBusiCodeEnum) {
        FundTradeCalendarInfoRequestDTO requestDTO = new FundTradeCalendarInfoRequestDTO();
        requestDTO.setFundCode(hwDealOrderDtlPO.getFundCode());
        requestDTO.setBusiCode(middleBusiCodeEnum.getBusiCode());
        requestDTO.setAppDt(request.getNewPreSubmitTaDt());
        FundTradeCalendarInfoDTO fundTradeCalendarInfoDTO = paramOuterService.queryFundTradeCalendarInfo(requestDTO);
        AssertUtil.notNull(fundTradeCalendarInfoDTO, "未获取到有效的预约交易日历");
        return fundTradeCalendarInfoDTO;
    }

    private static DealOrderDtlSubmitConfirmVO buildConfirmVO(HwDealOrderDtlPO po) {
        DealOrderDtlSubmitConfirmVO confirmVO = new DealOrderDtlSubmitConfirmVO();
        confirmVO.setDealDtlNo(po.getDealDtlNo());
        confirmVO.setHkCustNo(po.getHkCustNo());
        confirmVO.setFundCode(po.getFundCode());
        return confirmVO;
    }

    private void validate(List<CustDealOrderAppBO> custDealOrderAppBOList) {
        for (HwDealOrderDtlPO hwDealOrderDtlPO : custDealOrderAppBOList) {
            validate(hwDealOrderDtlPO);
        }
        Map<String, List<HwDealOrderDtlPO>> collectBOfGroupByFundCode = custDealOrderAppBOList.stream().collect(Collectors.groupingBy(HwDealOrderDtlPO::getFundCode));
        if (collectBOfGroupByFundCode.size() > 1) {
            String msg = "存在订单的基金代码不是同一产品，请按产品批量修改";
            ExceptionUtils.throwException(ExceptionCodeEnum.PARAM_VALIDATE_ERROR.getCode(), msg);
        }
        Map<String, List<HwDealOrderDtlPO>> collectBOfGroupByPreSubmitTaDt = custDealOrderAppBOList.stream().collect(Collectors.groupingBy(HwDealOrderDtlPO::getPreSubmitTaDt));

        if (collectBOfGroupByPreSubmitTaDt.size() > 1) {
            String msg = "存在订单预计上报日不是同一天";
            ExceptionUtils.throwException(ExceptionCodeEnum.PARAM_VALIDATE_ERROR.getCode(), msg);
        }

        Map<String, List<HwDealOrderDtlPO>> collectBOfGroupByMiddleBusiCode = custDealOrderAppBOList.stream().collect(Collectors.groupingBy(HwDealOrderDtlPO::getMiddleBusiCode));

        if (collectBOfGroupByMiddleBusiCode.size() > 1) {
            String msg = "存在订单业务类型不相同";
            ExceptionUtils.throwException(ExceptionCodeEnum.PARAM_VALIDATE_ERROR.getCode(), msg);
        }
    }


    public void validate(HwDealOrderDtlPO po) {
        if (!SubmitStatusEnum.allowSubmit(po.getSubmitStatus())) {
            String msg = String.format("存在明细订单【%s】是【%s】", po.getDealDtlNo(), SubmitStatusEnum.getDescByCode(po.getSubmitStatus()));
            ExceptionUtils.throwException(ExceptionCodeEnum.PARAM_VALIDATE_ERROR.getCode(), msg);
        }

        if (!Objects.equals(po.getAppStatus(), AppStatusEnum.APP_SUCCESS.getCode())
                || !Objects.equals(AckStatusEnum.UN_CONFIRMED.getCode(), po.getAckStatus())) {
            String msg = String.format("存在明细订单【%s】不满足修改条件", po.getDealDtlNo());
            ExceptionUtils.throwException(ExceptionCodeEnum.PARAM_VALIDATE_ERROR.getCode(), msg);
        }
    }

}
