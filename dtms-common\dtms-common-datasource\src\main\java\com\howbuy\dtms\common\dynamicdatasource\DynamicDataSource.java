package com.howbuy.dtms.common.dynamicdatasource;

import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class DynamicDataSource extends AbstractRoutingDataSource {

    /**
     * 默认 routeKey
     */
    private String defaultRouteKey = "mysql";

    private Map<Object, Object> targetDataSources = new HashMap<>();

    @Override
    public void afterPropertiesSet() {
        super.setTargetDataSources(targetDataSources);
        super.afterPropertiesSet();
    }

    /**
     * 该方法用来获取数据源名称的 当系统需要使用数据源的时候，会调用该方法获取
     * @return
     */
    @Override
    protected Object determineCurrentLookupKey() {
        String key = RouteHolder.getRouteKey();
        if(key != null && !key.isEmpty()){
            return key;
        }
        if(defaultRouteKey != null && !defaultRouteKey.isEmpty()){
            return defaultRouteKey;
        }
        throw new NullPointerException("No routeKey is set.");
    }

    /**
     * 设置默认 routeKey
     * @param defaultRouteKey
     */
    public void setDefaultRouteKey(String defaultRouteKey){
        this.defaultRouteKey = defaultRouteKey;
    }

    /**
     * 设置数据源
     * @param targetDataSources
     */
    public void setTargetDataSources(Map<Object, Object> targetDataSources) {
        this.targetDataSources = targetDataSources;
    }
}