/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.manager.outservice.dtmsorder.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/7/21 18:20
 * @since JDK 1.8
 */
@Getter
@Setter
public class UpdatePiggyTradeAppBuyRequestDTO extends DtmsOrderBaseRequestDTO {
    /**
     * 导入申请id
     */
    private String importAppId;

    /**
     * 买入金额
     */
    private String buyAmt;

    /**
     * 折扣率
     */
    private String discountRate;

    /**
     * 手续费
     */
    private String fee;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 备注
     */
    private String remark;
}
