* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0;
    margin: 0;
    vertical-align: baseline;
    border: none;
    outline: 0;
}
html {
    width: 100%;
    height: 100%; 
    background-color: #f4f6f9;
}
html,
body {
    position: relative;
    color: #333;
    font: 12px 'Microsoft YaHei', '微软雅黑', Arial, Helvetica, sans-serif;
    /* font-family: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif"; */
    /* font-size: 12px; */
    /* font-feature-settings: 'tnum';
    font-variant: tabular-nums;
    text-rendering: optimizeLegibility; */
}

html,
body,
form,
fieldset,
p,
div,
h1,
h2,
h3,
h4,
h5,
h6 {
    -webkit-text-size-adjust: none;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    display: block;
}

ol,
ul {
    list-style: none;
}

del {
    text-decoration: line-through;
}
table {
    border-spacing: 0;
    border-collapse: collapse;
}
a {
    color: #333;
    text-decoration: none;
    outline: none;
}

input[type='submit'],
input[type='button'],
input[type='text'],
textarea,
input[type='password'],
input[type='tel'] {
    -webkit-appearance: none;
}
input[type='text'],
input[type='password'],
input[type='tel'],
input[type='number'] {
    line-height: normal;
}
/* 修改input自带的背景颜色 */
input:-internal-autofill-selected {
    box-shadow: inset 0 0 0 1000px #ffffff !important;
    /* -webkit-text-fill-color: #c0aefa;  */
}

button {
    font: inherit;
}
select {
    background: transparent;
    border: 0;
    -webkit-appearance: none;
}

::-webkit-input-placeholder {
    color: #ccc;
}
::-moz-placeholder {
    color: #ccc;
}

/* 去掉marker伪类 */
::marker {
    content: '';
}
