# 1. 初始化入参
# 2. Mock获取储蓄罐基金列表
# 3. 如果支持购买预约
## 3.1 获取预约交易日历
## 3.2 更新预约上报日期=录入的预约上报日期、预约上报时间=开发日历的预约截止时间、开放日=开发日历的开发截止日期
## 3.3 获取储蓄罐基金净值状态（Mock获取不到的不考虑，获取方法中有异常处理逻辑）
## 3.4 比较获取净值状态的日期是否小于 MAX（当前日期，预约起始日期），如果小于，则抛异常提示：基金【%s】不在开放期内
## 3.5 如果不小于，更新支付截止日期=储蓄罐基金净值交易日期、支付截止时间=储蓄罐基金的下单截止时间
# 4 不支持预约
## 4.1 判断是否大于等于储蓄罐基金的下单截止时间，如果大于，指定间隔天数+1
## 4.2 获取储蓄罐基金净值状态（Mock获取不到的不考虑，获取方法中有异常处理逻辑）
## 4.3 更新支付截止日期=储蓄罐基金净值交易日期、支付截止时间=储蓄罐基金下单截止时间、预计上报日期=储蓄罐基金净值交易日期、预计上报时间=目标基金的上报管理人时间
## 4.4 获取目标基金的净值状态，跟新开放日=净值状态交易日