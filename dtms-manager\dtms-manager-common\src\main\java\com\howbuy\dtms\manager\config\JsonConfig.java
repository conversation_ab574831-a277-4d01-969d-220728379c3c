package com.howbuy.dtms.manager.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.howbuy.dtms.manager.constant.Constants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.jackson.JsonComponent;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <AUTHOR>
 * @description JSON日期转换配置
 * @date 2024-04-10
 */
@JsonComponent
public class JsonConfig {

    public static class LocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {
        @Override
        public LocalDateTime deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JsonProcessingException {
            if (StringUtils.isNumeric(jsonParser.getValueAsString())) { // 如果是时间戳形式
                return new Date(jsonParser.getValueAsLong()).toInstant().atOffset(ZoneOffset.of(Constants.DEFAULT_TIME_ZONE)).toLocalDateTime();
            } else { // 默认是yyyy-MM-dd HH:mm:ss 形式，如果有多种格式，可以自行拓展
                return LocalDateTime.parse(jsonParser.getValueAsString(), DateTimeFormatter.ofPattern(Constants.DEFAULT_LOCAL_DATE_TIME_FORMATTER));
            }
        }
    }
    public static class BigDecimalDeserializer extends JsonDeserializer<BigDecimal> {
        @Override
        public BigDecimal deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JsonProcessingException {
            if (StringUtils.isEmpty(jsonParser.getValueAsString())) {
                return null;
            } else {
                return new BigDecimal(jsonParser.getValueAsString());
            }
        }

        @Override
        public BigDecimal deserialize(JsonParser p, DeserializationContext ctx, BigDecimal intoValue) throws IOException {
            if (StringUtils.isEmpty(p.getValueAsString())) {
                return null;
            } else {
                return new BigDecimal(p.getValueAsString());
            }
        }
    }

    public static class LocalDateTimeSerializer extends JsonSerializer<LocalDateTime> {
        @Override
        public void serialize(LocalDateTime localDateTime, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
            jsonGenerator.writeString(localDateTime.format(DateTimeFormatter.ofPattern(Constants.DEFAULT_LOCAL_DATE_TIME_FORMATTER)));
        }
    }
}
