# DTMS外部Dubbo接口封装规范

> 本规范基于`DtmsCounterOrderOuterService`的实现，总结了对外部Dubbo接口封装的最佳实践。
> 作者: shaoyang.li
> 日期: 2025-03-25 17:21:21

## 1. 类命名规范

### 1.1 类命名规则
- 使用`{系统名}{业务域}OuterService`格式命名
- 采用PascalCase命名法
- 系统名表示被调用的外部系统
- 业务域表示具体业务领域

```java
public class DtmsCounterOrderOuterService { ... }
public class DtmsTradeOrderOuterService { ... }
```

### 1.2 类注解规范
- 使用`@Component`标注为Spring组件
- 类注释需包含作者、描述、创建日期等信息
- 继承关系应在注释中说明

```java
/**
 * 柜台订单外部服务封装
 *
 * <AUTHOR>
 * @description: 封装对DTMS订单系统的柜台订单相关接口调用
 * @date yyyy/MM/dd HH:mm
 * @since JDK 1.8
 */
@Component
public class DtmsCounterOrderOuterService { ... }
```

### 1.3 依赖注入规范
- 使用`@DubboReference`注入Dubbo服务
- 必须指定注册中心和容错配置
- 变量名应体现接口的用途

```java
@DubboReference(registry = RegistryIdConstant.DTMS_ORDER_REMOTE, check = false)
private CounterOrderAuditFacade counterOrderAuditFacade;
```

## 2. 方法规范

### 2.1 方法命名规范
- 方法名应反映实际业务操作
- 采用驼峰命名法
- 命名应与原始接口保持关联但更具业务语义

```java
// 原始接口: counterOrderAuditPass
// 封装方法: counterOrderAuditPass
public void counterOrderAuditPass(CounterOrderAuditDTO counterOrderAuditDTO) { ... }
```

### 2.2 参数封装规范
- 使用专用的DTO对象封装请求参数
- DTO类应位于dto包下
- 字段命名与原始接口保持一致
- 必要时提供参数转换方法

```java
public class CounterOrderAuditDTO {
    private String auditStatus;
    private String remark;
    private String operator;
    // ... 其他字段
}
```

### 2.3 异常处理规范
- 统一使用BusinessException封装异常
- 定义明确的异常码和异常信息
- 对空值和异常情况进行处理

```java
if (null == response) {
    throw new BusinessException(ExceptionCodeEnum.UPDATE_COUNTER_ORDER_FAIL);
}
if (!response.isSuccess()) {
    throw new BusinessException(ExceptionCodeEnum.UPDATE_COUNTER_ORDER_FAIL);
}
```

### 2.4 返回值规范
- 返回值类型应符合业务需求
- 对于查询类接口，返回封装后的VO对象
- 对于操作类接口，返回操作结果或标识

```java
public String saveCounterOrderInfo(CounterOrderAggregationSaveDTO dto) {
    // ... 处理逻辑
    return data.getAppSerialNo();
}
```

## 3. 数据转换规范

### 3.1 请求对象转换
- 创建私有的转换方法
- 方法名使用`get{目标对象}`格式
- 确保所有必要字段都被正确转换

```java
private static CounterAuditOrderRequest getCounterAuditOrderRequest(CounterOrderSaveDTO source) {
    CounterAuditOrderRequest target = new CounterAuditOrderRequest();
    target.setAppSerialNo(source.getAppSerialNo());
    // ... 其他字段转换
    return target;
}
```

### 3.2 集合转换规范
- 使用Stream API进行集合转换
- 转换方法应独立封装
- 注意处理空集合情况

```java
private static List<CounterAuditOrderDtlRequest> getCounterAuditOrderDtlRequestList(
        List<CounterOrderDetailSaveDTO> sourceList) {
    return sourceList.stream()
            .map(DtmsCounterOrderOuterService::getCounterAuditOrderDtlRequest)
            .collect(Collectors.toList());
}
```

## 4. 接口调用规范

### 4.1 参数校验
- 调用外部接口前进行参数校验
- 对必要字段进行非空检查
- 对特殊格式数据进行格式校验

```java
public void counterOrderAuditPass(CounterOrderAuditDTO dto) {
    Assert.notNull(dto, "请求参数不能为空");
    Assert.hasText(dto.getAppSerialNo(), "柜台订单号不能为空");
    // ... 其他校验
}
```

### 4.2 结果处理
- 统一处理接口返回结果
- 对异常情况进行适当转换
- 确保返回数据的一致性

```java
Response<CounterOrderBuyVO> response = counterOrderFacade.saveCounterOrderInfo(request);
if (null == response || !response.isSuccess()) {
    throw new BusinessException(ExceptionCodeEnum.SAVE_COUNTER_ORDER_FAIL);
}
return Optional.ofNullable(response.getData())
        .map(CounterOrderBuyVO::getAppSerialNo)
        .orElse(null);
```

### 4.3 日志记录
- 记录关键业务操作日志
- 包含必要的参数信息
- 记录异常和错误信息

```java
@Slf4j
public class DtmsCounterOrderOuterService {
    public void counterOrderAuditPass(CounterOrderAuditDTO dto) {
        log.info("开始审核柜台订单, 参数:{}", JSON.toJSONString(dto));
        try {
            // ... 业务处理
            log.info("审核柜台订单成功, 订单号:{}", dto.getAppSerialNo());
        } catch (Exception e) {
            log.error("审核柜台订单失败, 订单号:{}, 异常信息:{}",
                dto.getAppSerialNo(), e.getMessage(), e);
            throw e;
        }
    }
}
```

## 5. 最佳实践

### 5.1 接口封装原则
- 保持接口的单一职责
- 提供清晰的业务语义
- 屏蔽外部接口的复杂性
- 统一异常处理方式

### 5.2 性能优化
- 合理使用批量接口
- 避免重复对象创建
- 使用适当的集合类型
- 注意内存使用效率

### 5.3 可维护性
- 提供完整的注释文档
- 保持代码结构清晰
- 遵循统一的命名规范
- 适当抽取公共方法

## 6. 注意事项

### 6.1 接口版本控制
- 注意接口的版本兼容性
- 在注释中说明版本信息
- 处理接口升级情况

### 6.2 安全性考虑
- 注意敏感数据处理
- 避免敏感信息泄露
- 确保接口调用安全

### 6.3 可用性保障
- 实现合理的容错机制
- 提供降级方案
- 监控接口调用情况