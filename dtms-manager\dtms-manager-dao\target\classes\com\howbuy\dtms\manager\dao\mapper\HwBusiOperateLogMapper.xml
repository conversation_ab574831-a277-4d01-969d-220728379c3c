<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.HwBusiOperateLogMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwBusiOperateLogPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="appId" column="app_id" jdbcType="BIGINT"/>
            <result property="paramType" column="param_type" jdbcType="VARCHAR"/>
            <result property="paramTypeName" column="param_type_name" jdbcType="VARCHAR"/>
            <result property="operateType" column="operate_type" jdbcType="VARCHAR"/>
            <result property="operateTypeName" column="operate_type_name" jdbcType="VARCHAR"/>
            <result property="memo" column="memo" jdbcType="VARCHAR"/>
            <result property="operateContent" column="operate_content" jdbcType="VARCHAR"/>
            <result property="recStat" column="rec_stat" jdbcType="CHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTimestamp" column="create_timestamp" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="updateTimestamp" column="update_timestamp" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,app_id,param_type,
        param_type_name,operate_type,operate_type_name,
        memo,operate_content,rec_stat,
        creator,create_timestamp,modifier,
        update_timestamp
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hw_busi_operate_log
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from hw_busi_operate_log
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.HwBusiOperateLogPO" useGeneratedKeys="true">
        insert into hw_busi_operate_log
        ( id,app_id,param_type
        ,param_type_name,operate_type,operate_type_name
        ,memo,operate_content,rec_stat
        ,creator,create_timestamp,modifier
        ,update_timestamp)
        values (#{id,jdbcType=BIGINT},#{appId,jdbcType=BIGINT},#{paramType,jdbcType=VARCHAR}
        ,#{paramTypeName,jdbcType=VARCHAR},#{operateType,jdbcType=VARCHAR},#{operateTypeName,jdbcType=VARCHAR}
        ,#{memo,jdbcType=VARCHAR},#{operateContent,jdbcType=VARCHAR},#{recStat,jdbcType=CHAR}
        ,#{creator,jdbcType=VARCHAR},#{createTimestamp,jdbcType=TIMESTAMP},#{modifier,jdbcType=VARCHAR}
        ,#{updateTimestamp,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.HwBusiOperateLogPO" useGeneratedKeys="true">
        insert into hw_busi_operate_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="appId != null">app_id,</if>
                <if test="paramType != null">param_type,</if>
                <if test="paramTypeName != null">param_type_name,</if>
                <if test="operateType != null">operate_type,</if>
                <if test="operateTypeName != null">operate_type_name,</if>
                <if test="memo != null">memo,</if>
                <if test="operateContent != null">operate_content,</if>
                <if test="recStat != null">rec_stat,</if>
                <if test="creator != null">creator,</if>
                <if test="createTimestamp != null">create_timestamp,</if>
                <if test="modifier != null">modifier,</if>
                <if test="updateTimestamp != null">update_timestamp,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="appId != null">#{appId,jdbcType=BIGINT},</if>
                <if test="paramType != null">#{paramType,jdbcType=VARCHAR},</if>
                <if test="paramTypeName != null">#{paramTypeName,jdbcType=VARCHAR},</if>
                <if test="operateType != null">#{operateType,jdbcType=VARCHAR},</if>
                <if test="operateTypeName != null">#{operateTypeName,jdbcType=VARCHAR},</if>
                <if test="memo != null">#{memo,jdbcType=VARCHAR},</if>
                <if test="operateContent != null">#{operateContent,jdbcType=VARCHAR},</if>
                <if test="recStat != null">#{recStat,jdbcType=CHAR},</if>
                <if test="creator != null">#{creator,jdbcType=VARCHAR},</if>
                <if test="createTimestamp != null">#{createTimestamp,jdbcType=TIMESTAMP},</if>
                <if test="modifier != null">#{modifier,jdbcType=VARCHAR},</if>
                <if test="updateTimestamp != null">#{updateTimestamp,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.dtms.manager.dao.po.HwBusiOperateLogPO">
        update hw_busi_operate_log
        <set>
                <if test="appId != null">
                    app_id = #{appId,jdbcType=BIGINT},
                </if>
                <if test="paramType != null">
                    param_type = #{paramType,jdbcType=VARCHAR},
                </if>
                <if test="paramTypeName != null">
                    param_type_name = #{paramTypeName,jdbcType=VARCHAR},
                </if>
                <if test="operateType != null">
                    operate_type = #{operateType,jdbcType=VARCHAR},
                </if>
                <if test="operateTypeName != null">
                    operate_type_name = #{operateTypeName,jdbcType=VARCHAR},
                </if>
                <if test="memo != null">
                    memo = #{memo,jdbcType=VARCHAR},
                </if>
                <if test="operateContent != null">
                    operate_content = #{operateContent,jdbcType=VARCHAR},
                </if>
                <if test="recStat != null">
                    rec_stat = #{recStat,jdbcType=CHAR},
                </if>
                <if test="creator != null">
                    creator = #{creator,jdbcType=VARCHAR},
                </if>
                <if test="createTimestamp != null">
                    create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
                </if>
                <if test="modifier != null">
                    modifier = #{modifier,jdbcType=VARCHAR},
                </if>
                <if test="updateTimestamp != null">
                    update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.howbuy.dtms.manager.dao.po.HwBusiOperateLogPO">
        update hw_busi_operate_log
        set 
            app_id =  #{appId,jdbcType=BIGINT},
            param_type =  #{paramType,jdbcType=VARCHAR},
            param_type_name =  #{paramTypeName,jdbcType=VARCHAR},
            operate_type =  #{operateType,jdbcType=VARCHAR},
            operate_type_name =  #{operateTypeName,jdbcType=VARCHAR},
            memo =  #{memo,jdbcType=VARCHAR},
            operate_content =  #{operateContent,jdbcType=VARCHAR},
            rec_stat =  #{recStat,jdbcType=CHAR},
            creator =  #{creator,jdbcType=VARCHAR},
            create_timestamp =  #{createTimestamp,jdbcType=TIMESTAMP},
            modifier =  #{modifier,jdbcType=VARCHAR},
            update_timestamp =  #{updateTimestamp,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
