package com.howbuy.dtms.manager.outservice.settle.dto;

import com.howbuy.dtms.settle.client.facade.trade.fundforcesubtract.FundForceSubtractRequest;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:23:42+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class FundForceSubtractDTOConvertMapperImpl implements FundForceSubtractDTOConvertMapper {

    @Override
    public FundForceSubtractRequest convert(FundForceSubtractDTO dto) {
        if ( dto == null ) {
            return null;
        }

        FundForceSubtractRequest fundForceSubtractRequest = new FundForceSubtractRequest();

        fundForceSubtractRequest.setAckVol( dto.getAckVol() );
        fundForceSubtractRequest.setForceSubtractRule( dto.getForceSubtractRule() );
        fundForceSubtractRequest.setFundCode( dto.getFundCode() );
        fundForceSubtractRequest.setFundTxAcctNo( dto.getFundTxAcctNo() );
        fundForceSubtractRequest.setHkCustNo( dto.getHkCustNo() );
        fundForceSubtractRequest.setNav( dto.getNav() );
        fundForceSubtractRequest.setNavDt( dto.getNavDt() );
        fundForceSubtractRequest.setSerialNumber( dto.getSerialNumber() );
        fundForceSubtractRequest.setShareRegDt( dto.getShareRegDt() );
        fundForceSubtractRequest.setTradeDt( dto.getTradeDt() );
        fundForceSubtractRequest.setVolDtlNo( dto.getVolDtlNo() );
        fundForceSubtractRequest.setCounterOrderNo( dto.getCounterOrderNo() );

        return fundForceSubtractRequest;
    }
}
