/*
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

/**
 * @description: 支付对账标记枚举
 * <AUTHOR>
 * @date 2025-07-04 16:20:10
 * @since JDK 1.8
 */
public enum PmtCompFlagEnum {

    /**
     * 无需对账
     */
    NO_NEED_CHECK("0", "无需对账"),
    
    /**
     * 未对账
     */
    UN_CHECK("1", "未对账"),
    
    /**
     * 对账完成
     */
    CHECK_COMPLETE("2", "对账完成"),
    
    /**
     * 对账不平
     */
    CHECK_MISMATCH("3", "对账不平");

    /**
     * 枚举code
     */
    private final String code;
    
    /**
     * 枚举的中文意义
     */
    private final String desc;

    PmtCompFlagEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     * @param code 枚举code
     * @return 枚举对象
     */
    public static PmtCompFlagEnum getEnumByCode(String code) {
        for (PmtCompFlagEnum pmtCompFlagEnum : PmtCompFlagEnum.values()) {
            if (pmtCompFlagEnum.getCode().equals(code)) {
                return pmtCompFlagEnum;
            }
        }
        return null;
    }

    /**
     * 根据code获取描述
     * @param code 枚举code
     * @return 描述
     */
    public static String getDescByCode(String code) {
        PmtCompFlagEnum pmtCompFlagEnum = getEnumByCode(code);
        return pmtCompFlagEnum != null ? pmtCompFlagEnum.getDesc() : null;
    }
}
