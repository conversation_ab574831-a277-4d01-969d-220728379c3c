<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.customize.HwSubPaidSumInfoCustomizeMapper">
    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwSubPaidSumInfoPO">
        <!--@Table hw_sub_paid_sum_info-->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="serial_no" jdbcType="BIGINT" property="serialNo" />
        <result column="hk_cust_no" jdbcType="VARCHAR" property="hkCustNo" />
        <result column="fund_tx_acct_no" jdbcType="VARCHAR" property="fundTxAcctNo" />
        <result column="fund_code" jdbcType="VARCHAR" property="fundCode" />
        <result column="sub_total_amt" jdbcType="DECIMAL" property="subTotalAmt" />
        <result column="cum_paid_total_amt" jdbcType="DECIMAL" property="cumPaidTotalAmt" />
        <result column="rec_stat" jdbcType="VARCHAR" property="recStat" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="create_timestamp" jdbcType="TIMESTAMP" property="createTimestamp" />
        <result column="update_timestamp" jdbcType="TIMESTAMP" property="updateTimestamp" />
    </resultMap>

    <sql id="Base_Column_List">
        id, serial_no, hk_cust_no, fund_tx_acct_no, fund_code, sub_total_amt, cum_paid_total_amt, 
        rec_stat, version, create_timestamp, update_timestamp
    </sql>

    <!-- 根据基金代码查询认缴实缴汇总信息列表（排除指定香港客户号） -->
    <select id="queryByFundCodeExcludeHkCustNo" parameterType="map" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
        from hw_sub_paid_sum_info
        where fund_code = #{fundCode,jdbcType=VARCHAR}
        and sub_total_amt - cum_paid_total_amt > 0
        <if test="excludeHkCustNoList != null and excludeHkCustNoList.size() > 0">
            and hk_cust_no not in
            <foreach collection="excludeHkCustNoList" item="hkCustNo" open="(" separator="," close=")"> #{hkCustNo,jdbcType=VARCHAR}
            </foreach>
        </if>
        and rec_stat = '1'
    </select>

    <!-- 查询认缴实缴总金额 -->
    <select id="queryTotalAmount" parameterType="com.howbuy.dtms.manager.dao.query.HwSubPaidSumInfoQuery" resultType="java.util.Map">
        SELECT 
            SUM(sub_total_amt) as totalPaidAmount,
            SUM(cum_paid_total_amt) as totalActualPaidAmount
        FROM hw_sub_paid_sum_info
        <where>
            <if test="hkCustNo != null and hkCustNo != ''">
                AND hk_cust_no = #{hkCustNo,jdbcType=VARCHAR}
            </if>
            <if test="fundCodeList != null and fundCodeList.size() > 0">
                and fund_code in
                <foreach collection="fundCodeList" item="fundCode" open="(" separator="," close=")">
                    #{fundCode,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="serialNo != null and serialNo != ''">
                AND serial_no = #{serialNo,jdbcType=VARCHAR}
            </if>
            <if test="remainingPaidGreaterThanZero != null and remainingPaidGreaterThanZero == true">
                AND (IFNULL(sub_total_amt, 0) - IFNULL(cum_paid_total_amt, 0)) > 0
            </if>
            <if test="remainingPaidGreaterThanZero != null and remainingPaidGreaterThanZero == false">
                AND (IFNULL(sub_total_amt, 0) - IFNULL(cum_paid_total_amt, 0)) &lt;= 0
            </if>
            and rec_stat = '1'
        </where>
    </select>

    <!-- 分页查询认缴实缴明细 -->
    <select id="queryPaidListByPage" parameterType="com.howbuy.dtms.manager.dao.query.HwSubPaidSumInfoQuery" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM hw_sub_paid_sum_info
        <where>
            <if test="hkCustNo != null and hkCustNo != ''">
                AND hk_cust_no = #{hkCustNo}
            </if>
            <if test="fundCodeList != null and fundCodeList.size() > 0">
                and fund_code in
                <foreach collection="fundCodeList" item="fundCode" open="(" separator="," close=")">
                    #{fundCode,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="serialNo != null and serialNo != ''">
                AND serial_no = #{serialNo,jdbcType=VARCHAR}
            </if>
            <if test="remainingPaidGreaterThanZero != null and remainingPaidGreaterThanZero == true">
                AND (IFNULL(sub_total_amt, 0) - IFNULL(cum_paid_total_amt, 0)) > 0
            </if>
            <if test="remainingPaidGreaterThanZero != null and remainingPaidGreaterThanZero == false">
                AND (IFNULL(sub_total_amt, 0) - IFNULL(cum_paid_total_amt, 0)) &lt;= 0
            </if>
            and rec_stat = '1'
        </where>
        ORDER BY fund_code asc,hk_cust_no asc
    </select>
</mapper> 