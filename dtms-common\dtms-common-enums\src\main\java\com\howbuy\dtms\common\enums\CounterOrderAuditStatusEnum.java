/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/17 13:44
 * @since JDK 1.8
 */
public enum CounterOrderAuditStatusEnum {

    NOT_NEED_EXAMINE("0", "无需审核"),
    WAIT_EXAMINE("1", "等待审核"),
    WAIT_REVIEW("2", "等待复核"),
    APPROVE("3", "审核通过"),
    FAILURE("4", "审核不通过"),
    REJECT_TO_FIRST_EXAMINE("5", "驳回至经办"),
    REJECT_TO_CUSTOMER("6", "驳回至客户"),
    VOIDED("7", "作废"),

    WAIT_REVISIT("8", "等待回访");

    private final String code;
    private final String desc;

     CounterOrderAuditStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(String code) {
        for (CounterOrderAuditStatusEnum item : CounterOrderAuditStatusEnum.values()) {
            if (item.getCode().equals(code)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * @description: 审核驳回且可以在提交的状态集合
     * @param
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/11/17 19:31
     * @since JDK 1.8
     */
    public static boolean canReSubmit(String code) {
        return REJECT_TO_FIRST_EXAMINE.getCode().equals(code);
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
