package com.howbuy.dtms.manager.counter.controller.payment;

import com.howbuy.dtms.manager.common.FileExportVO;
import com.howbuy.dtms.manager.common.Response;
import com.howbuy.dtms.manager.counter.request.paymentcheck.PaymentCheckRequest;
import com.howbuy.dtms.manager.counter.request.paymentcheck.QueryPaymentCheckListRequest;
import com.howbuy.dtms.manager.counter.request.paymentcheck.ResetTxPmtFlagRequest;
import com.howbuy.dtms.manager.counter.service.payment.TradePaymentCheckService;
import com.howbuy.dtms.manager.counter.vo.payment.PaymentCheckResultListVO;
import com.howbuy.dtms.manager.utils.BasicValidator;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * 交易支付对账控制器
 *
 * <AUTHOR>
 * @date 2025-07-23 15:11:25
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/counter/paymentcheck/trade/")
public class TradePaymentCheckController {

    @Resource
    private TradePaymentCheckService tradePaymentCheckService;

    /**
     * @api {POST} /counter/paymentcheck/trade/check check()
     * @apiVersion 1.0.0
     * @apiGroup PaymentCheckController
     * @apiName check()
     * @apiDescription 支付对账
     * @apiParam (请求体) {String} pmtCheckDt 支付对账日期
     * @apiParamExample 请求体示例
     * {"pmtCheckDt":"jl"}
     * @apiSuccess (响应结果) {String} code 状态码      状态码0000表示成功
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","description":"操作成功"}
     */
    @PostMapping("check")
    public Response<Void> check(@RequestBody PaymentCheckRequest request) {
        BasicValidator.validator(request);
        tradePaymentCheckService.check(request);
        return Response.ok();
    }

    /**
     * @api {POST} /counter/paymentcheck/trade/querylist queryList()
     * @apiVersion 1.0.0
     * @apiGroup PaymentCheckController
     * @apiName queryList()
     * @apiDescription 交易支付对账查询
     * @apiParam (请求体) {String} pmtDealNo 支付订单号
     * @apiParam (请求体) {String} dealNo 订单号
     * @apiParam (请求体) {String} outPmtDealNo 外部支付订单号
     * @apiParam (请求体) {Array} fundCodes 基金代码列表
     * @apiParam (请求体) {String} pmtCheckDt 支付对账日期
     * @apiParam (请求体) {Array} pmtCompFlags 支付对账标记列表
     * @apiParam (请求体) {Number} pageNum 页码
     * @apiParam (请求体) {Number} pageSize 每页大小
     * @apiParamExample 请求体示例
     * {"fundCodes":["TmgZ"],"pmtCheckDt":"n","outPmtDealNo":"xOOnIoxYw","pageSize":5996,"dealNo":"nQt8ti4zC","pageNum":4851,"pmtDealNo":"Xb","pmtCompFlags":["DAw4Oz"]}
     * @apiSuccess (响应结果) {String} code 状态码      状态码0000表示成功
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Number} data.total 总记录数
     * @apiSuccess (响应结果) {Number} data.pages 总页数
     * @apiSuccess (响应结果) {Array} data.list 结果
     * @apiSuccess (响应结果) {String} data.list.pmtDealNo 支付订单号
     * @apiSuccess (响应结果) {String} data.list.dealNo 订单号
     * @apiSuccess (响应结果) {String} data.list.middleBusiCode 中台业务代码
     * @apiSuccess (响应结果) {String} data.list.paymentTypeList 支付方式列表      111；第一位：电汇；第二位：支票；第三位：海外储蓄罐
     * @apiSuccess (响应结果) {String} data.list.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {String} data.list.custName 客户姓名
     * @apiSuccess (响应结果) {String} data.list.cpAcctNo 资金账号
     * @apiSuccess (响应结果) {String} data.list.fundTxAcctNo 基金交易账号
     * @apiSuccess (响应结果) {String} data.list.fundCode 基金代码
     * @apiSuccess (响应结果) {String} data.list.fundAddr 基金简称
     * @apiSuccess (响应结果) {String} data.list.currency 币种
     * @apiSuccess (响应结果) {String} data.list.appDtm 申请时间
     * @apiSuccess (响应结果) {String} data.list.pmtAmt 支付金额
     * @apiSuccess (响应结果) {String} data.list.pmtCheckDt 支付对账日期
     * @apiSuccess (响应结果) {String} data.list.outPmtDealNo 外部支付订单号
     * @apiSuccess (响应结果) {String} data.list.outPmtAmt 外部支付金额
     * @apiSuccess (响应结果) {String} data.list.outCurrency 外部币种
     * @apiSuccess (响应结果) {String} data.list.outPmtFlag 外部支付标识      1-待支付 2-支付成功 3-支付失败 4-支付中
     * @apiSuccess (响应结果) {String} data.list.txPmtFlag 交易支付标识      0-无需付款；1-未付款；2-付款成功；3-付款失败；4-付款中；5-已退款;6-等待付款；7-撤单成功；
     * @apiSuccess (响应结果) {String} data.list.pmtCompFlag 支付对账标记      0-无需对账；1-未对账；2-对账完成；3-对账不平;
     * @apiSuccess (响应结果) {String} data.list.memo 备注
     * @apiSuccessExample 响应结果示例
     * {"code":"3uthMU","data":{"total":1974,"pages":8083,"list":[{"middleBusiCode":"wfXe","pmtAmt":"Bb","pmtCheckDt":"RKtvWl","hkCustNo":"bt","outPmtFlag":"JOPoSov","memo":"rrE8","dealNo":"n36I","custName":"AUXvtJx4z7","pmtDealNo":"hfN","fundTxAcctNo":"lnACVouu","fundAddr":"MUu8QPYv","paymentTypeList":"31rJ0w4","fundCode":"nWGNtiZ","pmtCompFlag":"Lf","appDtm":"qCBCZ","outPmtDealNo":"67l7Ojnr","txPmtFlag":"kMR3p","cpAcctNo":"Nx2pcJ2Bej","currency":"3u0rWF","outCurrency":"CbHWK","outPmtAmt":"RKx8"}]},"description":"CP"}
     */
    @PostMapping("querylist")
    public Response<PaymentCheckResultListVO> queryList(@RequestBody QueryPaymentCheckListRequest request) {
        PaymentCheckResultListVO result = tradePaymentCheckService.queryList(request);
        return Response.ok(result);
    }

    /**
     * @api {POST} /counter/paymentcheck/trade/resettxpmtflag resetTxPmtFlag()
     * @apiVersion 1.0.0
     * @apiGroup PaymentCheckController
     * @apiName resetTxPmtFlag()
     * @apiDescription 重置交易支付标识
     * @apiParam (请求体) {String} pmtDealNo 支付订单号
     * @apiParamExample 请求体示例
     * {"pmtDealNo":"X"}
     * @apiSuccess (响应结果) {String} code 状态码      状态码0000表示成功
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","description":"操作成功"}
     */
    @PostMapping("resettxpmtflag")
    public Response<Void> resetTxPmtFlag(@RequestBody ResetTxPmtFlagRequest request) {
        BasicValidator.validator(request);
        tradePaymentCheckService.resetTxPmtFlag(request);
        return Response.ok();
    }

    /**
     * @api {POST} /counter/paymentcheck/trade/download 导出支付对账查询结果
     * @apiVersion 1.0.0
     * @apiGroup PaymentCheckController
     * @apiName download()
     * @apiDescription 导出交易支付对账查询结果
     * @apiParam (请求体) {String} pmtDealNo 支付订单号
     * @apiParam (请求体) {String} dealNo 订单号
     * @apiParam (请求体) {String} outPmtDealNo 外部支付订单号
     * @apiParam (请求体) {Array} fundCodes 基金代码列表
     * @apiParam (请求体) {String} pmtCheckDt 支付对账日期
     * @apiParam (请求体) {Array} pmtCompFlags 支付对账标记列表
     * @apiParamExample 请求体示例
     * {"fundCodes":["TmgZ"],"pmtCheckDt":"20250723","outPmtDealNo":"xOOnIoxYw","dealNo":"nQt8ti4zC","pmtDealNo":"Xb","pmtCompFlags":["1"]}
     * @apiSuccess (响应结果) {String} code 状态码      状态码0000表示成功
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.fileBytes 文件字节流
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","data":{"fileBytes":"UEsDBBQACAgIAA=="},"description":"操作成功"}
     */
    @PostMapping("download")
    public Response<FileExportVO> download(@RequestBody QueryPaymentCheckListRequest request) throws IOException {
        FileExportVO fileExportVO = tradePaymentCheckService.download(request);
        return Response.ok(fileExportVO);
    }
}
