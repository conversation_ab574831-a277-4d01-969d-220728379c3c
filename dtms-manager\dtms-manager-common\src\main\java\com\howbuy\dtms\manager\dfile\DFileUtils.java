package com.howbuy.dtms.manager.dfile;

import com.alibaba.fastjson.JSON;
import com.howbuy.dfile.HFileService;
import com.howbuy.dfile.HInputStream;
import com.howbuy.dfile.internal.config.StoreConfig;
import com.howbuy.dfile.internal.config.processor.StoreConfigProcessor;
import com.howbuy.dfile.internal.util.PathUtil;
import com.howbuy.dtms.manager.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Path;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * guangyao.wu 2024-11-18 10:21
 */
@Slf4j
public class DFileUtils {

    public static final String URL_SEPARATOR = "/";

    /**
     * 除最后一个元素，其余均会拼接【/】
     */
    public static String joinPath(String... paths) {
        StringBuilder sb = new StringBuilder();
        int limit = paths.length - 1;
        for (int i = 0; i < limit; i++) {
            String path = paths[i];
            if (StringUtils.isEmpty(path)) {
                continue;
            }
            sb.append(path);
            if (!path.endsWith(URL_SEPARATOR) && !path.endsWith(File.separator)) {
                sb.append(URL_SEPARATOR);
            }
        }
        sb.append(paths[limit]);
        return sb.toString();
    }

    /**
     * 基于 pa sdk封装：获取指定文件配置路径：storeServer+storeConfig
     */
    public static String getWebdavPath(String storeConfigKey) {
        StoreConfig storeConfig = StoreConfigProcessor.getInstance().getStoreConfig(storeConfigKey);
        return PathUtil.getWebDavRootPath(storeConfig, true);
    }

    public static Path getLocalPath(String storeConfigKey) {
        StoreConfig storeConfig = StoreConfigProcessor.getInstance().getStoreConfig(storeConfigKey);
        return PathUtil.getFileRootPath(storeConfig);
    }

    public static void mkDir(DFileInfoDTO fileInfoDTO) {
        try {
            HFileService.getInstance().mkdir(fileInfoDTO.getStoreConfigKey(), fileInfoDTO.getRelativePath());
        } catch (Exception e) {
            log.error("创建文件目录报错：{}", fileInfoDTO, e);
            throw new BusinessException("创建文件目录报错");
        }
    }

    /**
     * 基于 pa sdk封装：判断文件是否存在
     */
    public static boolean exist(DFileInfoDTO fileInfoDTO) {
        try {
            return HFileService.getInstance().exists(fileInfoDTO.getStoreConfigKey(), fileInfoDTO.getRelativePath(), fileInfoDTO.getFileName());
        } catch (Exception e) {
            log.error("判断文件是否存在报错：{}", fileInfoDTO, e);
            throw new BusinessException("判断文件是否存在报错");
        }
    }

    /**
     * 基于 pa sdk封装：删除文件
     */
    public static void deleteFile(DFileInfoDTO fileInfoDto) {
        try {
            HFileService.getInstance().deleteFile(fileInfoDto.getStoreConfigKey(), fileInfoDto.getRelativePath(), fileInfoDto.getFileName());
        } catch (Exception e) {
            log.error("删除文件报错：{}", fileInfoDto, e);
            throw new BusinessException("删除文件报错");
        }
    }

    public static List<DFileInfoDTO> list(DFileInfoDTO dir) throws IOException {
        String storeConfigKey = dir.getStoreConfigKey();
        String relativePath = dir.getRelativePath();
        return Stream.of(HFileService.getInstance().list(storeConfigKey, relativePath))
                .map(fileName -> new DFileInfoDTO().setStoreConfigKey(storeConfigKey).setRelativePath(relativePath)
                        .setFileName(fileName))
                .collect(Collectors.toList());
    }

    public static void batchDelete(DFileInfoDTO... dtoList) {
        if (ArrayUtils.isEmpty(dtoList)) {
            return;
        }
        Stream.of(dtoList).forEach(DFileUtils::deleteFile);
    }

    /**
     * 基于 pa sdk封装：写文件
     * note: webdav 暂不支持流的方式写文件
     */
    public static void write(DFileInfoDTO fileInfoDTO, String content) throws IOException {
        String storeConfigKey = fileInfoDTO.getStoreConfigKey();
        log.info("写入dfile文件信息：{}", JSON.toJSONString(fileInfoDTO));
        HFileService.getInstance().write(storeConfigKey, fileInfoDTO.getRelativePath(), fileInfoDTO.getFileName(), content);
    }

    public static void write(DFileInfoDTO fileInfoDTO, byte[] content) throws IOException {
        String storeConfigKey = fileInfoDTO.getStoreConfigKey();
        log.info("写入dfile文件信息：{}", JSON.toJSONString(fileInfoDTO));
        HFileService.getInstance().write(storeConfigKey, fileInfoDTO.getRelativePath(), fileInfoDTO.getFileName(), content);
    }

    /**
     * 基于 pa sdk封装：读文件
     * note: webdav 暂不支持流的方式读文件
     */
    public static byte[] read2Bytes(DFileInfoDTO fileInfoDTO) throws IOException {
        log.info("读取dfile文件信息：{}", JSON.toJSONString(fileInfoDTO));
        return HFileService.getInstance().read2Bytes(fileInfoDTO.getStoreConfigKey(), fileInfoDTO.getRelativePath(), fileInfoDTO.getFileName());
    }

    /**
     * 基于 pa sdk封装：读文件
     * note: webdav 暂不支持流的方式读文件
     */
    public static String read2String(DFileInfoDTO fileInfoDTO) throws IOException {
        log.info("读取dfile文件信息：{}", JSON.toJSONString(fileInfoDTO));
        return HFileService.getInstance().read2String(fileInfoDTO.getStoreConfigKey(), fileInfoDTO.getRelativePath(), fileInfoDTO.getFileName());
    }

    public static HInputStream newInputStream(DFileInfoDTO fileInfoDto) throws IOException {
        return new HInputStream(fileInfoDto.getStoreConfigKey(), fileInfoDto.getRelativePath(), fileInfoDto.getFileName());
    }

    public static void read(DFileInfoDTO fileInfoDTO, OutputStream outputStream) throws IOException {
        try (
                HInputStream his = newInputStream(fileInfoDTO);
                InputStream is = his.getInputStream();
                OutputStream os = outputStream
        ) {
            IOUtils.copy(is, os);
        }
    }

}
