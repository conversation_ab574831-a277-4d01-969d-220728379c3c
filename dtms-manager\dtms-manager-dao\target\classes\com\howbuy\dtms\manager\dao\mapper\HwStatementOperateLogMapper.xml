<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.HwStatementOperateLogMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwStatementOperateLogPO">
        <!--@Table hw_statement_operate_log-->
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="operatorNo" column="operator_no" jdbcType="VARCHAR"/>
        <result property="operateDate" column="operate_date" jdbcType="TIMESTAMP"/>
        <result property="operateContent" column="operate_content" jdbcType="VARCHAR"/>
        <result property="busiSerialNo" column="busi_serial_no" jdbcType="VARCHAR"/>
        <result property="busiType" column="busi_type" jdbcType="VARCHAR"/>
        <result property="memo" column="memo" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
        operator_no, 
        operate_date, 
        operate_content, 
        busi_serial_no, 
        busi_type, 
        memo
    </sql>

    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_statement_operate_log
        where id = #{id}
    </select>

    <!-- 查询符合条件的数据 -->
    <select id="selectBySelective" parameterType="com.howbuy.dtms.manager.dao.po.HwStatementOperateLogPO"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_statement_operate_log
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="operatorNo != null and operatorNo != ''">
                and operator_no = #{operatorNo}
            </if>
            <if test="operateDate != null">
                and operate_date = #{operateDate}
            </if>
            <if test="operateContent != null and operateContent != ''">
                and operate_content = #{operateContent}
            </if>
            <if test="busiSerialNo != null and busiSerialNo != ''">
                and busi_serial_no = #{busiSerialNo}
            </if>
            <if test="busiType != null and busiType != ''">
                and busi_type = #{busiType}
            </if>
            <if test="memo != null and memo != ''">
                and memo = #{memo}
            </if>
        </where>
        order by id
    </select>


    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(*)
        from hw_statement_operate_log
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="operatorNo != null and operatorNo != ''">
                and operator_no = #{operatorNo}
            </if>
            <if test="operateDate != null">
                and operate_date = #{operateDate}
            </if>
            <if test="operateContent != null and operateContent != ''">
                and operate_content = #{operateContent}
            </if>
            <if test="busiSerialNo != null and busiSerialNo != ''">
                and busi_serial_no = #{busiSerialNo}
            </if>
            <if test="busiType != null and busiType != ''">
                and busi_type = #{busiType}
            </if>
            <if test="memo != null and memo != ''">
                and memo = #{memo}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into hw_statement_operate_log(operator_no, operate_date, operate_content, busi_serial_no, busi_type,
                                             memo)
        values (#{operatorNo}, #{operateDate}, #{operateContent}, #{busiSerialNo}, #{busiType}, #{memo})
    </insert>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true">
        insert into hw_statement_operate_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="operatorNo != null and operatorNo != ''">
                operator_no,
            </if>
            <if test="operateDate != null">
                operate_date,
            </if>
            <if test="operateContent != null and operateContent != ''">
                operate_content,
            </if>
            <if test="busiSerialNo != null and busiSerialNo != ''">
                busi_serial_no,
            </if>
            <if test="busiType != null and busiType != ''">
                busi_type,
            </if>
            <if test="memo != null and memo != ''">
                memo,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="operatorNo != null and operatorNo != ''">
                #{operatorNo},
            </if>
            <if test="operateDate != null">
                #{operateDate},
            </if>
            <if test="operateContent != null and operateContent != ''">
                #{operateContent},
            </if>
            <if test="busiSerialNo != null and busiSerialNo != ''">
                #{busiSerialNo},
            </if>
            <if test="busiType != null and busiType != ''">
                #{busiType},
            </if>
            <if test="memo != null and memo != ''">
                #{memo},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into hw_statement_operate_log(operator_no, operate_date, operate_content, busi_serial_no, busi_type,
        memo)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.operatorNo}, #{entity.operateDate}, #{entity.operateContent}, #{entity.busiSerialNo},
            #{entity.busiType}, #{entity.memo})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update hw_statement_operate_log
        <set>
            <if test="operatorNo != null and operatorNo != ''">
                operator_no = #{operatorNo},
            </if>
            <if test="operateDate != null">
                operate_date = #{operateDate},
            </if>
            <if test="operateContent != null and operateContent != ''">
                operate_content = #{operateContent},
            </if>
            <if test="busiSerialNo != null and busiSerialNo != ''">
                busi_serial_no = #{busiSerialNo},
            </if>
            <if test="busiType != null and busiType != ''">
                busi_type = #{busiType},
            </if>
            <if test="memo != null and memo != ''">
                memo = #{memo},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from hw_statement_operate_log
        where id = #{id}
    </delete>

    <!--分页查询，根据主键ID升序排序-->
    <select id="selectWithPage" parameterType="com.howbuy.dtms.manager.dao.query.HwStatementOperateLogQuery"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_statement_operate_log
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="operatorNo != null and operatorNo != ''">
                and operator_no = #{operatorNo}
            </if>
            <if test="operateDate != null">
                and operate_date = #{operateDate}
            </if>
            <if test="operateContent != null and operateContent != ''">
                and operate_content = #{operateContent}
            </if>
            <if test="busiSerialNo != null and busiSerialNo != ''">
                and busi_serial_no = #{busiSerialNo}
            </if>
            <if test="busiType != null and busiType != ''">
                and busi_type = #{busiType}
            </if>
            <if test="memo != null and memo != ''">
                and memo = #{memo}
            </if>
            <if test="idList != null and idList.size > 0">
                and id in
                <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by id
    </select>
</mapper>

