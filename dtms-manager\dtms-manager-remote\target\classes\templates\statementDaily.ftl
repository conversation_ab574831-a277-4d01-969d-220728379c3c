<#import "fun.ftl" as fun />
<!DOCTYPE html>
<html lang="zh-CN" style="background-color: #ffffff">
<head>
    <meta charset="utf-8"/>
    <meta name="google" content="notranslate"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta http-equiv="Cache-Control" content="no-cache">
    <meta http-equiv="Cache" content="no-cache">
    <meta http-equiv="x-dns-prefetch-control" content="on">
    <!-- <link rel="icon" href="./favicon.ico" /> -->
    <link rel="stylesheet" href="/css/reset.css"/>
    <link rel="stylesheet" href="/css/main.css"/>
    <script src="/js/paged.polyfill.min.js"></script>
    <style>
        .page-header {
            position: running(header); /* 将 header 设置为可运行的元素 */
            width: 100%; /* 宽度 */
        }
        .page-footer {
            position: running(footer); /* 将 header 设置为可运行的元素 */
            width: 100%; /* 宽度 */
        }
        @page {
            size: A4 landscape;
            margin-top: 6cm;
            margin-right: 0.8cm;
            margin-bottom: 2.8cm;
            margin-left: 0.8cm;
            @top-center {
                content: element(header); /* 使用 header 元素的内容 */
            }
            @bottom-left {
                content: element(footer); /* 使用 footer 元素的内容 */
            }
            @bottom-right {
                content: counter(page) '/' counter(pages); /* 页码 */
            }
        }
    </style>
    <script>
        // document.addEventListener('DOMContentLoaded', function() {
        //
        // });
        window.onload = function () {
            new PagedPolyfill();
            document.querySelector('body').classList.add('page_loding_finish');
        };
    </script>
</head>

<body>
<!-- 页头 -->
<header class="page-header">
    <div class="app-header">
        <img src="/images/logo2.png" alt="logo"/>
    </div>
    <!-- 头 -->
    <div class="app-body__title1">
        <h3>综合日结单</h3>
        <p>Consolidated Daily Statement</p>
    </div>
    <div class="app-body__item1">${dataDTO.custDTO.custChineseName!}<br>${dataDTO.custDTO.custEnName!}</div>
    <div class="app-body__item2">
        <div class="app-body__item2-left">
            <div>${dataDTO.custDTO.residenceAddr!}</div>
            <div>${dataDTO.custDTO.mailingAddr!}</div>
        </div>
        <div class="app-body__item2-right">
            <div>
                <label>账户号码 Account Number: </label>
                <span class="p-l-5">${dataDTO.custDTO.hkCustNo!}-${dataDTO.custDTO.fundTxAcctNo!}</span>
            </div>
            <div>
                <label>结单日期 Statement Date: </label>
                <span class="p-l-5">${dataDTO.dataDt!}</span>
            </div>
        </div>
    </div>
</header>
<!-- 页脚 -->
<footer class="page-footer">
    <div class="app-footer">
        <div>
            <span>好买香港有限公司 Howbuy Hong Kong Limited (证监会中央编号：BJR948)/ 好买香港代理人有限公司 Howbuy Hong Kong Nominee Limited</span>
        </div>
        <div>
            <span>香港九龙尖沙咀广东道9号海港城港威大厦6座29楼2907-08室 Suite 2907-08, 29/F, Tower 6, The Gateway, Harbour City, 9 Canton Road, Tsim Sha Tsui, Kowloon, Hong Kong</span>
        </div>
        <div>
            <p>
                <label>Tel 联络电话：+852 3725 8088      Email 电邮：<EMAIL></label>
            </p>
        </div>
    </div>
</footer>
<!-- 内容 -->
<div class="app-body">
    <!-- 户口概览 -->
        <div class="app-body__title3 m-t-30">
            <h3>户口概览</h3>
            <p>Account Summary</p>
        </div>
        <div class="app-body__title4 m-t-10">
            <h3>现金账户</h3>
            <p>Cash Account</p>
        </div>
        <table
                class="app-body__table"
                border="1"
                cellspacing="0"
                cellpadding="0"
        >
            <thead>
            <tr>
                <th class="text-left"><p>货币</p>
                    <p>Currency</p></th>
                <th class="text-right"><p>现金余额</p>
                    <p>Cash Balance</p></th>
                <th class="text-right"><p>冻结金额</p>
                    <p>Frozen Amount</p></th>
                <th class="text-right"><p>账户结余</p>
                    <p>Cash Available</p></th>
                <th class="text-right"><p>美元汇率</p>
                    <p>Exchange Rate</p></th>
                <th class="text-right"><p>美元等值</p>
                    <p>USD Equivalent</p></th>
            </tr>
            </thead>
            <#if (dataDTO.cashAccountMap??) && (dataDTO.cashAccountMap?size > 0)>
            <tbody>
                <#list dataDTO.cashAccountMap?keys as curEn>
                    <#assign cashAccount = dataDTO.cashAccountMap[curEn]>
                <tr>
                    <td class="text-left">${curEn}</td>
                    <td class="text-right">${fun.formatBalance(cashAccount.totalBalance!0)}</td>
                    <td class="text-right">${fun.formatBalance(cashAccount.freezeBalance!0)}</td>
                    <td class="text-right">${fun.formatBalance(cashAccount.accountBalance!0)}</td>
                    <td class="text-right">${(cashAccount.dollarRate!0)?string("#,##0.0000")}</td>
                    <td class="text-right">${fun.formatBalance(cashAccount.dollarBalance!0)}</td>
                </tr>
                </#list>
            </tbody>
            <tfoot>
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td class="text-right">小计 Subtotal</td>
                <td class="text-right">${fun.formatBalance(dataDTO.sumCashDollarBalance!0)}</td>
            </tr>
            </tfoot>
            </#if>
        </table>
        <div class="app-body__title4 m-t-10">
            <h3>投资组合</h3>
            <p>Investment Portfolio </p>
        </div>
        <table
                class="app-body__table"
                border="1"
                cellspacing="0"
                cellpadding="0"
        >
            <thead>
            <tr>
                <th class="text-left"><p>货币</p>
                    <p>Currency</p></th>
                <th class="text-right"><p>持有资产</p>
                    <p>Holding Asset</p></th>
                <th class="text-right"><p>在途资产</p>
                    <p>Pending Asset</p></th>
                <th class="text-right"><p>总资产</p>
                    <p>Total Asset</p></th>
                <th class="text-right"><p>美元汇率</p>
                    <p>Exchange Rate</p></th>
                <th class="text-right"><p>美元等值</p>
                    <p>USD Equivalent</p></th>
            </tr>
            </thead>
            <#if (dataDTO.investAccountMap??) && (dataDTO.investAccountMap?size > 0)>
            <tbody>
                <#list dataDTO.investAccountMap?keys as curEn>
                    <#assign investAccount = dataDTO.investAccountMap[curEn]>
                <tr>
                    <td class="text-left">${curEn}</td>
                    <td class="text-right">${fun.formatBalance(investAccount.holdingAsset!0)}</td>
                    <td class="text-right">${fun.formatBalance(investAccount.pendingAsset!0)}</td>
                    <td class="text-right">${fun.formatBalance(investAccount.totalAsset!0)}</td>
                    <td class="text-right">${(investAccount.dollarRate!0)?string("#,##0.0000")}</td>
                    <td class="text-right">${fun.formatBalance(investAccount.dollarBalance!0)}</td>
                </tr>
                </#list>
            </tbody>
            <tfoot>
            <tr>
                <td class="text-left"></td>
                <td></td>
                <td></td>
                <td></td>
                <td class="text-right">小计 Subtotal</td>
                <td class="text-right">${fun.formatBalance(dataDTO.sumInvestDollarBalance!0)}</td>
            </tr>
            <tr style="height: 20px;"></tr>
            <tr>
                <td class="text-left"></td>
                <td></td>
                <td></td>
                <td></td>
                <td class="text-right">总计 Grand Total</td>
                <td class="text-right">${fun.formatBalance(dataDTO.sumDollarBalance!0)}</td>
            </tr>
            </tfoot>
            </#if>
        </table>

        <p class="app-body__explain m-t-20">*以最新公布的净值估算，与最终成交可能存在差异。Based on the latest published net
            asset value estimate, there may be differences from the final transaction.</p>

    <!-- 现金账户 -->
    <#if (dataDTO.cashAccountDetailMap??) && (dataDTO.cashAccountDetailMap?size > 0)>
        <div class="page_break"></div>
        <div class="app-body__title3 m-t-30">
            <h3>现金账户</h3>
            <p>Cash Account</p>
        </div>
        <!-- 美元账户 -->
        <#list dataDTO.cashAccountDetailMap?keys as curEn>
            <#assign cashDetailList = dataDTO.cashAccountDetailMap[curEn]>
        <div class="app-body__title4 m-t-30">
            <h3>${fun.dicMapFn(fun.currencyMap, curEn)}账户</h3>
            <p>${curEn} Account</p>
        </div>
        <table
                class="app-body__table"
                border="1"
                cellspacing="0"
                cellpadding="0"
        >
            <thead>
            <tr>
                <th class="text-left"><p>交易日期</p>
                    <p>Tran Date</p></th>
                <th colspan="2"><p>摘要</p>
                    <p>Description</p></th>
                <th class="text-right"><p>存入</p>
                    <p>Credit</p></th>
                <th class="text-right"><p>支出</p>
                    <p>Debit</p></th>
                <th class="text-right"><p>结余</p>
                    <p>Balance</p></th>
            </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="text-left">期初余额B/F BALANCE</td>
                    <td colspan="2"></td>
                    <td class="text-right"></td>
                    <td class="text-right"></td>
                    <td class="text-right">${fun.formatBalance(dataDTO.cashAccountBalanceMap[curEn].startBalance!0)}</td>
                </tr>
                <#list cashDetailList as cashDetail>
                <tr>
                    <td class="text-left">${cashDetail.dataDt!}</td>
                    <td class="text-left-w-500" colspan="2">${cashDetail.remark!}</td>
                    <td class="text-right">${fun.formatBalance(cashDetail.inBalance!0)}</td>
                    <td class="text-right">${fun.formatBalance(cashDetail.outBalance!0)}</td>
                    <td class="text-right">${fun.formatBalance(cashDetail.balance!0)}</td>
                </tr>
                </#list>
            </tbody>
            <tfoot>
            <tr>
                <td class="text-left">期末余额C/F BALANCE</td>
                <td colspan="2"></td>
                <td></td>
                <td></td>
                <td class="text-right">${fun.formatBalance(dataDTO.cashAccountBalanceMap[curEn].endBalance!0)}</td>
            </tr>
            </tfoot>
        </table>
        </#list>

    </#if>

    <!-- 投资组合 -->
    <#if (dataDTO.assetDetailMap??) && (dataDTO.assetDetailMap?size > 0)>
        <div class="page_break"></div>
        <div class="app-body__title3 m-t-30">
            <h3>投资组合</h3>
            <p>Investment Portfolio </p>
        </div>
        <#list dataDTO.assetDetailMap?keys as curEn>
            <#assign assetDetailList = dataDTO.assetDetailMap[curEn]>
        <div class="app-body__title4 m-t-30">
            <h3>${fun.dicMapFn(fun.currencyMap, curEn)}账户</h3>
            <p>${curEn} Account</p>
        </div>
        <table
                class="app-body__table"
                border="1"
                cellspacing="0"
                cellpadding="0"
        >
            <thead>
            <tr>
                <th class="text-left"><p>产品代码</p>
                    <p>Product</p>
                    <p>Code</p></th>
                <th><p>名称</p>
                    <p>Name</p></th>
                <th><p>份额</p>
                    <p>Shares</p></th>
                <th><p>净值日期</p>
                    <p>NAV Date</p></th>
                <th><p>净值</p>
                    <p>NAV</p></th>
                <th><p>平衡因子</p>
                    <p>Equalisation</p>
                    <p>Credit</p></th>
                <th><p>累计应收费用</p>
                    <p>Accumulated</p>
                    <p>Accrued Fee</p></th>
                <th><p>费后市值</p>
                    <p>Net Market</p>
                    <p>Value</p></th>
                <th><p>持仓收益</p>
                    <p>Unrealized</p>
                    <p>Profit&Loss</p></th>
                <th><p>持仓收益率%</p>
                    <p>Unrealized</p>
                    <p>Profit&Loss %</p></th>
            </tr>
            </thead>
            <tbody>
                <#list assetDetailList as assetDetail>
                <tr>
                    <td class="text-left">${assetDetail.fundCode!}</td>
                    <td class="text-left-w-220">${assetDetail.fundAbbr!}</td>
                    <td>${(assetDetail.balanceVol!0)?string("#,##0.000000")}</td>
                    <td>${assetDetail.navDt!}</td>
                    <td>${(assetDetail.nav!0)?string("#,##0.0000####")}</td>
                    <td>${fun.formatBalance(assetDetail.balanceFactor!0)}</td>
                    <td>${fun.formatBalance(assetDetail.naFee!0)}</td>
                    <td>${fun.formatBalance(assetDetail.balanceAmtExFee!0)}</td>
                    <td>${(assetDetail.balanceIncomeExFee!0)?string("#,##0.00")}</td>
                    <#if (assetDetail.balanceIncomeRateExFee??) && (assetDetail.balanceIncomeRateExFee != 0)>
                        <td>${fun.formatBalance(assetDetail.balanceIncomeRateExFee)}</td>
                    <#else>
                        <td>0.00%</td>
                    </#if>
                </tr>
                </#list>
            </tbody>
            <tfoot>
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td>总计 Grand Total</td>
                <td>${fun.formatBalance(dataDTO.sumBalanceAmtExFeeMap[curEn])}</td>
                <td>${curEn}</td>
                <td></td>
            </tr>
            </tfoot>
        </table>
        </#list>
    </#if>
    <!-- 在途资产 -->
    <#if (dataDTO.pendingOrderDtlListMap??) && (dataDTO.pendingOrderDtlListMap?size > 0)>
        <div class="page_break"></div>
        <div class="app-body__title3 m-t-30">
            <h3>在途资产</h3>
            <p>Pending Asset </p>
        </div>
        <#list dataDTO.pendingOrderDtlListMap?keys as curEn>
            <#assign pendingOrderDtlList = dataDTO.pendingOrderDtlListMap[curEn]>
            <div class="app-body__title4 m-t-30">
                <h3>${fun.dicMapFn(fun.currencyMap, curEn)}账户</h3>
                <p>${curEn} Account</p>
            </div>
            <table
                    class="app-body__table"
                    border="1"
                    cellspacing="0"
                    cellpadding="0"
            >
                <thead>
                <tr>
                    <th class="text-left"><p>交易类型</p>
                        <p>Type</p></th>
                    <th><p>产品代码</p>
                        <p>Product Code</p></th>
                    <th><p>名称</p>
                        <p>Name</p></th>
                    <th><p>下单日期</p>
                        <p>Order Date</p></th>
                    <th><p>下单金额</p>
                        <p>Order Amount</p></th>
                    <th><p>下单份额</p>
                        <p>Order Shares</p></th>
                    <th><p>预计交易日期</p>
                        <p>Estimated Trade Date</p></th>
                    <th><p>在途资产</p>
                        <p>Pending Asset</p></th>
                </tr>
                </thead>
                <tbody>
                <#list pendingOrderDtlList as orderDtl>
                    <tr>
                        <td class="text-left">${orderDtl.busiType!}</td>
                        <td>${orderDtl.fundCode!}</td>
                        <td class="text-left-w-220">${orderDtl.fundAbbr!}</td>
                        <td>${orderDtl.appDt!}</td>
                        <td>${fun.formatBalance(orderDtl.appAmt!0)}</td>
                        <td>${(orderDtl.appVol!0)?string("#,##0.000000")}</td>
                        <td>${orderDtl.taTradeDt!}</td>
                        <td>${fun.formatBalance(orderDtl.pendingAsset!0)}</td>
                    </tr>
                </#list>
                </tbody>
                <tfoot>
                <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td>总计 Subtotal</td>
                    <td>${fun.formatBalance(dataDTO.sumPendingOrderDtlListMap[curEn])}</td>
                </tr>
                </tfoot>
            </table>
        </#list>
        <p class="app-body__explain m-t-20">*认购的交易日期为预估交易日期，实际交易日期请以最终确认情况为准Please note that the trade date of subscription transaction is estimated. The  actual trade date is subject to the settlement information</p>

    </#if>
    <!-- 交易 -->
    <#if (dataDTO.ackOrderDtlList??) && (dataDTO.ackOrderDtlList?size > 0)>
        <div class="page_break"></div>
        <div class="app-body__title4 m-t-10">
            <h3>交易</h3>
            <p>Transactions</p>
        </div>
        <table
                class="app-body__table"
                border="1"
                cellspacing="0"
                cellpadding="0"
        >
            <thead>
            <tr>
                <th class="text-left"><p>交易类型</p>
                    <p>Type</p></th>
                <th><p>货币</p>
                    <p>Currency</p></th>
                <th><p>产品代码</p>
                    <p>Product Code</p></th>
                <th><p>名称</p>
                    <p>Name</p></th>
                <th><p>下单日期</p>
                    <p>Order Date</p></th>
                <th><p>下单金额</p>
                    <p>Order Amount</p></th>
                <th><p>下单份额</p>
                    <p>Order Shares</p></th>
                <th><p>交易日期</p>
                    <p>Trade Date</p></th>
                <th><p>确认份额</p>
                    <p>Settled Shares</p></th>
                <th><p>确认净值</p>
                    <p>Settled NAV</p></th>
                <th><p>费用</p>
                    <p>Charges</p></th>
                <th><p>确认金额</p>
                    <p>Settled Amount</p></th>
            </tr>
            </thead>
            <tbody>
            <#list dataDTO.ackOrderDtlList as ackOrderDtl>
            <tr>
                <td class="text-left">${ackOrderDtl.busiType!}</td>
                <td>${ackOrderDtl.currency!}</td>
                <td>${ackOrderDtl.fundCode!}</td>
                <td class="text-left-w-220">${ackOrderDtl.fundAbbr!}</td>
                <td>${ackOrderDtl.appDt!}</td>
                <td>${fun.formatBalance(ackOrderDtl.appAmt!0)}</td>
                <td>${(ackOrderDtl.appVol!0)?string("#,##0.000000")}</td>
                <td>${ackOrderDtl.taTradeDt!}</td>
                <td>${(ackOrderDtl.ackVol!0)?string("#,##0.000000")}</td>
                <td>${(ackOrderDtl.ackNav!0)?string("#,##0.0000####")}</td>
                <td>${fun.formatBalance(ackOrderDtl.fee!0)}</td>
                <td>${fun.formatBalance(ackOrderDtl.ackAmt!0)}</td>
            </tr>
            </#list>
            </tbody>
        </table>
    </#if>

    <!-- 待确认交易 -->
    <#if (dataDTO.unAckOrderDtlList??) && (dataDTO.unAckOrderDtlList?size > 0)>
        <div class="page_break"></div>
        <div class="app-body__title4 m-t-10">
            <h3>待确认交易</h3>
            <p>Pending Transactions</p>
        </div>
        <table
                class="app-body__table"
                border="1"
                cellspacing="0"
                cellpadding="0"
        >
            <thead>
            <tr>
                <th class="text-left"><p>交易类型</p>
                    <p>Type</p></th>
                <th><p>货币</p>
                    <p>Currency</p></th>
                <th><p>产品代码</p>
                    <p>Product Code</p></th>
                <th><p>名称</p>
                    <p>Name</p></th>
                <th><p>下单日期</p>
                    <p>Order Date</p></th>
                <th><p>下单金额</p>
                    <p>Order Amount</p></th>
                <th><p>下单份额</p>
                    <p>Order Shares</p></th>
                <th><p>预计交易日期</p>
                    <p>Estimated Trade Date</p></th>
                <th><p>付款状态</p>
                    <p>Payment Status</p></th>
            </tr>
            </thead>
            <tbody>
            <#list dataDTO.unAckOrderDtlList as unAckOrderDtl>
            <tr>
                <td class="text-left">${unAckOrderDtl.busiType!}</td>
                <td>${unAckOrderDtl.currency!}</td>
                <td>${unAckOrderDtl.fundCode!}</td>
                <td class="text-left-w-220">${unAckOrderDtl.fundAbbr!}</td>
                <td>${unAckOrderDtl.appDt!}</td>
                <td>${fun.formatBalance(unAckOrderDtl.appAmt!0)}</td>
                <td>${(unAckOrderDtl.appVol!0)?string("#,##0.000000")}</td>
                <td>${unAckOrderDtl.openDt!}</td>
                <td>${fun.dicMapFn(fun.payStatusMap, unAckOrderDtl.payStatus)}</td>
            </tr>
            </#list>
            </tbody>
        </table>
    </#if>

    <!-- 文字内容 -->
    <div class="page_break"></div>
    <div class="app-body__title2">
        <h3>重要提示</h3>
        <p>IMPORTANT NOTICE</p>
    </div>
    <div class="app-body__content3">
        <p>本综合日结单显示了好买香港代理人公司和好买香港有限公司为客户持有的所有资产。</p>
        <p>The Consolidated Monthly Statement reflects all assets held by Howbuy Hong Kong Nominee Limited and Howbuy Hong Kong Limited for the Client.</p>
    </div>
    <div class="app-body__content3">
        <p>客户应明白投资涉及风险，投资产品价格均可升可跌。在作出任何投资决定前，客户应细阅及了解有关投资产品的销售文件，以及其中所载的风险披露声明及风险警示。</p>
        <p>Clients should note that investments involve risks and price of the investment products may move up or down. Clients should read and fully understand the offering documents relating to the investment products and all the risk disclosure statements and risk warnings therein before making any investment decisions.</p>
    </div>
    <div class="app-body__content3">
        <p>如果您认为此结单存在任何错误、误差或遗漏，请立即通过客服热线电话+852 3725 8088或邮箱******************** 联系我们。除非我们在此结单发出之日起7天内收到来自您的异议，否则我们将假设您同意我们的记录。</p>
        <p>If you believe there are any errors, discrepancies or omissions in this Statement, kindly contact us immediately at telephone no. +852 3725 8088 <NAME_EMAIL>. Unless we hear the contrary from you within 7 days from the date of issue, we would assume that you agree with our records.</p>
    </div>
    <div class="app-body__content3">
        <p>用于决定投资组合价值的价格是从各种被相信为可靠的来源获得的，但是并不能保证它们的准确性。由于来源方面报告的延迟，投资产品可能会基于前一个净值日的价格进行估值。</p>
        <p>Prices used to determine portfolio valuations are obtained from various sources believed to be reliable, but the accuracy cannot be guaranteed. Investment products may be valued based on the price of last valuation date due to delayed reporting from source.</p>
    </div>
    <div class="app-body__content3">
        <p>按照《证券及期货(成交单据、户口结单及收据)规则》及其他不时适用的法律或规则规定，此账户结单将作为客户款项资产的正式收据。</p>
        <p>This statement serves as an official receipt of client asset in accordance with Securities and Futures (Contract Notes, Statements of Account and Receipts) Rules and any applicable law(s) or regulations from time to time.</p>
    </div>
    <div class="app-body__content3">
        <p>上述证券（即将）由我司按照《证券及期货条例》予以保管。</p>
        <p>The above mentioned securities are being held (or will be held) by us for custody purpose in accordance with the Securities and Futures Ordinance.</p>
    </div>
    <div class="app-body__content3">
        <p>阁下的联系方式如有任何变更，请实时以书面方式通知本公司。</p>
        <p>Please notify us in writing immediately if your contact information has been changed.</p>
    </div>
    <div class="app-body__content4">
        <p>E.& O.E</p>
        <p>如有错漏 祈为指正</p>
    </div>
</div>
</body>
</html>