/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

/**
 * <AUTHOR>
 * @description: 业务类型
 * @date 2024/4/15 16:49
 * @since JDK 1.8
 */
public enum BusinessTypeEnum {

    BUY("10", "买入"),

    SELL("11", "卖出"),

    BATCH_PAID("12", "批量实缴"),


    SUB_AND_FIRST_PAID("13", "认缴和首次实缴"),

    FULL_BATCH_SUBSCRIBE("14", "全委专户批量认申购"),

    FULL_BATCH_REDEEM("15", "全委专户批量赎回"),

    NO_TRADE_OVER_ACCOUNT_IN("16", "非交易过户转入"),

    NO_TRADE_OVER_ACCOUNT_OUT("17", "非交易过户转出"),

    DIV_MODE_CHANGE("18", "修改分红方式"),

    FORCE_REDEEM("19", "强制赎回"),

    DIV("20", "红利下发"),

    FORCE_ADD("21", "份额强增"),

    FORCE_SUBTRACT("22", "份额强减"),

    FUND_TERMINATION("23", "基金终止"),

    FUND_LIQUIDATION("24", "基金清盘"),

    /**
     * 交易过户买入
     */
    TRANSFER_BUY("25", "交易过户买入"),

    /**
     * 交易过户卖出
     */
    TRANSFER_SELL("26", "交易过户卖出"),

    /**
     * 系列合并转入
     */
    SERIES_MERGE_IN("27", "系列合并转入"),

    /**
     * 系列合并转出
     */
    SERIES_MERGE_OUT("28", "系列合并转出"),

    /**
     * 基金转换
     */
    FUND_TRANSFER("29", "基金转换"),

    /**
     * 平衡因子兑换
     */
    BALANCE_FACTOR_EXCHANGE("30", "平衡因子兑换"),

    /**
     * 展期修改
     */
    EXPIRATION_MODIFY("31", "展期修改"),

    /**
     * 平衡因子更新
     */
    BALANCE_FACTOR_UPDATE("32", "平衡因子更新"),


    ;

    private String code;
    private String description;

    BusinessTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return this.code;
    }

    public String getDescription() {
        return this.description;
    }

    public static BusinessTypeEnum getByCode(String code) {
        BusinessTypeEnum[] businessTypeEnums = values();

        for (int i = 0; i < businessTypeEnums.length; ++i) {
            BusinessTypeEnum businessTypeEnum = businessTypeEnums[i];
            if (businessTypeEnum.getCode().equals(code)) {
                return businessTypeEnum;
            }
        }

        return null;
    }

}
