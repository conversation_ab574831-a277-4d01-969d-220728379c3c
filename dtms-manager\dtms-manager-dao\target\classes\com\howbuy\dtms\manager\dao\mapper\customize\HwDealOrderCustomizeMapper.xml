<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.customize.HwDealOrderCustomizeMapper">
    <select id="queryInTransitOrderByPayEndDt" parameterType="map"
            resultMap="com.howbuy.dtms.manager.dao.mapper.HwDealOrderMapper.BaseResultMap">
        select
        <include refid="com.howbuy.dtms.manager.dao.mapper.HwDealOrderMapper.Base_Column_List"/>
        from hw_deal_order t
        where t.REC_STAT = '0'
          and t.order_status = '1'
          and t.middle_busi_code in ('1120', '1122', '112A', '112B')
          and t.pay_status in ('1', '2')
          and t.currency = '840'
          and t.hk_cust_no = #{hkCustNo,jdbcType=VARCHAR}
          and exists (select 1
                      from HW_DEAL_ORDER_DTL b
                      where b.deal_no = t.deal_no
                        and b.rec_stat = '0'
                        and b.product_pay_end_dt <![CDATA[ <= ]]> #{payEndDt,jdbcType=VARCHAR})
    </select>

    <select id="batchQueryCustsWithPendingOrder" resultType="java.lang.String">
        SELECT DISTINCT
            o.hk_cust_no
        FROM 
            hw_deal_order o
            INNER JOIN hw_deal_order_dtl d ON o.deal_no = d.deal_no
        WHERE 
            o.hk_cust_no IN
            <foreach collection="hkCustNoList" item="hkCustNo" open="(" separator="," close=")">
                #{hkCustNo,jdbcType=VARCHAR}
            </foreach>
            AND d.middle_busi_code IN ('1120', '1122', '112A', '112B')
            AND o.order_status = '1'
            AND o.pay_status = '4'
            <if test="fundCode != null and fundCode != ''">
                AND d.fund_code = #{fundCode,jdbcType=VARCHAR}
            </if>
            AND o.rec_stat = '0'
            AND d.rec_stat = '0'
    </select>

    <select id="queryPendingOrderCustsByFundCode" resultType="java.lang.String">
        SELECT DISTINCT
            hk_cust_no
        FROM
            hw_deal_order
        WHERE
            product_code = #{fundCode,jdbcType=VARCHAR}
          AND middle_busi_code IN ('1120', '1122', '112A', '112B')
          AND order_status = '1'
          AND pay_status = '4'
          AND rec_stat = '0'
    </select>
</mapper>

