/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

/**
 * @description: 基金状态 0-可申购赎回、1-发行、4-停止交易、5-停止申购、6-停止赎回、8-基金终止、9-基金封闭
 * <AUTHOR>
 * @date 2024/5/7 15:13
 * @since JDK 1.8
 */
public enum FundStatEnum {
    /**
     * 可申购赎回
     */
    PURCHASE_REDEMPTION("0", "可申购赎回"),
    /**
     * 发行
     */
    ISSUE("1", "发行"),
    /**
     * 停止交易
     */
    STOP_TRADE("4", "停止交易"),
    /**
     * 停止申购
     */
    STOP_PURCHASE("5", "停止申购"),
    /**
     * 停止赎回
     */
    STOP_REDEMPTION("6", "停止赎回"),
    /**
     * 基金终止
     */
    FUND_TERMINATION("8", "基金终止"),
    /**
     * 基金封闭
     */
    FUND_CLOSED("9", "基金封闭");

    private String code;
    private String desc;

    FundStatEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static FundStatEnum getEnumByCode(String code) {
        for (FundStatEnum fundStatEnum : FundStatEnum.values()) {
            if (fundStatEnum.getCode().equals(code)) {
                return fundStatEnum;
            }
        }
        return null;
    }
}
