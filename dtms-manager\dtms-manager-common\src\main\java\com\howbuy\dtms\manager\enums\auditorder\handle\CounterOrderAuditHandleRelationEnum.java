package com.howbuy.dtms.manager.enums.auditorder.handle;

import com.howbuy.dtms.common.enums.CounterOrderAuditStatusEnum;
import com.howbuy.dtms.manager.enums.ExceptionCodeEnum;
import com.howbuy.dtms.manager.enums.auditorder.CounterBizTypeEnum;
import com.howbuy.dtms.manager.exception.BusinessException;

public enum CounterOrderAuditHandleRelationEnum {

    /************************************等待复核>>>>>>>>审核通过**************************************/
    SUBS_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.SUBS.getCode()),

    PURCHASE_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.PURCHASE.getCode()),

    _112A_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum._112A.getCode()),

    _112B_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum._112B.getCode()),

    SUB_AND_FIRST_PAID_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),

    REDEEM_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.REDEEM_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.REDEEM.getCode()),

    REVOKE_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.REVOKE_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.REVOKE.getCode()),

    EXTENSION_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.EXTENSION_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.EXTENSION.getCode()),

    FUND_TRANSFER_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.FUND_TRANSFER_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.FUND_TRANSFER.getCode()),

    FORCE_SUBTRACT_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.FORCE_SUBTRACT_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.FORCE_SUBTRACT.getCode()),

    FORCE_ADD_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.FORCE_ADD_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.FORCE_ADD.getCode()),

    BATCH_PAID_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.BATCH_PAID_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.BATCH_PAID.getCode()),

    FULL_BATCH_SUBSCRIBE_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.FULL_BATCH_SUBSCRIBE_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.FULL_BATCH_SUBSCRIBE.getCode()),

    FULL_BATCH_REDEEM_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.FULL_BATCH_REDEEM_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.FULL_BATCH_REDEEM.getCode()),

    FUND_TX_ACCT_NO_OPEN_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.FUND_TX_ACCT_NO_OPEN_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.FUND_TX_ACCT_NO_OPEN.getCode()),


    /************************************等待复核驳回至经办人**************************************/
    PURCHASE_COUNTER_ORDER_AUDIT_NOT_PASS_TO_REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.PURCHASE.getCode()),

    SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_TO__REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.SUBS.getCode()),

    _112A_COUNTER_ORDER_AUDIT_NOT_PASS_TO__REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum._112A.getCode()),

    _112B_COUNTER_ORDER_AUDIT_NOT_PASS_TO__REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum._112B.getCode()),

    SUB_AND_FIRST_PAID_COUNTER_ORDER_AUDIT_NOT_PASS_TO__REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),

    CRM_COUNTER_ORDER_AUDIT_NOT_PASS_TO__REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.CRM_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.SUBS.getCode()),

    REDEEM_COUNTER_ORDER_AUDIT_NOT_PASS_TO__REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.REDEEM.getCode()),

    REVOKE_COUNTER_ORDER_AUDIT_NOT_PASS_TO__REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.REVOKE.getCode()),


    EXTENSION_COUNTER_ORDER_AUDIT_NOT_PASS_TO__REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.EXTENSION.getCode()),

    FUND_TRANSFER_COUNTER_ORDER_AUDIT_NOT_PASS_TO__REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.FUND_TRANSFER.getCode()),

    FORCE_SUBTRACT_COUNTER_ORDER_AUDIT_NOT_PASS_TO_REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.FORCE_SUBTRACT.getCode()),

    FORCE_ADD_COUNTER_ORDER_AUDIT_NOT_PASS_TO_REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.FORCE_ADD.getCode()),

    BATCH_PAID_COUNTER_ORDER_AUDIT_NOT_PASS_TO_REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.BATCH_PAID.getCode()),

    FULL_BATCH_SUBSCRIBE_COUNTER_ORDER_AUDIT_NOT_PASS_TO_REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.FULL_BATCH_SUBSCRIBE.getCode()),

    FULL_BATCH_REDEEM_COUNTER_ORDER_AUDIT_NOT_PASS_TO_REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.FULL_BATCH_REDEEM.getCode()),

    FUND_TX_ACCT_NO_OPEN_COUNTER_ORDER_AUDIT_NOT_PASS_TO_REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.FUND_TX_ACCT_NO_OPEN.getCode()),


    /************************************等待回访>>>>>>>>审核通过处理器**************************************/
    SUBS_AUDIT_PASS_WAIT_REVISIT_TO_APPROVE(CounterOrderHandleEnum.WAIT_REVISIT_TO_APPROVE_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.SUBS.getCode()),

    PURCHASE_AUDIT_PASS_WAIT_REVISIT_TO_APPROVE(CounterOrderHandleEnum.WAIT_REVISIT_TO_APPROVE_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.PURCHASE.getCode()),

    _112A_AUDIT_PASS_WAIT_REVISIT_TO_APPROVE(CounterOrderHandleEnum.WAIT_REVISIT_TO_APPROVE_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum._112A.getCode()),

    _112B_AUDIT_PASS_WAIT_REVISIT_TO_APPROVE(CounterOrderHandleEnum.WAIT_REVISIT_TO_APPROVE_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum._112B.getCode()),

    SUB_AND_FIRST_PAID_AUDIT_PASS_WAIT_REVISIT_TO_APPROVE(CounterOrderHandleEnum.WAIT_REVISIT_TO_APPROVE_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),

    REDEEM_AUDIT_PASS_WAIT_REVISIT_TO_APPROVE(CounterOrderHandleEnum.WAIT_REVISIT_TO_APPROVE_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.REDEEM.getCode()),

    REVOKE_AUDIT_PASS_WAIT_REVISIT_TO_APPROVE(CounterOrderHandleEnum.WAIT_REVISIT_TO_APPROVE_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.REVOKE.getCode()),

    EXTENSION_AUDIT_PASS_WAIT_REVISIT_TO_APPROVE(CounterOrderHandleEnum.WAIT_REVISIT_TO_APPROVE_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.EXTENSION.getCode()),

    FUND_TRANSFER_AUDIT_PASS_WAIT_REVISIT_TO_APPROVE(CounterOrderHandleEnum.WAIT_REVISIT_TO_APPROVE_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.FUND_TRANSFER.getCode()),

    /************************************等待回访>>>>>>>>驳回至经办处理器**************************************/
    SUBS_AUDIT_NOT_PASS_WAIT_REVISIT_TO_REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.WAIT_REVISIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.SUBS.getCode()),

    PURCHASE_AUDIT_NOT_PASS_WAIT_REVISIT_TO_REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.WAIT_REVISIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.PURCHASE.getCode()),

    _112A_AUDIT_NOT_PASS_WAIT_REVISIT_TO_REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.WAIT_REVISIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum._112A.getCode()),

    _112B_AUDIT_NOT_PASS_WAIT_REVISIT_TO_REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.WAIT_REVISIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum._112B.getCode()),

    SUB_AND_FIRST_PAID_AUDIT_NOT_PASS_WAIT_REVISIT_TO_REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.WAIT_REVISIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),

    REDEEM_AUDIT_NOT_PASS_WAIT_REVISIT_TO_REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.WAIT_REVISIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.REDEEM.getCode()),
    REVOKE_AUDIT_NOT_PASS_WAIT_REVISIT_TO_REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.WAIT_REVISIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.REVOKE.getCode()),

    EXTENSION_AUDIT_NOT_PASS_WAIT_REVISIT_TO_REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.WAIT_REVISIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.EXTENSION.getCode()),

    FUND_TRANSFER_AUDIT_NOT_PASS_WAIT_REVISIT_TO_REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.WAIT_REVISIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.FUND_TRANSFER.getCode()),

    /************************************等待复核>>>>>作废的功能**************************************/
    PURCHASE_COUNTER_ORDER_AUDIT_NOT_PASS_TO_VOIDED(CounterOrderHandleEnum.COUNTER_ORDER_AUDIT_VOIDED_HANDLE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.PURCHASE.getCode()),

    SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_TO_VOIDED(CounterOrderHandleEnum.COUNTER_ORDER_AUDIT_VOIDED_HANDLE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.SUBS.getCode()),

    _112A_COUNTER_ORDER_AUDIT_NOT_PASS_TO_VOIDED(CounterOrderHandleEnum.COUNTER_ORDER_AUDIT_VOIDED_HANDLE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum._112A.getCode()),

    _112B_COUNTER_ORDER_AUDIT_NOT_PASS_TO_VOIDED(CounterOrderHandleEnum.COUNTER_ORDER_AUDIT_VOIDED_HANDLE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum._112B.getCode()),

    SUB_AND_FIRST_PAID_COUNTER_ORDER_AUDIT_NOT_PASS_TO_VOIDED(CounterOrderHandleEnum.COUNTER_ORDER_AUDIT_VOIDED_HANDLE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),


    REDEEM_COUNTER_ORDER_AUDIT_NOT_PASS_TO_VOIDED(CounterOrderHandleEnum.COUNTER_ORDER_AUDIT_VOIDED_HANDLE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.REDEEM.getCode()),

    REVOKE_COUNTER_ORDER_AUDIT_NOT_PASS_TO_VOIDED(CounterOrderHandleEnum.COUNTER_ORDER_AUDIT_VOIDED_HANDLE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.REVOKE.getCode()),

    EXTENSION_COUNTER_ORDER_AUDIT_NOT_PASS_TO_VOIDED(CounterOrderHandleEnum.COUNTER_ORDER_AUDIT_VOIDED_HANDLE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.EXTENSION.getCode()),

    FUND_TRANSFER_COUNTER_ORDER_AUDIT_NOT_PASS_TO_VOIDED(CounterOrderHandleEnum.COUNTER_ORDER_AUDIT_VOIDED_HANDLE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.FUND_TRANSFER.getCode()),

    FORCE_SUBTRACT_COUNTER_ORDER_AUDIT_NOT_PASS_TO_VOIDED(CounterOrderHandleEnum.COUNTER_ORDER_AUDIT_VOIDED_HANDLE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.FORCE_SUBTRACT.getCode()),
    FORCE_ADD_COUNTER_ORDER_AUDIT_NOT_PASS_TO_VOIDED(CounterOrderHandleEnum.COUNTER_ORDER_AUDIT_VOIDED_HANDLE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.FORCE_ADD.getCode()),
    BATCH_PAID_COUNTER_ORDER_AUDIT_NOT_PASS_TO_VOIDED(CounterOrderHandleEnum.COUNTER_ORDER_AUDIT_VOIDED_HANDLE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.BATCH_PAID.getCode()),
    FULL_BATCH_SUBSCRIBE_COUNTER_ORDER_AUDIT_NOT_PASS_TO_VOIDED(CounterOrderHandleEnum.COUNTER_ORDER_AUDIT_VOIDED_HANDLE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.FULL_BATCH_SUBSCRIBE.getCode()),
    FULL_BATCH_REDEEM_COUNTER_ORDER_AUDIT_NOT_PASS_TO_VOIDED(CounterOrderHandleEnum.COUNTER_ORDER_AUDIT_VOIDED_HANDLE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.FULL_BATCH_REDEEM.getCode()),

    FUND_TX_ACCT_NO_OPEN_COUNTER_ORDER_AUDIT_NOT_PASS_TO_VOIDED(CounterOrderHandleEnum.COUNTER_ORDER_AUDIT_VOIDED_HANDLE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.FUND_TX_ACCT_NO_OPEN.getCode()),

    ;

    private final CounterOrderHandleEnum handleEnum;

    private final String oldAuditStatus;

    private final String auditStatus;

    private final String bizType;

    CounterOrderAuditHandleRelationEnum(CounterOrderHandleEnum handleEnum, String oldAuditStatus, String auditStatus, String bizType) {
        this.handleEnum = handleEnum;
        this.oldAuditStatus = oldAuditStatus;
        this.auditStatus = auditStatus;
        this.bizType = bizType;
    }

    /**
     * @param auditStatus
     * @param bizType
     * @return java.lang.String
     * @description: 根据审核的状态和业务类型，获取对应的执行器，
     * @author: jinqing.rao
     * @date: 2024/11/17 13:34
     * @since JDK 1.8
     */
    public static String getBizTypeByAuditStatusAndBizType(String auditStatus, String bizType) {
        for (CounterOrderAuditHandleRelationEnum counterOrderAuditHandleRelationEnum : CounterOrderAuditHandleRelationEnum.values()) {
            if (counterOrderAuditHandleRelationEnum.getAuditStatus().equals(auditStatus) && counterOrderAuditHandleRelationEnum.getBizType().equals(bizType)) {
                return counterOrderAuditHandleRelationEnum.getHandleEnum().getCode();
            }
        }
        throw new BusinessException(ExceptionCodeEnum.COUNTER_ORDER_LOGIC_CHECK_HANDLER_NOT_EXISTS);
    }

    public static String getHandleByAuditStatusAndBizType(String oldAuditStatus, String auditStatus, String busiType) {
        for (CounterOrderAuditHandleRelationEnum counterOrdeBizTypeRuleEnum : CounterOrderAuditHandleRelationEnum.values()) {
            if (counterOrdeBizTypeRuleEnum.getOldAuditStatus().equals(oldAuditStatus) && counterOrdeBizTypeRuleEnum.getAuditStatus().equals(auditStatus) && counterOrdeBizTypeRuleEnum.getBizType().equals(busiType)) {
                return counterOrdeBizTypeRuleEnum.getHandleEnum().getCode();
            }
        }
        return null;
    }

    public CounterOrderHandleEnum getHandleEnum() {
        return handleEnum;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public String getBizType() {
        return bizType;
    }

    public String getOldAuditStatus() {
        return oldAuditStatus;
    }
}
