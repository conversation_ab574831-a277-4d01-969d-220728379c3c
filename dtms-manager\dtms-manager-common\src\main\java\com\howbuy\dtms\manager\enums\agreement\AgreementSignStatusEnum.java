/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.manager.enums.agreement;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/3/27 15:45
 * @since JDK 1.8
 */
public enum AgreementSignStatusEnum {

    // 1 签署成功  0 未签署
    SIGN_SUCCESS("1", "签署成功"),
    NOT_SIGN("0", "未签署");

    private String code;

    private String desc;

    AgreementSignStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getDescByCode(String code) {
        for (AgreementSignStatusEnum agreementSignStatusEnum : AgreementSignStatusEnum.values()) {
            if (agreementSignStatusEnum.getCode().equals(code)) {
                return agreementSignStatusEnum.getDesc();
            }
        }
        return null;
    }

}
