package com.howbuy.dtms.manager.outservice.settle.dto;

import com.howbuy.dtms.settle.client.facade.validate.fundforcesubtract.FundForceSubtractValidateRequest;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:23:44+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class FundForceSubtractValidateDTOConvertMapperImpl implements FundForceSubtractValidateDTOConvertMapper {

    @Override
    public FundForceSubtractValidateRequest convert(FundForceSubtractValidateDTO dto) {
        if ( dto == null ) {
            return null;
        }

        FundForceSubtractValidateRequest fundForceSubtractValidateRequest = new FundForceSubtractValidateRequest();

        fundForceSubtractValidateRequest.setAckVol( dto.getAckVol() );
        fundForceSubtractValidateRequest.setForceSubtractRule( dto.getForceSubtractRule() );
        fundForceSubtractValidateRequest.setFundCode( dto.getFundCode() );
        fundForceSubtractValidateRequest.setFundTxAcctNo( dto.getFundTxAcctNo() );
        fundForceSubtractValidateRequest.setHkCustNo( dto.getHkCustNo() );
        fundForceSubtractValidateRequest.setNav( dto.getNav() );
        fundForceSubtractValidateRequest.setNavDt( dto.getNavDt() );
        fundForceSubtractValidateRequest.setSerialNumber( dto.getSerialNumber() );
        fundForceSubtractValidateRequest.setShareRegDt( dto.getShareRegDt() );
        fundForceSubtractValidateRequest.setTradeDt( dto.getTradeDt() );
        fundForceSubtractValidateRequest.setVolDtlNo( dto.getVolDtlNo() );

        return fundForceSubtractValidateRequest;
    }
}
