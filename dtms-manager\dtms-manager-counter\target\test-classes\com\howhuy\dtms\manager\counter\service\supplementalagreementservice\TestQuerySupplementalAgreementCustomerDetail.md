# SupplementalAgreementService.querySupplementalAgreementCustomerDetail 单元测试说明文档

## 测试目标
验证补充协议客户明细查询功能的正确性和完整性，确保各种查询条件组合能正确工作，并返回符合预期的结果。

## 测试范围
- 基本查询功能
- 各种查询条件筛选
- 边界条件处理
- 异常情况处理

## 测试用例说明

### 1. 协议不存在场景测试
**测试方法**：`test_querySupplementalAgreementCustomerDetail_agreement_not_exists`  
**测试目的**：验证当查询不存在的协议ID时，是否正确抛出业务异常  
**测试步骤**：
1. 准备包含不存在协议ID的请求
2. 模拟 BpSupplementalAgreementRepository.queryAgreementById 返回 null
3. 执行查询方法
4. 验证是否抛出 BusinessException，且错误码为 SUPPLEMENTAL_AGREEMENT_NOT_EXISTS

### 2. 单个客户查询场景测试
**测试方法**：`test_querySupplementalAgreementCustomerDetail_single_customer`  
**测试目的**：验证按客户号查询单个客户的补充协议明细  
**测试步骤**：
1. 准备带有客户号的请求参数
2. 模拟相关依赖返回预设数据
3. 执行查询方法
4. 验证返回结果包含正确的客户信息、持仓信息和在途信息

### 3. 持仓条件筛选场景测试
**测试方法**：`test_querySupplementalAgreementCustomerDetail_with_position`  
**测试目的**：验证只查询有持仓的客户  
**测试步骤**：
1. 准备带有持仓筛选条件的请求
2. 模拟持仓查询返回多个客户
3. 执行查询方法
4. 验证返回结果只包含有持仓的客户，且持仓标记正确

### 4. 在途条件筛选场景测试
**测试方法**：`test_querySupplementalAgreementCustomerDetail_with_pending`  
**测试目的**：验证只查询有在途交易的客户  
**测试步骤**：
1. 准备带有在途筛选条件的请求
2. 模拟在途查询返回多个客户
3. 执行查询方法
4. 验证返回结果只包含有在途交易的客户，且在途标记正确

### 5. 持仓和在途条件组合筛选场景测试
**测试方法**：`test_querySupplementalAgreementCustomerDetail_with_position_and_pending`  
**测试目的**：验证同时使用持仓和在途条件进行组合筛选  
**测试步骤**：
1. 准备同时带有持仓和在途筛选条件的请求
2. 模拟持仓和在途查询分别返回不同的客户列表（有交集）
3. 执行查询方法
4. 验证返回结果仅包含既有持仓又有在途的客户（两个列表的交集）

### 6. 是否需要签署协议筛选场景测试
**测试方法**：`test_querySupplementalAgreementCustomerDetail_with_need_sign`  
**测试目的**：验证按是否需要签署协议条件筛选客户  
**测试步骤**：
1. 准备带有需要签署协议标记的请求
2. 模拟相关依赖返回符合条件的客户
3. 执行查询方法
4. 验证返回结果中的客户都需要签署协议

### 7. 无符合条件的客户场景测试
**测试方法**：`test_querySupplementalAgreementCustomerDetail_no_matching_customers`  
**测试目的**：验证当没有客户符合筛选条件时的返回结果  
**测试步骤**：
1. 准备带有筛选条件的请求
2. 模拟查询返回空列表
3. 执行查询方法
4. 验证返回空结果集（total=0，list为空列表）

## 测试数据准备
- 模拟补充协议配置
- 模拟客户明细信息
- 模拟持仓客户列表
- 模拟在途交易客户列表

## 注意事项
1. 使用 PowerMockito 模拟静态方法调用
2. 模拟依赖接口的返回值要合理设置
3. 测试边界条件和异常场景
4. 检查返回结果的数据结构和内容正确性

## 预期结果
所有测试用例都应成功通过，确保补充协议客户明细查询功能在各种场景下能够正确工作。 