package com.howbuy.dtms.manager.outservice.settle.dto;

import com.howbuy.dtms.settle.client.facade.trade.fundreedem.FundRedeemRequest;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:23:43+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class FundRedeemRequestDTOConvertMapperImpl implements FundRedeemRequestDTOConvertMapper {

    @Override
    public FundRedeemRequest convert(FundRedeemRequestDTO dto) {
        if ( dto == null ) {
            return null;
        }

        FundRedeemRequest fundRedeemRequest = new FundRedeemRequest();

        fundRedeemRequest.setTraceId( dto.getTraceId() );
        fundRedeemRequest.setAppAmt( dto.getAppAmt() );
        fundRedeemRequest.setAppDt( dto.getAppDt() );
        fundRedeemRequest.setAppStatus( dto.getAppStatus() );
        fundRedeemRequest.setAppTm( dto.getAppTm() );
        fundRedeemRequest.setAppVol( dto.getAppVol() );
        fundRedeemRequest.setCpAcctNo( dto.getCpAcctNo() );
        fundRedeemRequest.setCurrency( dto.getCurrency() );
        fundRedeemRequest.setDealDtlNo( dto.getDealDtlNo() );
        fundRedeemRequest.setFeeRate( dto.getFeeRate() );
        fundRedeemRequest.setFundCode( dto.getFundCode() );
        fundRedeemRequest.setFundTxAcctNo( dto.getFundTxAcctNo() );
        fundRedeemRequest.setHkCustNo( dto.getHkCustNo() );
        fundRedeemRequest.setMiddleOrderNo( dto.getMiddleOrderNo() );
        fundRedeemRequest.setOpenDt( dto.getOpenDt() );
        fundRedeemRequest.setPreSubmitTaDt( dto.getPreSubmitTaDt() );
        fundRedeemRequest.setPreSubmitTaTm( dto.getPreSubmitTaTm() );
        fundRedeemRequest.setRedeemType( dto.getRedeemType() );
        fundRedeemRequest.setSerialNumber( dto.getSerialNumber() );
        fundRedeemRequest.setShareRegDt( dto.getShareRegDt() );
        fundRedeemRequest.setSubmitTaDt( dto.getSubmitTaDt() );

        return fundRedeemRequest;
    }
}
