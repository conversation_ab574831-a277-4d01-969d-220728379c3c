package com.howbuy.dtms.manager.counter.request.hwpiggytradeappimport;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 计算储蓄罐交易申请手续费请求参数
 *
 * <AUTHOR>
 * @date 2025-07-21 19:24:03
 * @since JDK 1.8
 */
@Getter
@Setter
public class ComputePiggyTradeAppFeeRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 储蓄罐交易申请记录ID
     */
    private Long id;
    
    /**
     * 买入金额
     */
    private String buyAmt;
    
    /**
     * 折扣率
     */
    private String discountRate;
}
