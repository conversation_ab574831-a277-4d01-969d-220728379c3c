package com.howbuy.dtms.manager.counter.request.paymentcheck;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 重置交易支付标识请求对象
 *
 * <AUTHOR>
 * @date 2025-07-23 16:08:57
 * @since JDK 1.8
 */
@Setter
@Getter
public class ResetTxPmtFlagRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 支付订单号
     */
    @NotBlank(message = "支付订单号不能为空")
    private String pmtDealNo;
}
