/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

/**
 * @description: 储蓄罐申请来源 0-excel 1-可用余额、2-客户控管表、3-退款控管表、4-自动赎回
 * <AUTHOR>
 * @date 2025/7/15 15:49
 * @since JDK 1.8
 */
public enum PiggyAppSourceEnum {

    EXCEL("0", "excel"),
    AVA<PERSON>ABLE_BALANCE("1", "可用余额"),
    CUSTOMER_CONTROL_TABLE("2", "客户控管表"),
    REFUND_CONTROL_TABLE("3", "退款控管表"),
    AUTO_REDEMPTION("4", "自动赎回");

    private String code;
    private String desc;

    PiggyAppSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     * @param code 枚举code
     * @return 枚举对象
     */
    public static PiggyAppSourceEnum getEnumByCode(String code) {
        for (PiggyAppSourceEnum piggyAppSourceEnum : PiggyAppSourceEnum.values()) {
            if (piggyAppSourceEnum.getCode().equals(code)) {
                return piggyAppSourceEnum;
            }
        }
        return null;
    }

    /**
     * 根据code获取描述
     * @param code 枚举code
     * @return 描述
     */
    public static String getDescByCode(String code) {
        PiggyAppSourceEnum piggyAppSourceEnum = getEnumByCode(code);
        return piggyAppSourceEnum != null ? piggyAppSourceEnum.getDesc() : null;
    }

    /**
     * 校验code是否有效
     * @param code 枚举code
     * @return boolean true-有效，false-无效
     */
    public static boolean isValidCode(String code) {
        return getEnumByCode(code) != null;
    }

}
