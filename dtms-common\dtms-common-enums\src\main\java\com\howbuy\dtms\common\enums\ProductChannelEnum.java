/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

/**
 * <AUTHOR>
 * @description: 产品渠道 1-柜台 2-非柜台
 * @date 2024/4/24 17:39
 * @since JDK 1.8
 */
public enum ProductChannelEnum {
    /**
     * 柜台
     */
    COUNTER("1", "柜台"),
    /**
     * 非柜台
     */
    NON_COUNTER("2", "非柜台");

    private String code;
    private String desc;

    ProductChannelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ProductChannelEnum getEnumByCode(String code) {
        for (ProductChannelEnum productChannelEnum : ProductChannelEnum.values()) {
            if (productChannelEnum.getCode().equals(code)) {
                return productChannelEnum;
            }
        }
        return null;
    }

}
