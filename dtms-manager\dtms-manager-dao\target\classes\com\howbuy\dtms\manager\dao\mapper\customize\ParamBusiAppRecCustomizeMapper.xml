<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.customize.ParamBusiAppRecCustomizeMapper">

    <select id="queryParamBusiAppRecPage" resultMap="com.howbuy.dtms.manager.dao.mapper.ParamBusiAppRecMapper.BaseResultMap">
        SELECT
            <include refid="com.howbuy.dtms.manager.dao.mapper.ParamBusiAppRecMapper.Base_Column_List"/>
        FROM
            param_busi_app_rec
        where
            rec_stat = '1'
            <if test="applyStartDt != null and applyStartDt != ''">
                AND DATE_FORMAT(create_timestamp, '%Y%m%d') &gt;= #{applyStartDt}
            </if>
            <if test="applyEndDt != null and applyEndDt != ''">
                AND DATE_FORMAT(create_timestamp, '%Y%m%d') &lt;= #{applyEndDt}
            </if>
            <if test="bizType != null and bizType != ''">
                AND param_type = #{bizType}
            </if>
            <if test="auditStatusList != null and auditStatusList.size() > 0">
                AND audit_status IN
                <foreach collection="auditStatusList" item="auditStatus" open="(" separator="," close=")">
                    #{auditStatus}
                </foreach>
            </if>
        ORDER BY create_timestamp DESC
    </select>
    <select id="queryByAppId" resultType="com.howbuy.dtms.manager.dao.po.ParamBusiAppRecPO">

        SELECT
        <include refid="com.howbuy.dtms.manager.dao.mapper.ParamBusiAppRecMapper.Base_Column_List"/>
        FROM
        param_busi_app_rec
        where
        rec_stat = '1'
        AND app_id = #{appId,jdbcType=BIGINT}
    </select>
    <select id="queryByParamTypeAndOperateType" resultType="com.howbuy.dtms.manager.dao.po.ParamBusiAppRecPO">
        SELECT
        <include refid="com.howbuy.dtms.manager.dao.mapper.ParamBusiAppRecMapper.Base_Column_List"/>
        FROM
        param_busi_app_rec
        where
        rec_stat = '1'
        AND param_type = #{paramType,jdbcType=VARCHAR}
        AND operate_type = #{operateType,jdbcType=VARCHAR}
        <if test="statusList != null and statusList.size() > 0">
            AND audit_status IN
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>

    </select>
</mapper> 