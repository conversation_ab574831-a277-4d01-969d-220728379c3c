<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.HwCustInfoSnapshotMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwCustInfoSnapshotPO">
        <!--@Table hw_cust_info_snapshot-->
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="hkCustNo" column="hk_cust_no" jdbcType="VARCHAR"/>
        <result property="hboneNo" column="hbone_no" jdbcType="VARCHAR"/>
        <result property="ebrokerId" column="ebroker_id" jdbcType="VARCHAR"/>
        <result property="invstType" column="invst_type" jdbcType="VARCHAR"/>
        <result property="custState" column="cust_state" jdbcType="VARCHAR"/>
        <result property="idType" column="id_type" jdbcType="VARCHAR"/>
        <result property="idNoMask" column="id_no_mask" jdbcType="VARCHAR"/>
        <result property="idNoDigest" column="id_no_digest" jdbcType="VARCHAR"/>
        <result property="idNoCipher" column="id_no_cipher" jdbcType="VARCHAR"/>
        <result property="birthDay" column="birth_day" jdbcType="VARCHAR"/>
        <result property="custChineseName" column="cust_chinese_name" jdbcType="VARCHAR"/>
        <result property="custEnName" column="cust_en_name" jdbcType="VARCHAR"/>
        <result property="investorQualification" column="investor_qualification" jdbcType="VARCHAR"/>
        <result property="assetCertExpiredDate" column="asset_cert_expired_date" jdbcType="VARCHAR"/>
        <result property="riskToleranceLevel" column="risk_tolerance_level" jdbcType="VARCHAR"/>
        <result property="derivativeKnowledge" column="derivative_knowledge" jdbcType="VARCHAR"/>
        <result property="piggyAgreementState" column="piggy_agreement_state" jdbcType="VARCHAR"/>
        <result property="piggyFundCodeList" column="piggy_fund_code_list" jdbcType="VARCHAR"/>
        <result property="piggyAgreementSignExpiredDt" column="piggy_agreement_sign_expired_dt" jdbcType="VARCHAR"/>
        <result property="bizType" column="biz_type" jdbcType="VARCHAR"/>
        <result property="bizId" column="biz_id" jdbcType="BIGINT"/>
        <result property="recStat" column="rec_stat" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, 
        hk_cust_no, 
        hbone_no, 
        ebroker_id, 
        invst_type, 
        cust_state, 
        id_type, 
        id_no_mask, 
        id_no_digest, 
        id_no_cipher,
        birth_day,
        cust_chinese_name, 
        cust_en_name, 
        investor_qualification, 
        asset_cert_expired_date, 
        risk_tolerance_level, 
        derivative_knowledge, 
        piggy_agreement_state, 
        piggy_fund_code_list,
        piggy_agreement_sign_expired_dt,
        biz_type,
        biz_id, 
        rec_stat, 
        creator, 
        modifier, 
        create_time, 
        update_time
    </sql>

    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hw_cust_info_snapshot
        where id = #{id}
    </select>

    <!-- 查询符合条件的数据 -->
    <select id="selectBySelective" parameterType="com.howbuy.dtms.manager.dao.po.HwCustInfoSnapshotPO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hw_cust_info_snapshot
        <where>
                <if test="id != null">
                    and id = #{id}
                </if>
                <if test="hkCustNo != null and hkCustNo != ''">
                    and hk_cust_no = #{hkCustNo}
                </if>
                <if test="hboneNo != null and hboneNo != ''">
                    and hbone_no = #{hboneNo}
                </if>
                <if test="ebrokerId != null and ebrokerId != ''">
                    and ebroker_id = #{ebrokerId}
                </if>
                <if test="invstType != null and invstType != ''">
                    and invst_type = #{invstType}
                </if>
                <if test="custState != null and custState != ''">
                    and cust_state = #{custState}
                </if>
                <if test="idType != null and idType != ''">
                    and id_type = #{idType}
                </if>
                <if test="idNoMask != null and idNoMask != ''">
                    and id_no_mask = #{idNoMask}
                </if>
                <if test="idNoDigest != null and idNoDigest != ''">
                    and id_no_digest = #{idNoDigest}
                </if>
                <if test="birthDay != null and birthDay != ''">
                    and birth_day = #{birthDay}
                </if>
                <if test="custChineseName != null and custChineseName != ''">
                    and cust_chinese_name = #{custChineseName}
                </if>
                <if test="custEnName != null and custEnName != ''">
                    and cust_en_name = #{custEnName}
                </if>
                <if test="investorQualification != null and investorQualification != ''">
                    and investor_qualification = #{investorQualification}
                </if>
                <if test="assetCertExpiredDate != null and assetCertExpiredDate != ''">
                    and asset_cert_expired_date = #{assetCertExpiredDate}
                </if>
                <if test="riskToleranceLevel != null and riskToleranceLevel != ''">
                    and risk_tolerance_level = #{riskToleranceLevel}
                </if>
                <if test="derivativeKnowledge != null and derivativeKnowledge != ''">
                    and derivative_knowledge = #{derivativeKnowledge}
                </if>
                <if test="piggyAgreementState != null and piggyAgreementState != ''">
                    and piggy_agreement_state = #{piggyAgreementState}
                </if>
                <if test="piggyFundCodeList != null and piggyFundCodeList != ''">
                    and piggy_fund_code_list = #{piggyFundCodeList}
                </if>
                <if test="bizType != null and bizType != ''">
                    and biz_type = #{bizType}
                </if>
                <if test="bizId != null">
                    and biz_id = #{bizId}
                </if>
                <if test="recStat != null and recStat != ''">
                    and rec_stat = #{recStat}
                </if>
                <if test="creator != null and creator != ''">
                    and creator = #{creator}
                </if>
                <if test="modifier != null and modifier != ''">
                    and modifier = #{modifier}
                </if>
                <if test="createTime != null">
                    and create_time = #{createTime}
                </if>
                <if test="updateTime != null and updateTime != ''">
                    and update_time = #{updateTime}
                </if>
        </where>
        order by id
    </select>


    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(*)
        from hw_cust_info_snapshot
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and hk_cust_no = #{hkCustNo}
            </if>
            <if test="hboneNo != null and hboneNo != ''">
                and hbone_no = #{hboneNo}
            </if>
            <if test="ebrokerId != null and ebrokerId != ''">
                and ebroker_id = #{ebrokerId}
            </if>
            <if test="invstType != null and invstType != ''">
                and invst_type = #{invstType}
            </if>
            <if test="custState != null and custState != ''">
                and cust_state = #{custState}
            </if>
            <if test="idType != null and idType != ''">
                and id_type = #{idType}
            </if>
            <if test="idNoMask != null and idNoMask != ''">
                and id_no_mask = #{idNoMask}
            </if>
            <if test="idNoDigest != null and idNoDigest != ''">
                and id_no_digest = #{idNoDigest}
            </if>
            <if test="birthDay != null and birthDay != ''">
                and birth_day = #{birthDay}
            </if>
            <if test="custChineseName != null and custChineseName != ''">
                and cust_chinese_name = #{custChineseName}
            </if>
            <if test="custEnName != null and custEnName != ''">
                and cust_en_name = #{custEnName}
            </if>
            <if test="investorQualification != null and investorQualification != ''">
                and investor_qualification = #{investorQualification}
            </if>
            <if test="assetCertExpiredDate != null and assetCertExpiredDate != ''">
                and asset_cert_expired_date = #{assetCertExpiredDate}
            </if>
            <if test="riskToleranceLevel != null and riskToleranceLevel != ''">
                and risk_tolerance_level = #{riskToleranceLevel}
            </if>
            <if test="derivativeKnowledge != null and derivativeKnowledge != ''">
                and derivative_knowledge = #{derivativeKnowledge}
            </if>
            <if test="piggyAgreementState != null and piggyAgreementState != ''">
                and piggy_agreement_state = #{piggyAgreementState}
            </if>
            <if test="piggyFundCodeList != null and piggyFundCodeList != ''">
                and piggy_fund_code_list = #{piggyFundCodeList}
            </if>
            <if test="bizType != null and bizType != ''">
                and biz_type = #{bizType}
            </if>
            <if test="bizId != null">
                and biz_id = #{bizId}
            </if>
            <if test="recStat != null and recStat != ''">
                and rec_stat = #{recStat}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="modifier != null and modifier != ''">
                and modifier = #{modifier}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null and updateTime != ''">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>

    <select id="selectByBizIdAndType" resultType="com.howbuy.dtms.manager.dao.po.HwCustInfoSnapshotPO">
        select
        <include refid="Base_Column_List" />
        from hw_cust_info_snapshot
        where biz_id = #{bizId} and biz_type = #{bizType}
        and rec_stat = '0'
        ORDER BY create_time DESC, id DESC
        LIMIT 1
    </select>
</mapper>

