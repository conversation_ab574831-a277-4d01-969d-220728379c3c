<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.customize.HwCustBalanceFactorCustomizeMapper">
    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, hk_cust_no, fund_tx_acct_no, fund_code, nav_dt, balance_factor, rec_stat, create_timestamp,
        update_timestamp, version
    </sql>

    <!-- 查询条件 -->
    <sql id="Query_Where_Clause">
        <where>
            <if test="hkCustNo != null and hkCustNo != ''">
                AND hk_cust_no = #{hkCustNo, jdbcType=VARCHAR}
            </if>
            <if test="fundCodeList != null and fundCodeList.size() > 0">
                AND fund_code IN
                <foreach collection="fundCodeList" item="fundCode" open="(" separator="," close=")">
                    #{fundCode, jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test='isNotEqualZero != null and isNotEqualZero == "1"'>
                AND balance_factor != 0
            </if>
            <if test='isNotEqualZero != null and isNotEqualZero == "0"'>
                AND balance_factor = 0
            </if>
            <if test="startNavDt != null and startNavDt != ''">
                AND nav_dt >= #{startNavDt, jdbcType=VARCHAR}
            </if>
            <if test="endNavDt != null and endNavDt != ''">
                AND nav_dt &lt;= #{endNavDt, jdbcType=VARCHAR}
            </if>
        </where>
    </sql>

    <!-- 分页查询柜台平衡因子信息 -->
    <select id="pageQuery" resultType="com.howbuy.dtms.manager.dao.po.HwCustBalanceFactorPO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hw_cust_balance_factor
        <include refid="Query_Where_Clause"/>
        ORDER BY fund_code, hk_cust_no
    </select>

    <!-- 查询总平衡因子 -->
    <select id="queryTotalBalanceFactor" resultType="java.math.BigDecimal">
        SELECT SUM(ifnull(balance_factor, 0))
        FROM hw_cust_balance_factor
        <include refid="Query_Where_Clause"/>
    </select>
</mapper>