/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.manager.common;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/7/22
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PageRequest extends BaseRequest {


    private int pageNo = 1;

    private int pageSize = 20;
}
