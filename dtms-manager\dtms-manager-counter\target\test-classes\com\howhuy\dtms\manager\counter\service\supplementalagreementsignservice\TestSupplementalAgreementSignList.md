# SupplementalAgreementSignService 单元测试说明文档

## 测试目标

验证补充协议签约Service的功能正确性和完整性，确保各种查询条件组合能正确工作，并验证签署提交功能的正确实现和异常处理能力。

## 测试范围

- 补充协议签约列表查询功能
- 补充协议签署提交功能
- 各种边界条件和异常情况处理

## 测试用例说明

### 1. 查询补充协议签约列表成功场景测试

**测试方法**：`test_querySupplementalAgreementSignList_success`  
**测试目的**：验证正常参数下查询补充协议签约列表功能是否正确  
**测试步骤**：

1. 准备包含完整参数的查询请求
2. 模拟 HwCustSupplementalAgreementRepository.querySupplementalAgreementSignList 返回预设数据
3. 执行查询方法
4. 验证返回结果的正确性和完整性

### 2. 查询补充协议签约列表空结果场景测试

**测试方法**：`test_querySupplementalAgreementSignList_empty_result`  
**测试目的**：验证无匹配结果时的处理逻辑  
**测试步骤**：

1. 准备查询请求
2. 模拟 HwCustSupplementalAgreementRepository.querySupplementalAgreementSignList 返回空结果
3. 执行查询方法
4. 验证返回的空列表处理是否正确

### 3. 补充协议签署提交成功场景测试

**测试方法**：`test_submitSupplementalAgreementSign_success`  
**测试目的**：验证正常参数下签署提交功能是否正确  
**测试步骤**：

1. 准备包含完整参数的签署提交请求
2. 模拟 BpSupplementalAgreementRepository.queryAgreementConfigByAgrId 返回协议信息
3. 模拟 HwCustSupplementalAgreementRepository.updateByAgrId 返回成功
4. 执行签署提交方法
5. 验证方法执行不抛出异常

### 4. 协议不存在场景测试

**测试方法**：`test_submitSupplementalAgreementSign_agreement_not_exists`  
**测试目的**：验证协议不存在时的异常处理  
**测试步骤**：

1. 准备签署提交请求
2. 模拟 BpSupplementalAgreementRepository.queryAgreementConfigByAgrId 返回 null
3. 执行签署提交方法
4. 验证是否抛出业务异常，且错误码为 SUPPLEMENTAL_AGREEMENT_NOT_EXISTS

### 5. 已签署成功状态再次签署场景测试

**测试方法**：`test_submitSupplementalAgreementSign_already_signed`  
**测试目的**：验证已签署成功的协议再次签署时的异常处理  
**测试步骤**：

1. 准备签署状态为已签署成功的提交请求
2. 模拟 BpSupplementalAgreementRepository.queryAgreementConfigByAgrId 返回协议信息
3. 执行签署提交方法
4. 验证是否抛出业务异常，且错误码为 SUPPLEMENTAL_AGREEMENT_SIGN_SUCCESS_RE_SIGN_ERROR

### 6. 更新失败场景测试

**测试方法**：`test_submitSupplementalAgreementSign_update_failed`  
**测试目的**：验证数据库更新失败时的异常处理  
**测试步骤**：

1. 准备签署提交请求
2. 模拟 BpSupplementalAgreementRepository.queryAgreementConfigByAgrId 返回协议信息
3. 模拟 HwCustSupplementalAgreementRepository.updateByAgrId 返回 0（更新失败）
4. 执行签署提交方法
5. 验证是否抛出业务异常，且错误码为 SUPPLEMENTAL_AGREEMENT_SIGN_SUBMIT_FAIL

## 测试数据准备

- 模拟补充协议配置BO对象
- 模拟补充协议配置PO对象
- 模拟用户信息

## 注意事项

1. 使用 PowerMockito 模拟静态方法调用（KeycloakUtils.getUserName）
2. 模拟依赖接口的返回值要合理设置
3. 测试边界条件和异常场景
4. 检查返回结果的数据结构和内容正确性

## 预期结果

所有测试用例都应成功通过，确保补充协议签约Service的功能在各种场景下能够正确工作。
