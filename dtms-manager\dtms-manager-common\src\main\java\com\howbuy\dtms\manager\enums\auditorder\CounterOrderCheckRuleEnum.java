package com.howbuy.dtms.manager.enums.auditorder;

public enum CounterOrderCheckRuleEnum {

    // 复核人验证规则
    COUNTER_ORDER_REVIEWER_CHECK("COUNTER_ORDER_REVIEWER_CHECK", "复核人验证"),

    COUNTER_ORDER_PREORDER_BIND_COUNTER_ORDER_CHECK("COUNTER_ORDER_PREORDER_BIND_COUNTER_ORDER_CHECK", "预约订单绑定柜台订单校验"),

    TRADE_ORDER_BIND_COUNTER_ORDER_CHECK("TRADE_ORDER_BIND_COUNTER_ORDER_CHECK", "交易订单绑定柜台订单校验"),

    COUNTER_ORDER_PREORDER_HAVE_TRADE_ORDER_CHECK("COUNTER_ORDER_PREORDER_HAVE_TRADE_ORDER_CHECK", "预约订单存在交易订单校验"),

    /**
     * 柜台审核认申购订单下单校验
     */
    COUNTER_ORDER_PURCHASE_CHECK("counter_order_purchase_check", "柜台审核认申购订单下单校验"),

    /**
     * 柜台审核赎回订单下单校验
     */
    COUNTER_ORDER_REDEEM_CHECK("counter_order_redeem_check", "柜台审核赎回订单下单校验"),

     /**
     * 柜台审核赎回订单下单校验
     */
    COUNTER_ORDER_REVOKE_CHECK("COUNTER_ORDER_REVOKE_CHECK", "柜台交易撤单下单校验"),

    /**
     * 客户年龄大于65岁
     */
    CUST_AGE_GREATER_THAN_65("CUST_AGE_GREATER_THAN_65", "客户年龄大于65岁"),

    /**
     * PI资质强控校验
     */
    CUST_PI_QUALIFICATION_CHECK("CUST_PI_QUALIFICATION_CHECK", "PI资质强控校验"),
    /**
     * 客户是否是测试账号校验
     */
    HK_CUST_NO_IS_TEST("HK_CUST_NO_IS_TEST", "香港客户号是测试账号校验"),
    /**
     * 审核通过的前置状态校验枚举
     */
    COUNTER_ORDER_AUDIT_PASS_INIT_STATUS_CHECK("COUNTER_ORDER_AUDIT_PASS_INIT_STATUS_CHECK", "审核通过的初始状态校验"),
    /**
     * CRM柜台认申购/赎回订单，材料信息校验
     */
    COUNTER_ORDER_CRM_SUBS_FILE_PARAMS_CHECK("COUNTER_ORDER_CRM_SUBS_FILE_PARAMS_CHECK","CRM柜台认申购/赎回订单，材料信息校验"),

    /**
     * 等待回访持牌人信息校验
     */
    PRODUCT_LICENSEE_INFO("PRODUCT_LICENSEE_INFO","待回访持牌人信息校验"),

    COUNTER_CLOSED("COUNTER_CLOSED","柜台收市校验"),

    /**
     * 基金是展期产品
     */
    COUNTER_ORDER_IS_EXPIRED_PRODUCT("COUNTER_ORDER_IS_EXPIRED_PRODUCT","基金是否是展期产品"),

    /**
     * 产品渠道校验
     */
    PRODUCT_CHANEL_CHECK("PRODUCT_CHANEL_CHECK","产品渠道校验"),

    /**
     * 产品业务校验
     */
    PRODUCT_BUSINESS("PRODUCT_BUSINESS", "产品业务校验"),

    /**
     * 客户状态校验
     */
    CUST_STATUS_CHECK("CUST_STATUS_CHECK", "客户状态校验"),

    /**
     * 基金转换提交校验
     */
    FUND_TRANSFER_SUBMIT_CHECK("FUND_TRANSFER_SUBMIT_CHECK", "基金转换提交校验"),

    /**
     * 清算强减校验
     */
    SETTLE_FORCE_SUBTRACT_CHECK("SETTLE_FORCE_SUBTRACT_CHECK", "清算强减校验"),

    /**
     * 清算强增校验
     */
    SETTLE_FORCE_ADD_CHECK("SETTLE_FORCE_ADD_CHECK", "清算强增校验"),

    /**
     * 批量实缴柜台校验
     */
    BATCH_PAID_COUNTER_CHECK("BATCH_PAID_COUNTER_CHECK", "批量实缴柜台校验"),

    /**
     * 批量实缴提交校验
     */
    BATCH_PAID_SUBMIT_CHECK("BATCH_PAID_SUBMIT_CHECK", "批量实缴提交校验"),

    /**
     * 全委批量认申购校验
     */
    FULL_BATCH_SUBSCRIBE_CHECK("FULL_BATCH_SUBSCRIBE_CHECK", "全委批量认申购校验"),

    /**
     * 全委批量赎回校验
     */
    FULL_BATCH_REDEEM_CHECK("FULL_BATCH_REDEEM_CHECK", "全委批量赎回校验"),

    /**
     * 全委基金交易账号存在校验
     */
    FULL_FUND_TX_ACCT_EXIST_CHECK("FULL_FUND_TX_ACCT_EXIST_CHECK", "全委基金交易账号存在校验"),
    ;




    private final String code;

    private final String value;

    private CounterOrderCheckRuleEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }


}
