<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.IoStatementContentExportMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.IoStatementContentExportPO">
        <!--@Table io_statement_content_export-->
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="recId" column="rec_id" jdbcType="BIGINT"/>
        <result property="hkCustNo" column="hk_cust_no" jdbcType="VARCHAR"/>
        <result property="fundTxAcctNo" column="fund_tx_acct_no" jdbcType="VARCHAR"/>
        <result property="manualFlag" column="manual_flag" jdbcType="VARCHAR"/>
        <result property="jsonContent" column="json_content" jdbcType="VARCHAR"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
    rec_id,
    hk_cust_no,
    fund_tx_acct_no,
    manual_flag,
    json_content,
    is_deleted,
    create_time,
    update_time
    </sql>

    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from io_statement_content_export
        where id = #{id}
    </select>

    <!-- 查询符合条件的数据 -->
    <select id="selectBySelective" parameterType="com.howbuy.dtms.manager.dao.po.IoStatementContentExportPO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from io_statement_content_export
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="recId != null">
                and rec_id = #{recId}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and hk_cust_no = #{hkCustNo}
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                and fund_tx_acct_no = #{fundTxAcctNo}
            </if>
            <if test="manualFlag != null and manualFlag != ''">
                and manual_flag = #{manualFlag}
            </if>
            <if test="jsonContent != null and jsonContent != ''">
                and json_content = #{jsonContent}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
        order by id
    </select>


    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(*)
        from io_statement_content_export
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="recId != null">
                and rec_id = #{recId}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and hk_cust_no = #{hkCustNo}
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                and fund_tx_acct_no = #{fundTxAcctNo}
            </if>
            <if test="manualFlag != null and manualFlag != ''">
                and manual_flag = #{manualFlag}
            </if>
            <if test="jsonContent != null and jsonContent != ''">
                and json_content = #{jsonContent}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into io_statement_content_export(rec_id, hk_cust_no, fund_tx_acct_no, manual_flag, json_content, is_deleted, create_time, update_time)
        values (#{recId}, #{hkCustNo}, #{fundTxAcctNo}, #{manualFlag}, #{jsonContent}, #{isDeleted}, #{createTime}, #{updateTime})
    </insert>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true">
        insert into io_statement_content_export
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recId != null">
                rec_id,
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                hk_cust_no,
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                fund_tx_acct_no,
            </if>
            <if test="manualFlag != null and manualFlag != ''">
                manual_flag,
            </if>
            <if test="jsonContent != null and jsonContent != ''">
                json_content,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recId != null">
                #{recId},
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                #{hkCustNo},
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                #{fundTxAcctNo},
            </if>
            <if test="manualFlag != null and manualFlag != ''">
                #{manualFlag},
            </if>
            <if test="jsonContent != null and jsonContent != ''">
                #{jsonContent},
            </if>
            <if test="isDeleted != null">
                #{isDeleted},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into io_statement_content_export(rec_id, hk_cust_no, fund_tx_acct_no, manual_flag, json_content, is_deleted, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.recId}, #{entity.hkCustNo}, #{entity.fundTxAcctNo}, #{entity.manualFlag}, #{entity.jsonContent}, #{entity.isDeleted}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update io_statement_content_export
        <set>
            <if test="recId != null">
                rec_id = #{recId},
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                hk_cust_no = #{hkCustNo},
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                fund_tx_acct_no = #{fundTxAcctNo},
            </if>
            <if test="manualFlag != null and manualFlag != ''">
                manual_flag = #{manualFlag},
            </if>
            <if test="jsonContent != null and jsonContent != ''">
                json_content = #{jsonContent},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from io_statement_content_export where id = #{id}
    </delete>

    <select id="selectWithParam" parameterType="com.howbuy.dtms.manager.dao.query.IoStatementContentExportQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from io_statement_content_export
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="recId != null">
                and rec_id = #{recId}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and hk_cust_no = #{hkCustNo}
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                and fund_tx_acct_no = #{fundTxAcctNo}
            </if>
            <if test="manualFlag != null and manualFlag != ''">
                and manual_flag = #{manualFlag}
            </if>
            and is_deleted = 0
        </where>
        order by id
    </select>
</mapper>

