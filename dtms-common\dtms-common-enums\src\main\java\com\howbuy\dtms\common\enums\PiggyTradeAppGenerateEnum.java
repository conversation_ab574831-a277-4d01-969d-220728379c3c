/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

/**
 * @description: (储蓄罐交易申请生成枚举 0-未生成 1-已生成 2-生成失败)
 * <AUTHOR>
 * @date 2025/7/16 13:53
 * @since JDK 1.8
 */
public enum PiggyTradeAppGenerateEnum {

    /**
     * 未生成
     */
    NOT_GENERATE("0", "未生成"),

    /**
     * 已生成
     */
    GENERATED("1", "已生成"),

    /**
     * 生成失败
     */
    GENERATE_FAILED("2", "生成失败");

    private String code;

    private String desc;

    PiggyTradeAppGenerateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    /**
     * 根据code获取枚举
     * @param code 枚举code
     * @return 枚举对象
     */
    public static PiggyTradeAppGenerateEnum getEnumByCode(String code) {
        for (PiggyTradeAppGenerateEnum generateEnum : PiggyTradeAppGenerateEnum.values()) {
            if (generateEnum.getCode().equals(code)) {
                return generateEnum;
            }
        }
        return null;
    }

    /**
     * 根据code获取描述
     * @param code 枚举code
     * @return 描述
     */
    public static String getDescByCode(String code) {
        PiggyTradeAppGenerateEnum generateEnum = getEnumByCode(code);
        return generateEnum != null ? generateEnum.getDesc() : null;
    }

}
