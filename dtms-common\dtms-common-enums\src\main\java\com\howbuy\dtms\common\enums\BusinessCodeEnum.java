/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 业务代码枚举
 * @date 2024/4/11 10:03
 * @since JDK 1.8
 */
public enum BusinessCodeEnum {

    SUBS("020", "1120", null, "认购"),
    PURCHASE("022", "1122", null, "申购"),
    REDEEM("024", "1124", null, "赎回"),
    DIV("143", "1143", BusinessTypeEnum.DIV, "分红"),
    FORCE_ADD("144", "1144", BusinessTypeEnum.FORCE_ADD, "强增"),
    FORCE_SUBTRACT("145", "1145", BusinessTypeEnum.FORCE_SUBTRACT, "强减"),
    FORCE_REDEEM("142", "1142", BusinessTypeEnum.FORCE_REDEEM, "强赎"),
    _1133("033", "1133", null, "非交易过户申请"),
    NO_TRADE_OVER_ACCOUNT_IN("134", "1134", BusinessTypeEnum.NO_TRADE_OVER_ACCOUNT_IN, "非交易过户转入"),
    NO_TRADE_OVER_ACCOUNT_OUT("135", "1135", BusinessTypeEnum.NO_TRADE_OVER_ACCOUNT_OUT, "非交易过户转出"),

    FUND_TERMINATION("151", "1151", BusinessTypeEnum.FUND_TERMINATION, "基金终止"),
    FUND_LIQUIDATION("150", "1150", BusinessTypeEnum.FUND_LIQUIDATION, "基金清盘"),
    _112A("02A", "112A", null, "认缴"),
    _112B("02B", "112B", null, "实缴"),

    _1136("036", "1136", null, "基金转换"),
    _119F("09F", "119F", null, "展期修改"),

    _113B("13B", "113B", BusinessTypeEnum.SERIES_MERGE_OUT, "系列合并转出"),
    _113C("13C", "113C", BusinessTypeEnum.SERIES_MERGE_IN, "系列合并转入"),
    _119A("09A", "119A", null, "交易过户申请"),
    _119B("09B", "119B", BusinessTypeEnum.TRANSFER_SELL, "交易过户赎回"),
    _119C("09C", "119C", BusinessTypeEnum.TRANSFER_BUY, "交易过户申购"),
    _119D("19D", "119D", BusinessTypeEnum.BALANCE_FACTOR_UPDATE, "平衡因子更新"),

    _119E("19E", "119E", BusinessTypeEnum.BALANCE_FACTOR_EXCHANGE, "平衡因子兑换"),

    ;

    private String code;
    private String mCode;

    private BusinessTypeEnum businessTypeEnum;
    private String description;

    private BusinessCodeEnum(String code, String mCode, BusinessTypeEnum businessTypeEnum, String description) {
        this.code = code;
        this.mCode = mCode;
        this.businessTypeEnum = businessTypeEnum;
        this.description = description;
    }

    public String getCode() {
        return this.code;
    }

    public String getMCode() {
        return this.mCode;
    }

    public String getDescription() {
        return this.description;
    }

    public BusinessTypeEnum getTaBusinessTypeEnum() {
        return this.businessTypeEnum;
    }

    /**
     * 获取赎回的类型
     *
     * @return
     */
    public static List<String> getReeDemTypeList() {
        return Arrays.asList(_119B.mCode, REDEEM.mCode);
    }

    public static BusinessCodeEnum getByMCode(String mCode) {
        BusinessCodeEnum[] businessCodeEnums = values();

        for (int i = 0; i < businessCodeEnums.length; ++i) {
            BusinessCodeEnum businessCodeEnum = businessCodeEnums[i];
            if (businessCodeEnum.getMCode().equals(mCode)) {
                return businessCodeEnum;
            }
        }

        return null;
    }

    public static BusinessCodeEnum getByCode(String code) {
        BusinessCodeEnum[] businessCodeEnums = values();

        for (int i = 0; i < businessCodeEnums.length; ++i) {
            BusinessCodeEnum businessCodeEnum = businessCodeEnums[i];
            if (businessCodeEnum.getCode().equals(code)) {
                return businessCodeEnum;
            }
        }

        return null;
    }

}
