/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.manager.constant;

/**
 * <AUTHOR>
 * @description: 外部系统返回码
 * @date 2023/3/16 09:48
 * @since JDK 1.8
 */
public class OutReturnCodes {
    private OutReturnCodes() {
    }


    /**
     * CRM_TRADE成功码
     */
    public static final String CRM_TRADE_SUCCESS = "0000";

    /**
     * PRODUCT成功码
     */
    public static final String PRODUCT_SUCCESS = "C030000";

    /**
     * 香港客户信息不存在
     */
    public static final String HK_ACC_ONLINE_CUST_NOT_EXISTS = "H5530003";


}