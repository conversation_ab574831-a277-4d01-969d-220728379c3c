package com.howbuy.dtms.manager.counter.request.paymentcheck;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 支付对账请求对象
 *
 * <AUTHOR>
 * @date 2025-07-23 15:43:19
 * @since JDK 1.8
 */
@Setter
@Getter
public class PaymentCheckRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 支付对账日期
     */
    @NotBlank(message = "支付对账日期不能为空")
    private String pmtCheckDt;
}
