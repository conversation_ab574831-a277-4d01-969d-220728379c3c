<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.HwPiggyTradeAppImportMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwPiggyTradeAppImportPO">
        <!--@Table hw_piggy_trade_app_import-->
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="hkCustNo" column="hk_cust_no" jdbcType="VARCHAR"/>
        <result property="fundTxAcctNo" column="fund_tx_acct_no" jdbcType="VARCHAR"/>
        <result property="custName" column="cust_name" jdbcType="VARCHAR"/>
        <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
        <result property="productName" column="product_name" jdbcType="VARCHAR"/>
        <result property="currency" column="currency" jdbcType="VARCHAR"/>
        <result property="middleBusiCode" column="middle_busi_code" jdbcType="VARCHAR"/>
        <result property="preSubmitTaDt" column="pre_submit_ta_dt" jdbcType="VARCHAR"/>
        <result property="buyAmt" column="buy_amt" jdbcType="NUMERIC"/>
        <result property="fee" column="fee" jdbcType="NUMERIC"/>
        <result property="discountRate" column="discount_rate" jdbcType="NUMERIC"/>
        <result property="paymentType" column="payment_type" jdbcType="VARCHAR"/>
        <result property="redeemType" column="redeem_type" jdbcType="VARCHAR"/>
        <result property="appAmt" column="app_amt" jdbcType="NUMERIC"/>
        <result property="appVol" column="app_vol" jdbcType="NUMERIC"/>
        <result property="redeemDirection" column="redeem_direction" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="importDt" column="import_dt" jdbcType="VARCHAR"/>
        <result property="isGenerated" column="is_generated" jdbcType="VARCHAR"/>
        <result property="dealNo" column="deal_no" jdbcType="VARCHAR"/>
        <result property="piggyAppSource" column="piggy_app_source" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
        <result property="importAppId" column="import_app_id" jdbcType="VARCHAR"/>
        <result property="relationalDealNo" column="relational_deal_no" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
        hk_cust_no, 
        fund_tx_acct_no, 
        cust_name, 
        product_code, 
        product_name, 
        currency, 
        middle_busi_code, 
        pre_submit_ta_dt, 
        buy_amt, 
        fee, 
        discount_rate, 
        payment_type, 
        redeem_type, 
        app_amt, 
        app_vol, 
        redeem_direction, 
        remark, 
        import_dt, 
        is_generated, 
        deal_no, 
        piggy_app_source, 
        creator, 
        modifier, 
        create_time, 
        update_time, 
        is_deleted, 
        version, 
        import_app_id, 
        relational_deal_no
    </sql>

    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_piggy_trade_app_import
        where id = #{id}
    </select>

    <!-- 查询符合条件的数据 -->
    <select id="selectBySelective" parameterType="com.howbuy.dtms.manager.dao.po.HwPiggyTradeAppImportPO"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_piggy_trade_app_import
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and hk_cust_no = #{hkCustNo}
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                and fund_tx_acct_no = #{fundTxAcctNo}
            </if>
            <if test="custName != null and custName != ''">
                and cust_name = #{custName}
            </if>
            <if test="productCode != null and productCode != ''">
                and product_code = #{productCode}
            </if>
            <if test="productName != null and productName != ''">
                and product_name = #{productName}
            </if>
            <if test="currency != null and currency != ''">
                and currency = #{currency}
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                and middle_busi_code = #{middleBusiCode}
            </if>
            <if test="preSubmitTaDt != null and preSubmitTaDt != ''">
                and pre_submit_ta_dt = #{preSubmitTaDt}
            </if>
            <if test="buyAmt != null">
                and buy_amt = #{buyAmt}
            </if>
            <if test="fee != null">
                and fee = #{fee}
            </if>
            <if test="discountRate != null">
                and discount_rate = #{discountRate}
            </if>
            <if test="paymentType != null and paymentType != ''">
                and payment_type = #{paymentType}
            </if>
            <if test="redeemType != null and redeemType != ''">
                and redeem_type = #{redeemType}
            </if>
            <if test="appAmt != null">
                and app_amt = #{appAmt}
            </if>
            <if test="appVol != null">
                and app_vol = #{appVol}
            </if>
            <if test="redeemDirection != null and redeemDirection != ''">
                and redeem_direction = #{redeemDirection}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="importDt != null and importDt != ''">
                and import_dt = #{importDt}
            </if>
            <if test="isGenerated != null and isGenerated != ''">
                and is_generated = #{isGenerated}
            </if>
            <if test="dealNo != null and dealNo != ''">
                and deal_no = #{dealNo}
            </if>
            <if test="piggyAppSource != null and piggyAppSource != ''">
                and piggy_app_source = #{piggyAppSource}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="modifier != null and modifier != ''">
                and modifier = #{modifier}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
            <if test="version != null">
                and version = #{version}
            </if>
            <if test="importAppId != null and importAppId != ''">
                and import_app_id = #{importAppId}
            </if>
            <if test="relationalDealNo != null">
                and relational_deal_no = #{relationalDealNo}
            </if>
        </where>
        order by id
    </select>


    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(*)
        from hw_piggy_trade_app_import
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and hk_cust_no = #{hkCustNo}
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                and fund_tx_acct_no = #{fundTxAcctNo}
            </if>
            <if test="custName != null and custName != ''">
                and cust_name = #{custName}
            </if>
            <if test="productCode != null and productCode != ''">
                and product_code = #{productCode}
            </if>
            <if test="productName != null and productName != ''">
                and product_name = #{productName}
            </if>
            <if test="currency != null and currency != ''">
                and currency = #{currency}
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                and middle_busi_code = #{middleBusiCode}
            </if>
            <if test="preSubmitTaDt != null and preSubmitTaDt != ''">
                and pre_submit_ta_dt = #{preSubmitTaDt}
            </if>
            <if test="buyAmt != null">
                and buy_amt = #{buyAmt}
            </if>
            <if test="fee != null">
                and fee = #{fee}
            </if>
            <if test="discountRate != null">
                and discount_rate = #{discountRate}
            </if>
            <if test="paymentType != null and paymentType != ''">
                and payment_type = #{paymentType}
            </if>
            <if test="redeemType != null and redeemType != ''">
                and redeem_type = #{redeemType}
            </if>
            <if test="appAmt != null">
                and app_amt = #{appAmt}
            </if>
            <if test="appVol != null">
                and app_vol = #{appVol}
            </if>
            <if test="redeemDirection != null and redeemDirection != ''">
                and redeem_direction = #{redeemDirection}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="importDt != null and importDt != ''">
                and import_dt = #{importDt}
            </if>
            <if test="isGenerated != null and isGenerated != ''">
                and is_generated = #{isGenerated}
            </if>
            <if test="dealNo != null and dealNo != ''">
                and deal_no = #{dealNo}
            </if>
            <if test="piggyAppSource != null and piggyAppSource != ''">
                and piggy_app_source = #{piggyAppSource}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="modifier != null and modifier != ''">
                and modifier = #{modifier}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
            <if test="version != null">
                and version = #{version}
            </if>
            <if test="importAppId != null and importAppId != ''">
                and import_app_id = #{importAppId}
            </if>
            <if test="relationalDealNo != null">
                and relational_deal_no = #{relationalDealNo}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into hw_piggy_trade_app_import(hk_cust_no, fund_tx_acct_no, cust_name, product_code, product_name,
                                              currency, middle_busi_code, pre_submit_ta_dt, buy_amt, fee, discount_rate,
                                              payment_type, redeem_type, app_amt, app_vol, redeem_direction, remark,
                                              import_dt, is_generated, deal_no, piggy_app_source, creator, modifier,
                                              create_time, update_time, is_deleted, version, import_app_id,
                                              relational_deal_no)
        values (#{hkCustNo}, #{fundTxAcctNo}, #{custName}, #{productCode}, #{productName}, #{currency},
                #{middleBusiCode}, #{preSubmitTaDt}, #{buyAmt}, #{fee}, #{discountRate}, #{paymentType}, #{redeemType},
                #{appAmt}, #{appVol}, #{redeemDirection}, #{remark}, #{importDt}, #{isGenerated}, #{dealNo},
                #{piggyAppSource}, #{creator}, #{modifier}, #{createTime}, #{updateTime}, #{isDeleted}, #{version},
                #{importAppId}, #{relationalDealNo})
    </insert>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true">
        insert into hw_piggy_trade_app_import
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hkCustNo != null and hkCustNo != ''">
                hk_cust_no,
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                fund_tx_acct_no,
            </if>
            <if test="custName != null and custName != ''">
                cust_name,
            </if>
            <if test="productCode != null and productCode != ''">
                product_code,
            </if>
            <if test="productName != null and productName != ''">
                product_name,
            </if>
            <if test="currency != null and currency != ''">
                currency,
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                middle_busi_code,
            </if>
            <if test="preSubmitTaDt != null and preSubmitTaDt != ''">
                pre_submit_ta_dt,
            </if>
            <if test="buyAmt != null">
                buy_amt,
            </if>
            <if test="fee != null">
                fee,
            </if>
            <if test="discountRate != null">
                discount_rate,
            </if>
            <if test="paymentType != null and paymentType != ''">
                payment_type,
            </if>
            <if test="redeemType != null and redeemType != ''">
                redeem_type,
            </if>
            <if test="appAmt != null">
                app_amt,
            </if>
            <if test="appVol != null">
                app_vol,
            </if>
            <if test="redeemDirection != null and redeemDirection != ''">
                redeem_direction,
            </if>
            <if test="remark != null and remark != ''">
                remark,
            </if>
            <if test="importDt != null and importDt != ''">
                import_dt,
            </if>
            <if test="isGenerated != null and isGenerated != ''">
                is_generated,
            </if>
            <if test="dealNo != null and dealNo != ''">
                deal_no,
            </if>
            <if test="piggyAppSource != null and piggyAppSource != ''">
                piggy_app_source,
            </if>
            <if test="creator != null and creator != ''">
                creator,
            </if>
            <if test="modifier != null and modifier != ''">
                modifier,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="importAppId != null and importAppId != ''">
                import_app_id,
            </if>
            <if test="relationalDealNo != null">
                relational_deal_no,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hkCustNo != null and hkCustNo != ''">
                #{hkCustNo},
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                #{fundTxAcctNo},
            </if>
            <if test="custName != null and custName != ''">
                #{custName},
            </if>
            <if test="productCode != null and productCode != ''">
                #{productCode},
            </if>
            <if test="productName != null and productName != ''">
                #{productName},
            </if>
            <if test="currency != null and currency != ''">
                #{currency},
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                #{middleBusiCode},
            </if>
            <if test="preSubmitTaDt != null and preSubmitTaDt != ''">
                #{preSubmitTaDt},
            </if>
            <if test="buyAmt != null">
                #{buyAmt},
            </if>
            <if test="fee != null">
                #{fee},
            </if>
            <if test="discountRate != null">
                #{discountRate},
            </if>
            <if test="paymentType != null and paymentType != ''">
                #{paymentType},
            </if>
            <if test="redeemType != null and redeemType != ''">
                #{redeemType},
            </if>
            <if test="appAmt != null">
                #{appAmt},
            </if>
            <if test="appVol != null">
                #{appVol},
            </if>
            <if test="redeemDirection != null and redeemDirection != ''">
                #{redeemDirection},
            </if>
            <if test="remark != null and remark != ''">
                #{remark},
            </if>
            <if test="importDt != null and importDt != ''">
                #{importDt},
            </if>
            <if test="isGenerated != null and isGenerated != ''">
                #{isGenerated},
            </if>
            <if test="dealNo != null and dealNo != ''">
                #{dealNo},
            </if>
            <if test="piggyAppSource != null and piggyAppSource != ''">
                #{piggyAppSource},
            </if>
            <if test="creator != null and creator != ''">
                #{creator},
            </if>
            <if test="modifier != null and modifier != ''">
                #{modifier},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="isDeleted != null">
                #{isDeleted},
            </if>
            <if test="version != null">
                #{version},
            </if>
            <if test="importAppId != null and importAppId != ''">
                #{importAppId},
            </if>
            <if test="relationalDealNo != null">
                #{relationalDealNo},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into hw_piggy_trade_app_import(hk_cust_no, fund_tx_acct_no, cust_name, product_code, product_name,
        currency, middle_busi_code, pre_submit_ta_dt, buy_amt, fee, discount_rate, payment_type, redeem_type, app_amt,
        app_vol, redeem_direction, remark, import_dt, is_generated, deal_no, piggy_app_source, creator, modifier,
        create_time, update_time, is_deleted, version, import_app_id, relational_deal_no)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.hkCustNo}, #{entity.fundTxAcctNo}, #{entity.custName}, #{entity.productCode},
            #{entity.productName}, #{entity.currency}, #{entity.middleBusiCode}, #{entity.preSubmitTaDt},
            #{entity.buyAmt}, #{entity.fee}, #{entity.discountRate}, #{entity.paymentType}, #{entity.redeemType},
            #{entity.appAmt}, #{entity.appVol}, #{entity.redeemDirection}, #{entity.remark}, #{entity.importDt},
            #{entity.isGenerated}, #{entity.dealNo}, #{entity.piggyAppSource}, #{entity.creator}, #{entity.modifier},
            #{entity.createTime}, #{entity.updateTime}, #{entity.isDeleted}, #{entity.version}, #{entity.importAppId},
            #{entity.relationalDealNo})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update hw_piggy_trade_app_import
        <set>
            <if test="hkCustNo != null and hkCustNo != ''">
                hk_cust_no = #{hkCustNo},
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                fund_tx_acct_no = #{fundTxAcctNo},
            </if>
            <if test="custName != null and custName != ''">
                cust_name = #{custName},
            </if>
            <if test="productCode != null and productCode != ''">
                product_code = #{productCode},
            </if>
            <if test="productName != null and productName != ''">
                product_name = #{productName},
            </if>
            <if test="currency != null and currency != ''">
                currency = #{currency},
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                middle_busi_code = #{middleBusiCode},
            </if>
            <if test="preSubmitTaDt != null and preSubmitTaDt != ''">
                pre_submit_ta_dt = #{preSubmitTaDt},
            </if>
            <if test="buyAmt != null">
                buy_amt = #{buyAmt},
            </if>
            <if test="fee != null">
                fee = #{fee},
            </if>
            <if test="discountRate != null">
                discount_rate = #{discountRate},
            </if>
            <if test="paymentType != null and paymentType != ''">
                payment_type = #{paymentType},
            </if>
            <if test="redeemType != null and redeemType != ''">
                redeem_type = #{redeemType},
            </if>
            <if test="appAmt != null">
                app_amt = #{appAmt},
            </if>
            <if test="appVol != null">
                app_vol = #{appVol},
            </if>
            <if test="redeemDirection != null and redeemDirection != ''">
                redeem_direction = #{redeemDirection},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="importDt != null and importDt != ''">
                import_dt = #{importDt},
            </if>
            <if test="isGenerated != null and isGenerated != ''">
                is_generated = #{isGenerated},
            </if>
            <if test="dealNo != null and dealNo != ''">
                deal_no = #{dealNo},
            </if>
            <if test="piggyAppSource != null and piggyAppSource != ''">
                piggy_app_source = #{piggyAppSource},
            </if>
            <if test="creator != null and creator != ''">
                creator = #{creator},
            </if>
            <if test="modifier != null and modifier != ''">
                modifier = #{modifier},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="importAppId != null and importAppId != ''">
                import_app_id = #{importAppId},
            </if>
            <if test="relationalDealNo != null">
                relational_deal_no = #{relationalDealNo},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--乐观锁修改生成状态为失败-->
    <update id="updateGenerateStatusToFailWithOptimisticLock">
        update hw_piggy_trade_app_import
        set is_generated = #{isGenerated},
            modifier = #{modifier},
            update_time = #{updateTime}
        where id = #{id}
          and is_generated = '0'
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from hw_piggy_trade_app_import
        where id = #{id}
    </delete>

    <!--分页查询，查询导入储蓄罐申请列表-->
    <select id="queryImportHwPiggyTradeAppList" parameterType="com.howbuy.dtms.manager.dao.query.HwPiggyTradeAppImportQuery"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_piggy_trade_app_import
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and hk_cust_no = #{hkCustNo}
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                and fund_tx_acct_no = #{fundTxAcctNo}
            </if>
            <if test="custName != null and custName != ''">
                and cust_name = #{custName}
            </if>
            <if test="productCode != null and productCode != ''">
                and product_code = #{productCode}
            </if>
            <if test="productName != null and productName != ''">
                and product_name = #{productName}
            </if>
            <if test="currency != null and currency != ''">
                and currency = #{currency}
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                and middle_busi_code = #{middleBusiCode}
            </if>
            <if test="preSubmitTaDt != null and preSubmitTaDt != ''">
                and pre_submit_ta_dt = #{preSubmitTaDt}
            </if>
            <if test="buyAmt != null">
                and buy_amt = #{buyAmt}
            </if>
            <if test="fee != null">
                and fee = #{fee}
            </if>
            <if test="discountRate != null">
                and discount_rate = #{discountRate}
            </if>
            <if test="paymentType != null and paymentType != ''">
                and payment_type = #{paymentType}
            </if>
            <if test="redeemType != null and redeemType != ''">
                and redeem_type = #{redeemType}
            </if>
            <if test="appAmt != null">
                and app_amt = #{appAmt}
            </if>
            <if test="appVol != null">
                and app_vol = #{appVol}
            </if>
            <if test="redeemDirection != null and redeemDirection != ''">
                and redeem_direction = #{redeemDirection}
            </if>
            <if test="remark != null and remark != ''">
                and remark like CONCAT('%', #{remark,jdbcType=VARCHAR}, '%')
            </if>
            <if test="importDt != null and importDt != ''">
                and import_dt = #{importDt}
            </if>
            <if test="isGenerated != null and isGenerated != ''">
                and is_generated = #{isGenerated}
            </if>
            <if test="dealNo != null and dealNo != ''">
                and deal_no = #{dealNo}
            </if>
            <if test="piggyAppSource != null and piggyAppSource != ''">
                and piggy_app_source = #{piggyAppSource}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="modifier != null and modifier != ''">
                and modifier = #{modifier}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="importAppId != null and importAppId != ''">
                and import_app_id = #{importAppId}
            </if>
            <if test="relationalDealNo != null">
                and relational_deal_no = #{relationalDealNo}
            </if>
            <if test="idList != null and idList.size > 0">
                and id in
                <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="middleBusiCodeList != null and middleBusiCodeList.size > 0">
                and middle_busi_code in
                <foreach item="item" index="index" collection="middleBusiCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="isGeneratedList != null and isGeneratedList.size > 0">
                and is_generated in
                <foreach item="item" index="index" collection="isGeneratedList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and is_deleted = 0
        </where>
        order by pre_submit_ta_dt desc, hk_cust_no, middle_busi_code, product_code, currency, redeem_direction, deal_no is null, deal_no, id
    </select>

    <!--分页查询，查询导入储蓄罐申请列表-->
    <select id="summaryImportHwPiggyTradeApp" parameterType="com.howbuy.dtms.manager.dao.query.HwPiggyTradeAppImportQuery"
            resultType="com.howbuy.dtms.manager.dao.bo.HwPiggyTradeAppImportSummaryBO">
        select
        middle_busi_code as middleBusiCode,
        count(middle_busi_code) as totalCount,
        sum(if(buy_amt is null, 0, buy_amt)) as totalBuyAmt,
        sum(if(app_amt is null, 0, app_amt)) as totalAppAmt,
        sum(if(app_vol is null, 0, app_vol)) as totalAppVol
        from hw_piggy_trade_app_import
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and hk_cust_no = #{hkCustNo}
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                and fund_tx_acct_no = #{fundTxAcctNo}
            </if>
            <if test="custName != null and custName != ''">
                and cust_name = #{custName}
            </if>
            <if test="productCode != null and productCode != ''">
                and product_code = #{productCode}
            </if>
            <if test="productName != null and productName != ''">
                and product_name = #{productName}
            </if>
            <if test="currency != null and currency != ''">
                and currency = #{currency}
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                and middle_busi_code = #{middleBusiCode}
            </if>
            <if test="preSubmitTaDt != null and preSubmitTaDt != ''">
                and pre_submit_ta_dt = #{preSubmitTaDt}
            </if>
            <if test="buyAmt != null">
                and buy_amt = #{buyAmt}
            </if>
            <if test="fee != null">
                and fee = #{fee}
            </if>
            <if test="discountRate != null">
                and discount_rate = #{discountRate}
            </if>
            <if test="paymentType != null and paymentType != ''">
                and payment_type = #{paymentType}
            </if>
            <if test="redeemType != null and redeemType != ''">
                and redeem_type = #{redeemType}
            </if>
            <if test="appAmt != null">
                and app_amt = #{appAmt}
            </if>
            <if test="appVol != null">
                and app_vol = #{appVol}
            </if>
            <if test="redeemDirection != null and redeemDirection != ''">
                and redeem_direction = #{redeemDirection}
            </if>
            <if test="remark != null and remark != ''">
                and remark like CONCAT('%', #{remark,jdbcType=VARCHAR}, '%')
            </if>
            <if test="importDt != null and importDt != ''">
                and import_dt = #{importDt}
            </if>
            <if test="isGenerated != null and isGenerated != ''">
                and is_generated = #{isGenerated}
            </if>
            <if test="dealNo != null and dealNo != ''">
                and deal_no = #{dealNo}
            </if>
            <if test="piggyAppSource != null and piggyAppSource != ''">
                and piggy_app_source = #{piggyAppSource}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="modifier != null and modifier != ''">
                and modifier = #{modifier}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="importAppId != null and importAppId != ''">
                and import_app_id = #{importAppId}
            </if>
            <if test="relationalDealNo != null">
                and relational_deal_no = #{relationalDealNo}
            </if>
            <if test="idList != null and idList.size > 0">
                and id in
                <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="middleBusiCodeList != null and middleBusiCodeList.size > 0">
                and middle_busi_code in
                <foreach item="item" index="index" collection="middleBusiCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="isGeneratedList != null and isGeneratedList.size > 0">
                and is_generated in
                <foreach item="item" index="index" collection="isGeneratedList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and is_deleted = 0
        </where>
        group by middle_busi_code
    </select>

    <select id="findTradesByRelationalDealNos" parameterType="map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hw_piggy_trade_app_import t
        WHERE t.relational_deal_no IN
        <foreach collection="relationalDealNos" item="dealNo" open="(" separator="," close=")">
            #{dealNo,jdbcType=VARCHAR}
        </foreach>
        AND t.middle_busi_code = '1124'
        AND t.is_deleted = '0'
        AND (t.is_generated = '0' or exists (select 1
        from hw_deal_order t1
        where t1.external_deal_no = t.import_app_id
        and t1.rec_stat = '0'
        and t1.order_status not in ('5', '6')
        and t.is_generated = '1'))
    </select>
</mapper>

