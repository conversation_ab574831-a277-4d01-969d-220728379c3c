package com.howbuy.dtms.manager.outservice.dtmsorder.dto;

import com.howbuy.dtms.manager.common.PageRequest;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 查询支付对账结果请求DTO
 *
 * <AUTHOR>
 * @description: 封装查询支付对账结果接口的请求参数
 * @date 2025/07/23 14:09
 * @since JDK 1.8
 */
@Getter
@Setter
public class QueryPaymentCheckResultDTO extends PageRequest {

    /**
     * 支付对账日期（必填，格式：yyyyMMdd）
     */
    private String pmtCheckDt;

    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 支付订单号
     */
    private String pmtDealNo;

    /**
     * 外部支付订单号
     */
    private String outPmtDealNo;

    /**
     * 基金代码列表
     */
    private List<String> fundCodes;

    /**
     * 支付对账标记列表 0-无需对账；1-未对账；2-对账完成；3-对账不平
     */
    private List<String> pmtCompFlags;

    /**
     * 订单类型 订单类型 1-交易 2-edda入金
     */
    private String orderType;
}
