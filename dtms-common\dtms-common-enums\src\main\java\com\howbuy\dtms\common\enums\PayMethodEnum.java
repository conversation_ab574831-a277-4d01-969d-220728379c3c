/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 支付方式
 * @date 2024/4/16 16:58
 * @since JDK 1.8
 */
public enum PayMethodEnum {
    //1-电汇、2-基金转投(原支票未使用，改为基金转投)、3-海外储蓄罐
    ELECTRIC_REMITTANCE("1", "电汇", 0),
    TRANSFER("2", "基金转投", 1),
    OVERSEAS_CXG("3", "海外储蓄罐", 2);

    private String code;
    private String desc;
    /**
     * payMentMode 111 . 该枚举所属 index 值
     */
    private int index;

    PayMethodEnum(String code, String desc, int index) {
        this.code = code;
        this.desc = desc;
        this.index = index;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static PayMethodEnum getEnumByCode(String code) {
        for (PayMethodEnum payMethodEnum : PayMethodEnum.values()) {
            if (payMethodEnum.getCode().equals(code)) {
                return payMethodEnum;
            }
        }
        return null;
    }

    /**
     * @param value
     * @return java.util.List<com.howbuy.dtms.common.enums.PayMethodEnum>
     * @description:根据值 获取 包括的枚举列表
     * <AUTHOR>
     * @date 2024/5/8 18:03
     * @since JDK 1.8
     */
    public static List<PayMethodEnum> getListByValue(String value) {
        if (StringUtils.isEmpty(value)) {
            return Lists.newArrayList();
        }
        List<PayMethodEnum> returnList = Lists.newArrayList();
        for (PayMethodEnum paymentModeEnum : PayMethodEnum.values()) {
            if (YesOrNoEnum.YES.getValue().equals(String.valueOf(value.charAt(paymentModeEnum.index)))) {
                returnList.add(paymentModeEnum);
            }
        }
        return returnList;
    }

    /**
     * @param value
     * @return java.lang.String
     * @description:(根据值获取第一个枚举code)
     * <AUTHOR>
     * @date 2024/12/11 10:36
     * @since JDK 1.8
     */
    public static String getFirstCodeByValue(String value) {
        List<PayMethodEnum> enumList = getListByValue(value);
        if (CollectionUtils.isEmpty(enumList)) {
            return null;
        }
        return enumList.get(0).getCode();
    }


    /**
     * @param code
     * @return java.lang.String
     * @description:根据枚举code获取值
     * <AUTHOR>
     * @date 2024/5/8 18:05
     * @since JDK 1.8
     */
    public static String getValueByEnumCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (PayMethodEnum payMethodEnum : PayMethodEnum.values()) {
            if (code.equals(payMethodEnum.code)) {
                sb.append(YesOrNoEnum.YES.getValue());
            } else {
                sb.append(YesOrNoEnum.NO.getValue());
            }
        }
        return sb.toString();
    }

}
