package com.howbuy.dtms.manager.enums.auditorder.execute;

import com.howbuy.dtms.common.enums.CounterOrderAuditStatusEnum;
import com.howbuy.dtms.manager.enums.auditorder.CounterBizTypeEnum;
import org.apache.commons.lang3.StringUtils;

public enum CounterOrderAuditExecuteRelationEnum {


    /**************************************************等待复核--->审核通过执行器********************************************************************/
    PURCHASE_COUNTER_ORDER_AUDIT_PASS(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.PURCHASE.getCode()),

    SUBS_COUNTER_ORDER_AUDIT_PASS(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.SUBS.getCode()),

    _112A_COUNTER_ORDER_AUDIT_PASS(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum._112A.getCode()),

    _112B_COUNTER_ORDER_AUDIT_PASS(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum._112B.getCode()),

    SUB_AND_FIRST_PAID_COUNTER_ORDER_AUDIT_PASS(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),
    REDEEM_COUNTER_ORDER_AUDIT_PASS(CounterOrderExecuteEnum.REDEEM_COUNTER_ORDER_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.REDEEM.getCode()),

    REVOKE_COUNTER_ORDER_AUDIT_PASS(CounterOrderExecuteEnum.REVOKE_COUNTER_ORDER_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.REVOKE.getCode()),

    EXTENSION_COUNTER_ORDER_AUDIT_PASS(CounterOrderExecuteEnum.EXTENSION_COUNTER_ORDER_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.EXTENSION.getCode()),

    FUND_TRANSFER_COUNTER_ORDER_AUDIT_PASS(CounterOrderExecuteEnum.FUND_TRANSFER_COUNTER_ORDER_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.FUND_TRANSFER.getCode()),

    FORCE_SUBTRACT_COUNTER_ORDER_AUDIT_PASS(CounterOrderExecuteEnum.FORCE_SUBTRACT_COUNTER_ORDER_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.FORCE_SUBTRACT.getCode()),

    FORCE_ADD_COUNTER_ORDER_AUDIT_PASS(CounterOrderExecuteEnum.FORCE_ADD_COUNTER_ORDER_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.FORCE_ADD.getCode()),

    BATCH_PAID_COUNTER_ORDER_AUDIT_PASS(CounterOrderExecuteEnum.BATCH_PAID_COUNTER_ORDER_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.BATCH_PAID.getCode()),

    FULL_BATCH_SUBSCRIBE_COUNTER_ORDER_AUDIT_PASS(CounterOrderExecuteEnum.FULL_BATCH_SUBSCRIBE_COUNTER_ORDER_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.FULL_BATCH_SUBSCRIBE.getCode()),

    FULL_BATCH_REDEEM_COUNTER_ORDER_AUDIT_PASS(CounterOrderExecuteEnum.FULL_BATCH_REDEEM_COUNTER_ORDER_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.FULL_BATCH_REDEEM.getCode()),


    FUND_TX_ACCT_NO_OPEN_COUNTER_ORDER_AUDIT_PASS(CounterOrderExecuteEnum.FUND_TX_ACCT_NO_OPEN_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.FUND_TX_ACCT_NO_OPEN.getCode()),


    /**************************************************等待复核--->驳回至经办执行器********************************************************************/
    SUBS_COUNTER_ORDER_AUDIT_NOT_PASS(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.SUBS.getCode()),

    PURCHASE_COUNTER_ORDER_AUDIT_NOT_PASS(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.PURCHASE.getCode()),

    REDEEM_COUNTER_ORDER_AUDIT_NOT_PASS(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.REDEEM.getCode()),

    _112A_COUNTER_ORDER_AUDIT_NOT_PASS(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum._112A.getCode()),

    _112B_COUNTER_ORDER_AUDIT_NOT_PASS(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum._112B.getCode()),

    SUB_AND_FIRST_PAID_COUNTER_ORDER_AUDIT_NOT_PASS(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),

    REVOKE_COUNTER_ORDER_AUDIT_NOT_PASS(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.REVOKE.getCode()),

    EXTENSION_COUNTER_ORDER_AUDIT_NOT_PASS(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.EXTENSION.getCode()),

    FUND_TRANSFER_COUNTER_ORDER_AUDIT_NOT_PASS(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.FUND_TRANSFER.getCode()),

    FORCE_SUBTRACT_COUNTER_ORDER_AUDIT_NOT_PASS(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.FORCE_SUBTRACT.getCode()),

    FORCE_ADD_COUNTER_ORDER_AUDIT_NOT_PASS(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.FORCE_ADD.getCode()),

    BATCH_PAID_COUNTER_ORDER_AUDIT_NOT_PASS(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.BATCH_PAID.getCode()),

    FULL_BATCH_SUBSCRIBE_COUNTER_ORDER_AUDIT_NOT_PASS(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.FULL_BATCH_SUBSCRIBE.getCode()),

    FULL_BATCH_REDEEM_COUNTER_ORDER_AUDIT_NOT_PASS(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.FULL_BATCH_REDEEM.getCode()),

    FUND_TX_ACCT_NO_OPEN_COUNTER_ORDER_AUDIT_NOT_PASS(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.FUND_TX_ACCT_NO_OPEN.getCode()),



    /**************************************************等待回访--->审核通过 执行器通过执行器********************************************************************/
    REDEEM_COUNTER_ORDER_AUDIT_PASS_WAIT_REVISIT(CounterOrderExecuteEnum.REDEEM_AUDIT_PASS_WAIT_REVISIT_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.REDEEM.getCode()),

    SUBS_COUNTER_ORDER_AUDIT_PASS_WAIT_REVISIT(CounterOrderExecuteEnum.SUBS_REVISIT_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.SUBS.getCode()),

    PURCHASE_COUNTER_ORDER_AUDIT_PASS_WAIT_REVISIT(CounterOrderExecuteEnum.SUBS_REVISIT_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.PURCHASE.getCode()),

    _112A_COUNTER_ORDER_AUDIT_PASS_WAIT_REVISIT(CounterOrderExecuteEnum.SUBS_REVISIT_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum._112A.getCode()),

    _112B_COUNTER_ORDER_AUDIT_PASS_WAIT_REVISIT(CounterOrderExecuteEnum.SUBS_REVISIT_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum._112B.getCode()),

    SUB_AND_FIRST_PAID_COUNTER_ORDER_AUDIT_PASS_WAIT_REVISIT(CounterOrderExecuteEnum.SUBS_REVISIT_AUDIT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),

    REVOKE_COUNTER_ORDER_AUDIT_PASS_WAIT_REVISIT(CounterOrderExecuteEnum.REVOKE_AUDIT_PASS_WAIT_REVISIT_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.REVOKE.getCode()),
    EXTENSION_COUNTER_ORDER_AUDIT_PASS_WAIT_REVISIT(CounterOrderExecuteEnum.EXTENSION_AUDIT_PASS_WAIT_REVISIT_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.EXTENSION.getCode()),

    FUND_TRANSFER_COUNTER_ORDER_AUDIT_PASS_WAIT_REVISIT(CounterOrderExecuteEnum.FUND_TRANSFER_AUDIT_PASS_WAIT_REVISIT_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.FUND_TRANSFER.getCode()),

    /**************************************************等待回访--->驳回至经办 执行器通过执行器********************************************************************/
    REDEEM_AUDIT_NOT_PASS_REJECT_TO_FIRST_EXAMINE(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.REDEEM.getCode()),

    SUBS_AUDIT_NOT_PASS_REJECT_TO_FIRST_EXAMINE(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.SUBS.getCode()),

    PURCHASE_AUDIT_NOT_PASS_REJECT_TO_FIRST_EXAMINE(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.PURCHASE.getCode()),

    _112A_AUDIT_NOT_PASS_REJECT_TO_FIRST_EXAMINE(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum._112A.getCode()),

    _112B_AUDIT_NOT_PASS_REJECT_TO_FIRST_EXAMINE(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum._112B.getCode()),

    SUB_AND_FIRST_PAID_AUDIT_NOT_PASS_REJECT_TO_FIRST_EXAMINE(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),

    REVOKE_AUDIT_NOT_PASS_REJECT_TO_FIRST_EXAMINE(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.REVOKE.getCode()),

    EXTENSION_AUDIT_NOT_PASS_REJECT_TO_FIRST_EXAMINE(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.EXTENSION.getCode()),

    FUND_TRANSFER_AUDIT_NOT_PASS_REJECT_TO_FIRST_EXAMINE(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterBizTypeEnum.FUND_TRANSFER.getCode()),


    /**************************************************驳回至经办-->作废 执行器通过执行器********************************************************************/

    SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_VOIDED(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.SUBS.getCode()),

    PURCHASE_COUNTER_ORDER_AUDIT_NOT_PASS_VOIDED(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.PURCHASE.getCode()),

    REDEEM_COUNTER_ORDER_AUDIT_NOT_PASS_VOIDED(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.REDEEM.getCode()),

    _112A_COUNTER_ORDER_AUDIT_NOT_PASS_VOIDED(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum._112A.getCode()),

    _112B_COUNTER_ORDER_AUDIT_NOT_PASS_VOIDED(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum._112B.getCode()),

    SUB_AND_FIRST_PAID_COUNTER_ORDER_AUDIT_NOT_PASS_VOIDED(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),
    REVOKE_COUNTER_ORDER_AUDIT_NOT_PASS_VOIDED(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.REVOKE.getCode()),

    EXTENSION_COUNTER_ORDER_AUDIT_NOT_PASS_VOIDED(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.EXTENSION.getCode()),

    FUND_TRANSFER_COUNTER_ORDER_AUDIT_NOT_PASS_VOIDED(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.FUND_TRANSFER.getCode()),

    FORCE_SUBTRACT_COUNTER_ORDER_AUDIT_NOT_PASS_VOIDED(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.FORCE_SUBTRACT.getCode()),

    FORCE_ADD_COUNTER_ORDER_AUDIT_NOT_PASS_VOIDED(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.FORCE_ADD.getCode()),

    BATCH_PAID_COUNTER_ORDER_AUDIT_NOT_PASS_VOIDED(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.BATCH_PAID.getCode()),

    FULL_BATCH_SUBSCRIBE_COUNTER_ORDER_AUDIT_NOT_PASS_VOIDED(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.FULL_BATCH_SUBSCRIBE.getCode()),

    FULL_BATCH_REDEEM_COUNTER_ORDER_AUDIT_NOT_PASS_VOIDED(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.FULL_BATCH_REDEEM.getCode()),

    FUND_TX_ACCT_NO_OPEN_COUNTER_ORDER_AUDIT_NOT_PASS_VOIDED(CounterOrderExecuteEnum.COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE, CounterOrderAuditStatusEnum.REJECT_TO_FIRST_EXAMINE.getCode(), CounterOrderAuditStatusEnum.VOIDED.getCode(), CounterBizTypeEnum.FUND_TX_ACCT_NO_OPEN.getCode()),

    ;
    private final CounterOrderExecuteEnum executeEnum;

    private final String oldAuditStatus;

    private final String auditStatus;

    private final String bizType;

    CounterOrderAuditExecuteRelationEnum(CounterOrderExecuteEnum executeEnum, String oldAuditStatus, String auditStatus, String bizType) {
        this.executeEnum = executeEnum;
        this.oldAuditStatus = oldAuditStatus;
        this.auditStatus = auditStatus;
        this.bizType = bizType;
    }

     public static String getExecuteByAuditStatusAndBizType(String oldAuditStatus, String auditStatus, String bizType) {
        for (CounterOrderAuditExecuteRelationEnum counterOrdeBizTypeRuleEnum : CounterOrderAuditExecuteRelationEnum.values()) {
            if (counterOrdeBizTypeRuleEnum.getOldAuditStatus().equals(oldAuditStatus) && counterOrdeBizTypeRuleEnum.getAuditStatus().equals(auditStatus) && counterOrdeBizTypeRuleEnum.getBizType().equals(bizType)) {
                return counterOrdeBizTypeRuleEnum.getExecuteEnum().getCode();
            }
        }
        return null;
    }
    public static String getBizTypeByAuditStatusAndBizType(String auditStatus, String bizType) {
        for (CounterOrderAuditExecuteRelationEnum counterOrdeBizTypeRuleEnum : CounterOrderAuditExecuteRelationEnum.values()) {
            if (StringUtils.isNotBlank(counterOrdeBizTypeRuleEnum.getAuditStatus()) && counterOrdeBizTypeRuleEnum.getAuditStatus().equals(auditStatus) && counterOrdeBizTypeRuleEnum.getBizType().equals(bizType)) {
                return counterOrdeBizTypeRuleEnum.getExecuteEnum().getCode();
            }
        }
        return null;
    }

    public CounterOrderExecuteEnum getExecuteEnum() {
        return executeEnum;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public String getBizType() {
        return bizType;
    }

    public String getOldAuditStatus() {
        return oldAuditStatus;
    }
}
