
body {
    min-height: 100%; 
    width: 1024px;
    margin: 0 auto;
    padding: 20px 25px;
    background-color: #FFFFFF;
    display: flex;
    flex-direction: column;
}

/* 页头 */
.app-header {
    position: absolute;
    right: 20px;
    height: 36px;
    display: flex;
    align-items: center; /* 垂直对齐图片 */
}
.app-header img {
    width: auto;
    height: 36px;
}

/* 页脚 */
.app-footer {
    flex: 0 0 54px;
    line-height: 18px;
    font-size: 12px;
    color: #000;
}
.app-footer div p {
    display: inline-block;
}


/* 内容 */
.app-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding-bottom: 50px;
}

/* 标题 */
.app-body__title1 {
    color: #000;
    font-weight: bold;
    text-align: center;
}
.app-body__title1 h3{
    font-size: 20px;
    line-height: 28px;
}
.app-body__title1 p{
    font-size: 15px;
    line-height: 22px;
}
.app-body__title2, .app-body__title3, .app-body__title4 {
    color: #000;
    font-weight: bold;
    font-size: 15px;
    line-height: 22px;
    margin-top: 10px;
}
.app-body__title2 {
    text-align: center;
}
.app-body__title3 {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}
.app-body__title4 {
    display: flex;
    flex-direction: row;
    text-align: left;
    padding: 0 8px;
}
.app-body__title3 p, .app-body__title4 p {
    padding-left: 6px;
}

/* 用户信息相关 */
.app-body__item1, .app-body__item2 {
    color: #000;
    font-weight: bold;
    font-size: 15px;
    line-height: 22px;
}
.app-body__item1 {
    text-align: left;
    width: 400px;
    white-space: normal;
    overflow-wrap: break-word;
}
.app-body__item2 {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}
.body__item2 > div {
    flex: 1;
}
.app-body__item2-left > div  {
    text-align: left;
    width: 400px;
    white-space: normal;
    overflow-wrap: break-word;
}
.app-body__item2-right > div  {
    text-align: right;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

/* 内容文字信息 */
.app-body__content, .app-body__content2 {
    font-size: 14px;
    color: #000000;
    line-height: 24px;
    font-style: normal;
    margin-top: 15px;
}
.app-body__content {
    text-align: left;
}
.app-body__content2 {
    text-align: center;
}
.app-body__explain {
    font-size: 12px;
}
.app-body__content3 .app-body__content4 {
    font-size: 14px;
    color: #000000;
    line-height: 24px;
    font-style: normal;
    margin-top: 15px;
}
.app-body__content3 {
    text-align: left;
}
.app-body__content4 {
    text-align: center;
}

/* 表格 */
.app-body__table {
    font-size: 12px;
    color: #000;
    text-align: center;
    line-height: 27px;
    margin: 5px 0;
}
.app-body__table thead {
    border-top: 2px solid #000;
}
.app-body__table thead th {
    font-weight: normal;
}
.app-body__table tr {
    border-bottom: 1px solid #000;
}
/* .app-body__table tbody tr {} */
.app-body__table tfoot tr {
    border-bottom: 0;
    font-weight: bold;
}

.b-t-1 {
    border-top: 1px solid #000;
}
.w-50 {
    width: 50%;
}
.text-right {
    text-align: right;
}
.text-left {
    text-align: left;
}

.text-left-w-220 {
    text-align: left;
    width: 220px;
    white-space: normal;
    overflow-wrap: break-word;
}

.text-left-w-450 {
    text-align: left;
    width: 450px;
    white-space: normal;
    overflow-wrap: break-word;
}

/* 自定义间距 */
.p-l-5 {
    padding-left: 5px;
}
.p-l-30 {
    padding-left: 30px;
}
.m-t-10 {
    margin-top: 10px;
}
.m-t-20 {
    margin-top: 20px;
}
.m-t-30 {
    margin-top: 30px;
}

.page_no_break {
    break-inside: avoid;
    page-break-inside: avoid;
}

.page_break {
    page-break-after: always;
}