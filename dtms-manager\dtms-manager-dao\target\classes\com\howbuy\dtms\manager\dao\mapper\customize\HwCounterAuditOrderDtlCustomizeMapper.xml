<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.customize.HwCounterAuditOrderDtlCustomizeMapper">

    <select id="queryCounterOrderDtlByAppSerialNo"
            resultType="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderDtlPO">
        select
        <include refid="com.howbuy.dtms.manager.dao.mapper.HwCounterAuditOrderDtlMapper.Base_Column_List" />
        from hw_counter_audit_order_dtl
        where app_serial_no = #{appSerialNo,jdbcType=VARCHAR}
        and rec_stat = '0'
        order by id
    </select>
    <select id="queryAuditIngCounterOrderByPrebookDealNo"
            resultType="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderDtlPO">
        select b.app_serial_no,
               b.deal_no
        from hw_counter_audit_order a
                 left join hw_counter_audit_order_dtl b on a.app_serial_no = b.app_serial_no
        where b.prebook_deal_no = #{prebookDealNo,jdbcType=VARCHAR}
          and a.audit_status in ('2', '8')
          and b.rec_stat = '0'
    </select>

    <select id="queryAuditIngCounterOrderByTradeOrder"
            resultType="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderDtlPO">
        select
        b.app_serial_no,
        b.deal_no
        from hw_counter_audit_order a left join hw_counter_audit_order_dtl b on a.app_serial_no = b.app_serial_no
        where b.deal_no = #{tradeOrderNo,jdbcType=VARCHAR}
        and a.audit_status in ('2','8')
        and b.rec_stat = '0'
        order by b.create_timestamp desc, b.id desc
        limit 1;

    </select>
    <select id="queryUnComplateAuditorOrderDtlList"
            resultType="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderDtlPO">
        select
        <include refid="com.howbuy.dtms.manager.dao.mapper.HwCounterAuditOrderDtlMapper.Base_Column_List" />
            from hw_counter_audit_order_dtl t1
        where t1.rec_stat = '0'
        and exists (
            select 1
            from hw_counter_audit_order t2
            where t2.rec_stat = '0'
            and t2.app_serial_no = t1.app_serial_no
            <if test="startDt != null and startDt != ''">
                and t2.app_dt <![CDATA[>=]]> #{startDt}
            </if>
            <if test="endDt != null and endDt != ''">
                and t2.app_dt <![CDATA[<=]]> #{endDt}
            </if>
            <if test="auditStatusList != null and auditStatusList.size > 0">
                and t2.audit_status in
                <foreach collection="auditStatusList" item="item" index="index" open="(" close=")" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="fundCodeList != null and fundCodeList.size > 0">
                and t2.fund_code in
                <foreach collection="fundCodeList" item="item" index="index" open="(" close=")" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            and t2.counter_biz_type != 'fundTxAcctNoOpen'
        )
    </select>
</mapper>