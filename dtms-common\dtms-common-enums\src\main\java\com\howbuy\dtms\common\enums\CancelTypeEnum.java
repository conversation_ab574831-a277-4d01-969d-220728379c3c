/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

/**
 * <AUTHOR>
 * @description: 撤单类型 1-自行撤销；2-强制取消
 * @date 2024/4/30 13:08
 * @since JDK 1.8
 */
public enum CancelTypeEnum {

    /**
     * 自行撤销
     */
    SELF_CANCEL("1", "自行撤销"),
    /**
     * 强制取消
     */
    FORCE_CANCEL("2", "强制取消"),
    ;

    /**
     * 枚举code
     */
    private String code;
    /**
     * 枚举的中文意义
     */
    private String desc;

    CancelTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static CancelTypeEnum getByCode(String code) {
        CancelTypeEnum[] cancelTypeEnums = values();

        for (int i = 0; i < cancelTypeEnums.length; ++i) {
            CancelTypeEnum cancelTypeEnum = cancelTypeEnums[i];
            if (cancelTypeEnum.getCode().equals(code)) {
                return cancelTypeEnum;
            }
        }

        return null;
    }

}
