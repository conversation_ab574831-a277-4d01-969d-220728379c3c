<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.BpSupplementalAgreementConfigMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.BpSupplementalAgreementConfigPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="agrId" column="agr_id" jdbcType="BIGINT"/>
            <result property="fundCode" column="fund_code" jdbcType="VARCHAR"/>
            <result property="fundAbbr" column="fund_abbr" jdbcType="VARCHAR"/>
            <result property="fundManCode" column="fund_man_code" jdbcType="VARCHAR"/>
            <result property="agreementName" column="agreement_name" jdbcType="VARCHAR"/>
            <result property="agreementDescription" column="agreement_description" jdbcType="VARCHAR"/>
            <result property="agreementUrl" column="agreement_url" jdbcType="VARCHAR"/>
            <result property="agreementSignEndDt" column="agreement_sign_end_dt" jdbcType="VARCHAR"/>
            <result property="version" column="version" jdbcType="BIGINT"/>
            <result property="recStat" column="rec_stat" jdbcType="CHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,agr_id,fund_code,
        fund_abbr,fund_man_code,agreement_name,
        agreement_description,agreement_url,agreement_sign_end_dt,
        version,rec_stat,creator,
        create_time,modifier,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from bp_supplemental_agreement_config
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from bp_supplemental_agreement_config
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.BpSupplementalAgreementConfigPO" useGeneratedKeys="true">
        insert into bp_supplemental_agreement_config
        ( id,agr_id,fund_code
        ,fund_abbr,fund_man_code,agreement_name
        ,agreement_description,agreement_url,agreement_sign_end_dt
        ,version,rec_stat,creator
        ,create_time,modifier,update_time
        )
        values (#{id,jdbcType=BIGINT},#{agrId,jdbcType=BIGINT},#{fundCode,jdbcType=VARCHAR}
        ,#{fundAbbr,jdbcType=VARCHAR},#{fundManCode,jdbcType=VARCHAR},#{agreementName,jdbcType=VARCHAR}
        ,#{agreementDescription,jdbcType=VARCHAR},#{agreementUrl,jdbcType=VARCHAR},#{agreementSignEndDt,jdbcType=VARCHAR}
        ,#{version,jdbcType=BIGINT},#{recStat,jdbcType=CHAR},#{creator,jdbcType=VARCHAR}
        ,#{createTime,jdbcType=TIMESTAMP},#{modifier,jdbcType=VARCHAR},#{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.BpSupplementalAgreementConfigPO" useGeneratedKeys="true">
        insert into bp_supplemental_agreement_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="agrId != null">agr_id,</if>
                <if test="fundCode != null">fund_code,</if>
                <if test="fundAbbr != null">fund_abbr,</if>
                <if test="fundManCode != null">fund_man_code,</if>
                <if test="agreementName != null">agreement_name,</if>
                <if test="agreementDescription != null">agreement_description,</if>
                <if test="agreementUrl != null">agreement_url,</if>
                <if test="agreementSignEndDt != null">agreement_sign_end_dt,</if>
                <if test="version != null">version,</if>
                <if test="recStat != null">rec_stat,</if>
                <if test="creator != null">creator,</if>
                <if test="createTime != null">create_time,</if>
                <if test="modifier != null">modifier,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="agrId != null">#{agrId,jdbcType=BIGINT},</if>
                <if test="fundCode != null">#{fundCode,jdbcType=VARCHAR},</if>
                <if test="fundAbbr != null">#{fundAbbr,jdbcType=VARCHAR},</if>
                <if test="fundManCode != null">#{fundManCode,jdbcType=VARCHAR},</if>
                <if test="agreementName != null">#{agreementName,jdbcType=VARCHAR},</if>
                <if test="agreementDescription != null">#{agreementDescription,jdbcType=VARCHAR},</if>
                <if test="agreementUrl != null">#{agreementUrl,jdbcType=VARCHAR},</if>
                <if test="agreementSignEndDt != null">#{agreementSignEndDt,jdbcType=VARCHAR},</if>
                <if test="version != null">#{version,jdbcType=BIGINT},</if>
                <if test="recStat != null">#{recStat,jdbcType=CHAR},</if>
                <if test="creator != null">#{creator,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="modifier != null">#{modifier,jdbcType=VARCHAR},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.dtms.manager.dao.po.BpSupplementalAgreementConfigPO">
        update bp_supplemental_agreement_config
        <set>
                <if test="agrId != null">
                    agr_id = #{agrId,jdbcType=BIGINT},
                </if>
                <if test="fundCode != null">
                    fund_code = #{fundCode,jdbcType=VARCHAR},
                </if>
                <if test="fundAbbr != null">
                    fund_abbr = #{fundAbbr,jdbcType=VARCHAR},
                </if>
                <if test="fundManCode != null">
                    fund_man_code = #{fundManCode,jdbcType=VARCHAR},
                </if>
                <if test="agreementName != null">
                    agreement_name = #{agreementName,jdbcType=VARCHAR},
                </if>
                <if test="agreementDescription != null">
                    agreement_description = #{agreementDescription,jdbcType=VARCHAR},
                </if>
                <if test="agreementUrl != null">
                    agreement_url = #{agreementUrl,jdbcType=VARCHAR},
                </if>
                <if test="agreementSignEndDt != null">
                    agreement_sign_end_dt = #{agreementSignEndDt,jdbcType=VARCHAR},
                </if>
                <if test="version != null">
                    version = #{version,jdbcType=BIGINT},
                </if>
                <if test="recStat != null">
                    rec_stat = #{recStat,jdbcType=CHAR},
                </if>
                <if test="creator != null">
                    creator = #{creator,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="modifier != null">
                    modifier = #{modifier,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.howbuy.dtms.manager.dao.po.BpSupplementalAgreementConfigPO">
        update bp_supplemental_agreement_config
        set 
            agr_id =  #{agrId,jdbcType=BIGINT},
            fund_code =  #{fundCode,jdbcType=VARCHAR},
            fund_abbr =  #{fundAbbr,jdbcType=VARCHAR},
            fund_man_code =  #{fundManCode,jdbcType=VARCHAR},
            agreement_name =  #{agreementName,jdbcType=VARCHAR},
            agreement_description =  #{agreementDescription,jdbcType=VARCHAR},
            agreement_url =  #{agreementUrl,jdbcType=VARCHAR},
            agreement_sign_end_dt =  #{agreementSignEndDt,jdbcType=VARCHAR},
            version =  #{version,jdbcType=BIGINT},
            rec_stat =  #{recStat,jdbcType=CHAR},
            creator =  #{creator,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            modifier =  #{modifier,jdbcType=VARCHAR},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
