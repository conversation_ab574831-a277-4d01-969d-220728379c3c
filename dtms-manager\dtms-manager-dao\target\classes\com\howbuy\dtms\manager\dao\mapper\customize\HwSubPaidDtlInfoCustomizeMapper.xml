<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.customize.HwSubPaidDtlInfoCustomizeMapper">
    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwSubPaidDtlInfoPO">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="dtl_serial_no" jdbcType="BIGINT" property="dtlSerialNo" />
        <result column="serial_no" jdbcType="BIGINT" property="serialNo" />
        <result column="deal_no" jdbcType="BIGINT" property="dealNo" />
        <result column="hk_cust_no" jdbcType="VARCHAR" property="hkCustNo" />
        <result column="fund_tx_acct_no" jdbcType="VARCHAR" property="fundTxAcctNo" />
        <result column="fund_code" jdbcType="VARCHAR" property="fundCode" />
        <result column="middle_busi_code" jdbcType="VARCHAR" property="middleBusiCode" />
        <result column="sub_amt" jdbcType="DECIMAL" property="subAmt" />
        <result column="paid_amt" jdbcType="DECIMAL" property="paidAmt" />
        <result column="rec_stat" jdbcType="VARCHAR" property="recStat" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="create_timestamp" jdbcType="TIMESTAMP" property="createTimestamp" />
        <result column="update_timestamp" jdbcType="TIMESTAMP" property="updateTimestamp" />
    </resultMap>

    <sql id="Base_Column_List">
    id, dtl_serial_no, serial_no, hk_cust_no,fund_tx_acct_no, fund_code,middle_busi_code, deal_no,
    sub_amt, paid_amt, rec_stat, version, create_timestamp, update_timestamp
    </sql>

    <sql id="Query_Where_Clause">
        <where>
            <if test="hkCustNo != null and hkCustNo != ''">
        AND hk_cust_no = #{hkCustNo,jdbcType=VARCHAR}
            </if>
            <if test="fundCodeList != null and fundCodeList.size() > 0">
        AND fund_code IN
                <foreach collection="fundCodeList" item="fundCode" open="(" separator="," close=")">
          #{fundCode,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="dealNo != null and dealNo != ''">
        AND deal_no = #{dealNo,jdbcType=BIGINT}
            </if>
            <if test="serialNo != null and serialNo != ''">
        AND serial_no = #{serialNo,jdbcType=BIGINT}
            </if>
            <if test="middleBusiCodeList != null and middleBusiCodeList.size() > 0">
        AND middle_busi_code IN
                <foreach collection="middleBusiCodeList" item="middleBusiCode" open="(" separator="," close=")">
          #{middleBusiCode,jdbcType=VARCHAR}
                </foreach>
            </if>
      AND rec_stat = '1'
        </where>
    </sql>

    <!-- 分页查询认缴明细 -->
    <select id="queryPaidDetailListByPage" parameterType="com.howbuy.dtms.manager.dao.query.HwSubPaidDtlInfoQuery" resultMap="BaseResultMap">
    SELECT 
        <include refid="Base_Column_List" />
    FROM hw_sub_paid_dtl_info
        <include refid="Query_Where_Clause" />
    ORDER BY fund_code asc,hk_cust_no asc
    </select>

    <!-- 导出认缴明细列表 -->
    <select id="exportPaidDetailList" parameterType="com.howbuy.dtms.manager.dao.query.HwSubPaidDtlInfoQuery" resultMap="BaseResultMap">
    SELECT 
        <include refid="Base_Column_List" />
    FROM hw_sub_paid_dtl_info
        <include refid="Query_Where_Clause" />
        ORDER BY fund_code asc,hk_cust_no asc
    </select>
    <select id="querySubPaidDtlTotalAmount" resultType="java.util.Map">
        SELECT
            sum(sub_amt) as totalSubAmt,
            sum(paid_amt) as totalPaidAmt
        FROM hw_sub_paid_dtl_info
        <include refid="Query_Where_Clause" />
    </select>
</mapper> 