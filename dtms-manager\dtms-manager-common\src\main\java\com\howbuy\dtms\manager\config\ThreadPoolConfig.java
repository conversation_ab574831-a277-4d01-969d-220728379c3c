package com.howbuy.dtms.manager.config;

import com.howbuy.dtms.manager.constant.Constants;
import com.howbuy.trace.thread.ThreadTraceHelper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class ThreadPoolConfig {
    
    @Bean("statementTaskExecutor")
    public Executor statementTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(30); // 核心线程数
        executor.setMaxPoolSize(30); // 最大线程数
        executor.setThreadNamePrefix("StatementExecutor-"); // 线程名前缀
        executor.setQueueCapacity(500);
        executor.setKeepAliveSeconds(10);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @Bean(name = Constants.BATCH_TRADE_POOL)
    public ThreadPoolTaskExecutor batchTradePoolExecutor() {
        int cpuCoreNum = Runtime.getRuntime().availableProcessors();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(cpuCoreNum + 1);
        executor.setMaxPoolSize(2 * cpuCoreNum);
        executor.setQueueCapacity(500);
        executor.setKeepAliveSeconds(10);
        executor.setThreadNamePrefix(Constants.BATCH_TRADE_POOL);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(ThreadTraceHelper::decorate);
        executor.initialize();
        return executor;
    }
}