/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

/**
 * <AUTHOR>
 * @description: 支付状态 0-无需支付；1-未支付；2-支付中；3-部分成功；4-成功；5-失败；6-退款
 * @date 2024/4/18 14:24
 * @since JDK 1.8
 */
public enum PayStatusEnum {

    NO_NEED_PAY("0", "无需支付"),
    UN_PAY("1", "未支付"),
    PAYING("2", "支付中"),
    PAY_PART_SUCCESS("3", "支付部分成功"),
    PAY_SUCCESS("4", "成功"),
    PAY_FAIL("5", "失败"),
    REFUNDED("6", "已退款");

    private String code;
    private String desc;

    PayStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static PayStatusEnum getEnumByCode(String code) {
        for (PayStatusEnum payStatusEnum : PayStatusEnum.values()) {
            if (payStatusEnum.getCode().equals(code)) {
                return payStatusEnum;
            }
        }
        return null;
    }

    /**
     * 根据code获取描述
     * @param code 枚举code
     * @return 描述
     */
    public static String getDescByCode(String code) {
        PayStatusEnum payStatusEnum = getEnumByCode(code);
        return payStatusEnum != null ? payStatusEnum.getDesc() : null;
    }

}
