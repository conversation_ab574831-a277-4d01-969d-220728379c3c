<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.customize.ParamBusiAppContentCustomizeMapper">


    <select id="queryBusiContentByAppId" resultMap="com.howbuy.dtms.manager.dao.mapper.ParamBusiAppContentMapper.BaseResultMap">
        select
            <include refid="com.howbuy.dtms.manager.dao.mapper.ParamBusiAppContentMapper.Base_Column_List" />
            from param_busi_app_content where app_id = #{appId,jdbcType=BIGINT}
    </select>
</mapper>
