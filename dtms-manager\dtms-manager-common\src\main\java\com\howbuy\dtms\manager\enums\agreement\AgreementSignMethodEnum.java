package com.howbuy.dtms.manager.enums.agreement;

public enum AgreementSignMethodEnum {

    // 0-线上补签、1-线下补签
    ONLINE_SIGN("0", "线上补签"),
    OFFLINE_SIGN("1", "线下补签");
    private final String code;
    private final String desc;
    AgreementSignMethodEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    public static String getDescByCode(String code) {
        for (AgreementSignMethodEnum agreementSignMethodEnum : AgreementSignMethodEnum.values()) {
            if (agreementSignMethodEnum.getCode().equals(code)) {
                return agreementSignMethodEnum.getDesc();
            }
        }
        return null;
    }
}
