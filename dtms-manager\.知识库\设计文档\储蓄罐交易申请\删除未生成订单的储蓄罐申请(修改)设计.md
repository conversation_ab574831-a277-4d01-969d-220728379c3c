## 删除未生成订单的储蓄罐申请

- 请求地址

|                                 |      |                                       |                            |
| ------------------------------- | ---- | ------------------------------------- | -------------------------- |
| ImportHwPiggyTradeAppController | http | /counter/importHwPiggyTradeApp/delete | 删除未生成订单的储蓄罐申请 |

- 修改逻辑(ImportHwPiggyTradeAppService#delete())：
  - ImportHwPiggyTradeAppController修改接口url为/counter/importHwPiggyTradeApp/batchGenerateFail，方法名为batchGenerateFail，描述为批量生成失败
  - ImportHwPiggyTradeAppService修改方法名为batchGenerateFail
  - 遍历查询到的HwPiggyTradeAppImportPO列表（原逻辑不变）
    - 增加判断是否已生成交易订单hw_deal_order检查
      - 根据HwPiggyTradeAppImportPO列表获取importAppId列表
      - 根据importAppId列表查询交易订单hw_deal_order，查询条件external_deal_no为importAppId列表
      - 查询结果不为空，则打印异常信息，抛出异常信息：importAppId已生成交易订单，不能修改为生成失败
    - 新增乐观锁修改生成状态为失败方法 替换原有的修改方法
      - 增加乐观锁条件：生成状态为未生成方法
      - UPDATE影响行数不为1时，抛出异常