package com.howbuy.dtms.manager.counter.service.payment;

import com.howbuy.dtms.common.enums.PaymentOrderTypeEnum;
import com.howbuy.dtms.manager.common.FileExportVO;
import com.howbuy.dtms.manager.counter.request.paymentcheck.PaymentCheckRequest;
import com.howbuy.dtms.manager.counter.request.paymentcheck.QueryPaymentCheckListRequest;
import com.howbuy.dtms.manager.counter.request.paymentcheck.ResetTxPmtFlagRequest;
import com.howbuy.dtms.manager.counter.vo.payment.PaymentCheckResultExportVO;
import com.howbuy.dtms.manager.counter.vo.payment.PaymentCheckResultListVO;
import com.howbuy.dtms.manager.counter.vo.payment.PaymentCheckResultVO;
import com.howbuy.dtms.manager.excel.EasyExcelUtil;
import com.howbuy.dtms.manager.outservice.dtmsorder.DtmsPaymentCheckOuterService;
import com.howbuy.dtms.manager.outservice.dtmsorder.dto.QueryPaymentCheckResultDTO;
import com.howbuy.dtms.manager.utils.AssertUtil;
import com.howbuy.dtms.manager.utils.DateUtils;
import com.howbuy.dtms.order.client.domain.response.payment.QueryPaymentCheckResultResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 交易支付对账服务
 *
 * <AUTHOR>
 * @date 2025-07-23 15:11:25
 * @since JDK 1.8
 */
@Slf4j
@Service
public class TradePaymentCheckService {

    @Resource
    private DtmsPaymentCheckOuterService dtmsPaymentCheckOuterService;

    /**
     * 执行支付对账
     *
     * @param request 支付对账请求
     * <AUTHOR>
     * @date 2025-07-23 15:43:19
     */
    public void check(PaymentCheckRequest request) {
        log.info("执行支付对账开始，请求参数：{}", request);

        // 参数校验
        AssertUtil.notNull(request.getPmtCheckDt(), "支付对账日期不能为空");

        // 调用外部服务执行支付对账
        dtmsPaymentCheckOuterService.executePaymentCheck(request.getPmtCheckDt());

        log.info("执行支付对账成功，支付对账日期：{}", request.getPmtCheckDt());
    }

    /**
     * 查询支付对账列表
     *
     * @param request 查询请求
     * @return 支付对账列表
     * <AUTHOR>
     * @date 2025-07-23 15:11:25
     */
    public PaymentCheckResultListVO queryList(QueryPaymentCheckListRequest request) {
        log.info("查询支付对账列表开始，请求参数：{}", request);

        // 参数校验
        AssertUtil.notNull(request.getPmtCheckDt(), "支付对账日期不能为空");

        // 构建查询DTO
        QueryPaymentCheckResultDTO queryDTO = buildQueryDTO(request);

        // 调用外部服务查询支付对账结果
        QueryPaymentCheckResultResponse response = dtmsPaymentCheckOuterService.queryPaymentCheckResult(queryDTO);

        // 转换响应结果
        PaymentCheckResultListVO result = convertToPaymentCheckListVO(response, request);

        log.info("查询支付对账列表成功，支付对账日期：{}，总记录数：{}", request.getPmtCheckDt(), result.getTotal());
        return result;
    }

    /**
     * 重置交易支付标识
     *
     * @param request 重置交易支付标识请求
     * <AUTHOR>
     * @date 2025-07-23 16:08:57
     */
    public void resetTxPmtFlag(ResetTxPmtFlagRequest request) {
        log.info("重置交易支付标识开始，请求参数：{}", request);

        // 参数校验
        AssertUtil.notNull(request.getPmtDealNo(), "支付订单号不能为空");

        // 调用外部服务重置交易支付标识
        dtmsPaymentCheckOuterService.resetTxPmtFlag(request.getPmtDealNo());

        log.info("重置交易支付标识成功，支付订单号：{}", request.getPmtDealNo());
    }

    /**
     * 下载支付对账查询结果
     *
     * @param request 查询请求
     * @return 文件导出VO
     * @throws IOException IO异常
     * <AUTHOR>
     * @date 2025-07-23 16:24:39
     */
    public FileExportVO download(QueryPaymentCheckListRequest request) throws IOException {
        log.info("下载支付对账查询结果开始，请求参数：{}", request);

        // 设置分页参数为获取全部数据
        request.setPageNum(1);
        request.setPageSize(Integer.MAX_VALUE);

        // 查询支付对账列表
        PaymentCheckResultListVO listVO = this.queryList(request);

        FileExportVO fileExportVO = new FileExportVO();
        if (listVO != null && !CollectionUtils.isEmpty(listVO.getList())) {
            // 构建导出VO列表
            List<PaymentCheckResultExportVO> exportVOList = buildExportVO(listVO);

            // 生成文件名
            String fileName = "支付对账查询结果" + DateUtils.getNowDt();

            // 生成Excel文件字节流
            fileExportVO.setFileBytes(EasyExcelUtil.generateFileByteStr(PaymentCheckResultExportVO.class, fileName, exportVOList));
        }

        log.info("下载支付对账查询结果成功，支付对账日期：{}，导出记录数：{}",
                request.getPmtCheckDt(), listVO != null ? listVO.getTotal() : 0);
        return fileExportVO;
    }

    /**
     * 构建导出VO列表
     *
     * @param listVO 支付对账结果列表VO
     * @return 导出VO列表
     * <AUTHOR>
     * @date 2025-07-23 16:24:39
     */
    private List<PaymentCheckResultExportVO> buildExportVO(PaymentCheckResultListVO listVO) {
        List<PaymentCheckResultExportVO> exportVOList = new ArrayList<>();

        if (listVO != null && !CollectionUtils.isEmpty(listVO.getList())) {
            for (int i = 0; i < listVO.getList().size(); i++) {
                PaymentCheckResultVO resultVO = listVO.getList().get(i);
                PaymentCheckResultExportVO exportVO = PaymentCheckResultExportVO.builder()
                        .pmtDealNo(resultVO.getPmtDealNo())
                        .dealNo(resultVO.getDealNo())
                        .middleBusiCode(resultVO.getMiddleBusiCode())
                        .paymentTypeList(resultVO.getPaymentTypeList())
                        .hkCustNo(resultVO.getHkCustNo())
                        .custName(resultVO.getCustName())
                        .cpAcctNo(resultVO.getCpAcctNo())
                        .fundTxAcctNo(resultVO.getFundTxAcctNo())
                        .fundCode(resultVO.getFundCode())
                        .fundAddr(resultVO.getFundAddr())
                        .currency(resultVO.getCurrency())
                        .appDtm(resultVO.getAppDtm())
                        .pmtAmt(resultVO.getPmtAmt())
                        .pmtCheckDt(resultVO.getPmtCheckDt())
                        .outPmtDealNo(resultVO.getOutPmtDealNo())
                        .outPmtAmt(resultVO.getOutPmtAmt())
                        .outCurrency(resultVO.getOutCurrency())
                        .outPmtFlag(resultVO.getOutPmtFlag())
                        .txPmtFlag(resultVO.getTxPmtFlag())
                        .pmtCompFlag(resultVO.getPmtCompFlag())
                        .memo(resultVO.getMemo())
                        .build();

                // 设置序号
                exportVO.setSeq((long) (i + 1));
                exportVOList.add(exportVO);
            }
        }

        return exportVOList;
    }

    /**
     * 构建查询DTO
     *
     * @param request 查询请求
     * @return 查询DTO
     */
    private QueryPaymentCheckResultDTO buildQueryDTO(QueryPaymentCheckListRequest request) {
        QueryPaymentCheckResultDTO queryDTO = new QueryPaymentCheckResultDTO();
        queryDTO.setPmtDealNo(request.getPmtDealNo());
        queryDTO.setDealNo(request.getDealNo());
        queryDTO.setOutPmtDealNo(request.getOutPmtDealNo());
        queryDTO.setFundCodes(request.getFundCodes());
        queryDTO.setPmtCheckDt(request.getPmtCheckDt());
        queryDTO.setPmtCompFlags(request.getPmtCompFlags());
        queryDTO.setOrderType(PaymentOrderTypeEnum.TRADE.getCode());
        queryDTO.setPageNo(request.getPageNum());
        queryDTO.setPageSize(request.getPageSize());
        return queryDTO;
    }

    /**
     * 转换响应结果为VO
     *
     * @param response 外部服务响应
     * @param request  原始请求
     * @return 支付对账列表VO
     */
    private PaymentCheckResultListVO convertToPaymentCheckListVO(QueryPaymentCheckResultResponse response, QueryPaymentCheckListRequest request) {
        PaymentCheckResultListVO result = new PaymentCheckResultListVO();

        // 设置分页信息
        result.setTotal(response.getTotal());
        result.setPages(response.getPages());

        // 转换明细列表
        List<PaymentCheckResultVO> resultVOList = new ArrayList<>();
        if (response.getList() != null) {
            response.getList().forEach(item -> {
                PaymentCheckResultVO resultVO = convertToPaymentCheckDetailVO(item);
                resultVOList.add(resultVO);
            });
        }
        result.setList(resultVOList);

        return result;
    }

    /**
     * @param responseResultVo
     * @return com.howbuy.dtms.manager.counter.vo.payment.PaymentCheckResultVO
     * @description:(转换支付对账结果)
     * <AUTHOR>
     * @date 2025/7/23 15:26
     * @since JDK 1.8
     */
    private PaymentCheckResultVO convertToPaymentCheckDetailVO(QueryPaymentCheckResultResponse.PaymentCheckResultVO responseResultVo) {
        PaymentCheckResultVO paymentCheckResultVO = new PaymentCheckResultVO();
        paymentCheckResultVO.setPmtDealNo(String.valueOf(responseResultVo.getPmtDealNo()));
        paymentCheckResultVO.setDealNo(String.valueOf(responseResultVo.getDealNo()));
        paymentCheckResultVO.setMiddleBusiCode(responseResultVo.getMiddleBusiCode());
        paymentCheckResultVO.setPaymentTypeList(responseResultVo.getPaymentTypeList());
        paymentCheckResultVO.setHkCustNo(responseResultVo.getHkCustNo());
        paymentCheckResultVO.setCustName(responseResultVo.getCustName());
        paymentCheckResultVO.setCpAcctNo(responseResultVo.getCpAcctNo());
        paymentCheckResultVO.setFundTxAcctNo(responseResultVo.getFundTxAcctNo());
        paymentCheckResultVO.setFundCode(responseResultVo.getFundCode());
        paymentCheckResultVO.setFundAddr(responseResultVo.getFundAddr());
        paymentCheckResultVO.setCurrency(responseResultVo.getCurrency());
        paymentCheckResultVO.setAppDtm(responseResultVo.getAppDtm());
        paymentCheckResultVO.setPmtAmt(responseResultVo.getPmtAmt());
        paymentCheckResultVO.setPmtCheckDt(responseResultVo.getPmtCheckDt());
        paymentCheckResultVO.setOutPmtDealNo(responseResultVo.getOutPmtDealNo());
        paymentCheckResultVO.setOutPmtAmt(responseResultVo.getOutPmtAmt());
        paymentCheckResultVO.setOutCurrency(responseResultVo.getOutCurrency());
        paymentCheckResultVO.setOutPmtFlag(responseResultVo.getOutPmtFlag());
        paymentCheckResultVO.setTxPmtFlag(responseResultVo.getTxPmtFlag());
        paymentCheckResultVO.setPmtCompFlag(responseResultVo.getPmtCompFlag());
        paymentCheckResultVO.setMemo(responseResultVo.getMemo());
        return paymentCheckResultVO;
    }
}
