<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.ParamBusiAppContentMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.ParamBusiAppContentPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="appId" column="app_id" jdbcType="BIGINT"/>
            <result property="oldContent" column="old_content" jdbcType="VARCHAR"/>
            <result property="newContent" column="new_content" jdbcType="VARCHAR"/>
            <result property="compareResult" column="compare_result" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,app_id,old_content,
        new_content,compare_result
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from param_busi_app_content
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from param_busi_app_content
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.ParamBusiAppContentPO" useGeneratedKeys="true">
        insert into param_busi_app_content
        ( id,app_id,old_content
        ,new_content,compare_result)
        values (#{id,jdbcType=BIGINT},#{appId,jdbcType=BIGINT},#{oldContent,jdbcType=VARCHAR}
        ,#{newContent,jdbcType=VARCHAR},#{compareResult,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.ParamBusiAppContentPO" useGeneratedKeys="true">
        insert into param_busi_app_content
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="appId != null">app_id,</if>
                <if test="oldContent != null">old_content,</if>
                <if test="newContent != null">new_content,</if>
                <if test="compareResult != null">compare_result,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="appId != null">#{appId,jdbcType=BIGINT},</if>
                <if test="oldContent != null">#{oldContent,jdbcType=VARCHAR},</if>
                <if test="newContent != null">#{newContent,jdbcType=VARCHAR},</if>
                <if test="compareResult != null">#{compareResult,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.dtms.manager.dao.po.ParamBusiAppContentPO">
        update param_busi_app_content
        <set>
                <if test="appId != null">
                    app_id = #{appId,jdbcType=BIGINT},
                </if>
                <if test="oldContent != null">
                    old_content = #{oldContent,jdbcType=VARCHAR},
                </if>
                <if test="newContent != null">
                    new_content = #{newContent,jdbcType=VARCHAR},
                </if>
                <if test="compareResult != null">
                    compare_result = #{compareResult,jdbcType=VARCHAR},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.howbuy.dtms.manager.dao.po.ParamBusiAppContentPO">
        update param_busi_app_content
        set 
            app_id =  #{appId,jdbcType=BIGINT},
            old_content =  #{oldContent,jdbcType=VARCHAR},
            new_content =  #{newContent,jdbcType=VARCHAR},
            compare_result =  #{compareResult,jdbcType=VARCHAR}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
