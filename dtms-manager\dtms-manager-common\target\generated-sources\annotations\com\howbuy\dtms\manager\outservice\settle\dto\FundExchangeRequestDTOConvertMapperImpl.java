package com.howbuy.dtms.manager.outservice.settle.dto;

import com.howbuy.dtms.settle.client.facade.trade.fundexchange.FundExchangeRequest;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:23:41+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class FundExchangeRequestDTOConvertMapperImpl implements FundExchangeRequestDTOConvertMapper {

    @Override
    public FundExchangeRequest convert(FundExchangeRequestDTO dto) {
        if ( dto == null ) {
            return null;
        }

        FundExchangeRequest fundExchangeRequest = new FundExchangeRequest();

        fundExchangeRequest.setTraceId( dto.getTraceId() );
        fundExchangeRequest.setAppDt( dto.getAppDt() );
        fundExchangeRequest.setAppStatus( dto.getAppStatus() );
        fundExchangeRequest.setAppTm( dto.getAppTm() );
        fundExchangeRequest.setAppVol( dto.getAppVol() );
        fundExchangeRequest.setCpAcctNo( dto.getCpAcctNo() );
        fundExchangeRequest.setCurrency( dto.getCurrency() );
        fundExchangeRequest.setDealDtlNo( dto.getDealDtlNo() );
        fundExchangeRequest.setFeeRate( dto.getFeeRate() );
        fundExchangeRequest.setFundTxAcctNo( dto.getFundTxAcctNo() );
        fundExchangeRequest.setHkCustNo( dto.getHkCustNo() );
        fundExchangeRequest.setMiddleOrderNo( dto.getMiddleOrderNo() );
        fundExchangeRequest.setOpenDt( dto.getOpenDt() );
        fundExchangeRequest.setPreSubmitTaDt( dto.getPreSubmitTaDt() );
        fundExchangeRequest.setPreSubmitTaTm( dto.getPreSubmitTaTm() );
        fundExchangeRequest.setSerialNumber( dto.getSerialNumber() );
        fundExchangeRequest.setShareRegDt( dto.getShareRegDt() );
        fundExchangeRequest.setSourceFundCode( dto.getSourceFundCode() );
        fundExchangeRequest.setSubmitTaDt( dto.getSubmitTaDt() );
        fundExchangeRequest.setTargetFundCode( dto.getTargetFundCode() );
        fundExchangeRequest.setTargetSerialNumber( dto.getTargetSerialNumber() );

        return fundExchangeRequest;
    }
}
