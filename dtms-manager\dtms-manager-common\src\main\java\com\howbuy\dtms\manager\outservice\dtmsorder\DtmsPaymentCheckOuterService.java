package com.howbuy.dtms.manager.outservice.dtmsorder;

import com.alibaba.fastjson.JSON;
import com.howbuy.dtms.manager.constant.RegistryIdConstant;
import com.howbuy.dtms.manager.enums.ExceptionCodeEnum;
import com.howbuy.dtms.manager.exception.BusinessException;
import com.howbuy.dtms.manager.outservice.dtmsorder.dto.QueryPaymentCheckResultDTO;
import com.howbuy.dtms.order.client.domain.request.payment.PaymentCheckRequest;
import com.howbuy.dtms.order.client.domain.request.payment.QueryPaymentCheckResultRequest;
import com.howbuy.dtms.order.client.domain.request.payment.ResetTxPmtFlagRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.payment.PaymentCheckResponse;
import com.howbuy.dtms.order.client.domain.response.payment.QueryPaymentCheckResultResponse;
import com.howbuy.dtms.order.client.domain.response.payment.ResetTxPmtFlagResponse;
import com.howbuy.dtms.order.client.facade.query.payment.QueryPaymentCheckResultFacade;
import com.howbuy.dtms.order.client.facade.trade.payment.PaymentCheckFacade;
import com.howbuy.dtms.order.client.facade.trade.payment.ResetTxPmtFlagFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * 支付对账外部服务封装
 *
 * <AUTHOR>
 * @description: 封装对DTMS订单系统的支付对账相关接口调用
 * @date 2025/07/23 14:09
 * @since JDK 1.8
 */
@Slf4j
@Component
public class DtmsPaymentCheckOuterService {

    @DubboReference(registry = RegistryIdConstant.DTMS_ORDER_REMOTE, check = false)
    private QueryPaymentCheckResultFacade queryPaymentCheckResultFacade;

    @DubboReference(registry = RegistryIdConstant.DTMS_ORDER_REMOTE, check = false)
    private PaymentCheckFacade paymentCheckFacade;

    @DubboReference(registry = RegistryIdConstant.DTMS_ORDER_REMOTE, check = false)
    private ResetTxPmtFlagFacade resetTxPmtFlagFacade;

    /**
     * 查询支付对账结果
     *
     * @param queryDTO 查询支付对账结果请求参数
     * @return 支付对账结果响应
     * <AUTHOR>
     * @date 2025/07/23 14:09
     */
    public QueryPaymentCheckResultResponse queryPaymentCheckResult(QueryPaymentCheckResultDTO queryDTO) {
        log.info("查询支付对账结果开始，请求参数：{}", JSON.toJSONString(queryDTO));

        QueryPaymentCheckResultRequest request = convertToQueryPaymentCheckResultRequest(queryDTO);

        Response<QueryPaymentCheckResultResponse> response = queryPaymentCheckResultFacade.execute(request);

        if (null == response) {
            throw new BusinessException(ExceptionCodeEnum.DUBBO_INVOKE_ERROR);
        }
        if (!response.isSuccess()) {
            throw new BusinessException(ExceptionCodeEnum.DUBBO_INVOKE_ERROR.getCode(), response.getDescription());
        }

        log.info("查询支付对账结果成功，支付对账日期：{}", queryDTO.getPmtCheckDt());
        return response.getData();
    }

    /**
     * 执行支付对账
     *
     * @param pmtCheckDt 支付对账日期
     * <AUTHOR>
     * @date 2025/07/23 14:09
     */
    public void executePaymentCheck(String pmtCheckDt) {
        PaymentCheckRequest request = new PaymentCheckRequest();
        request.setPmtCheckDt(pmtCheckDt);
        Response<PaymentCheckResponse> response = paymentCheckFacade.execute(request);

        if (null == response) {
            throw new BusinessException(ExceptionCodeEnum.DUBBO_INVOKE_ERROR);
        }
        if (!response.isSuccess()) {
            throw new BusinessException(ExceptionCodeEnum.DUBBO_INVOKE_ERROR.getCode(), response.getDescription());
        }

        log.info("执行支付对账成功，支付对账日期：{}", pmtCheckDt);
    }

    /**
     * 重置交易支付标识
     *
     * @param pmtDealNo 支付订单号
     * <AUTHOR>
     * @date 2025/07/23 14:09
     */
    public void resetTxPmtFlag(String pmtDealNo) {
        log.info("重置交易支付标识开始，支付订单号：{}", pmtDealNo);

        ResetTxPmtFlagRequest request = new ResetTxPmtFlagRequest();
        request.setPmtDealNo(pmtDealNo);

        Response<ResetTxPmtFlagResponse> response = resetTxPmtFlagFacade.execute(request);

        if (null == response) {
            throw new BusinessException(ExceptionCodeEnum.DUBBO_INVOKE_ERROR);
        }
        if (!response.isSuccess()) {
            throw new BusinessException(ExceptionCodeEnum.DUBBO_INVOKE_ERROR.getCode(), response.getDescription());
        }
        log.info("重置交易支付标识成功，支付订单号：{}", pmtDealNo);
    }

    /**
     * @param source
     * @return com.howbuy.dtms.order.client.domain.request.payment.QueryPaymentCheckResultRequest
     * @description:(请在此添加描述)
     * <AUTHOR>
     * @date 2025/7/23 14:52
     * @since JDK 1.8
     */
    private static QueryPaymentCheckResultRequest convertToQueryPaymentCheckResultRequest(QueryPaymentCheckResultDTO source) {
        QueryPaymentCheckResultRequest target = new QueryPaymentCheckResultRequest();
        target.setPmtCheckDt(source.getPmtCheckDt());
        target.setPage(source.getPageNo());
        target.setSize(source.getPageSize());
        target.setDealNo(source.getDealNo());
        target.setPmtDealNo(source.getPmtDealNo());
        target.setOutPmtDealNo(source.getOutPmtDealNo());
        target.setFundCodes(source.getFundCodes());
        target.setPmtCompFlags(source.getPmtCompFlags());
        target.setOrderType(source.getOrderType());
        return target;
    }
}
