<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.HwCounterAuditOrderDtlMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderDtlPO">
    <!--@mbg.generated-->
    <!--@Table hw_counter_audit_order_dtl-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_serial_no" jdbcType="VARCHAR" property="appSerialNo" />
    <result column="app_serial_dtl_no" jdbcType="VARCHAR" property="appSerialDtlNo" />
    <result column="hk_cust_no" jdbcType="VARCHAR" property="hkCustNo" />
    <result column="cust_chinese_name" jdbcType="VARCHAR" property="custChineseName" />
    <result column="id_no_digest" jdbcType="VARCHAR" property="idNoDigest" />
    <result column="id_type" jdbcType="VARCHAR" property="idType" />
    <result column="cp_acct_no" jdbcType="VARCHAR" property="cpAcctNo" />
    <result column="bank_acct_mask" jdbcType="VARCHAR" property="bankAcctMask" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="payment_type" jdbcType="VARCHAR" property="paymentType" />
    <result column="redeem_type" jdbcType="VARCHAR" property="redeemType" />
    <result column="redeem_direction" jdbcType="VARCHAR" property="redeemDirection" />
    <result column="app_amt" jdbcType="DECIMAL" property="appAmt" />
    <result column="net_app_amt" jdbcType="DECIMAL" property="netAppAmt" />
    <result column="app_vol" jdbcType="DECIMAL" property="appVol" />
    <result column="fee" jdbcType="DECIMAL" property="fee" />
    <result column="app_discount" jdbcType="DECIMAL" property="appDiscount" />
    <result column="discount_type" jdbcType="CHAR" property="discountType" />
    <result column="discount_amt" jdbcType="DECIMAL" property="discountAmt" />
    <result column="fee_rate" jdbcType="DECIMAL" property="feeRate" />
    <result column="fee_cal_mode" jdbcType="VARCHAR" property="feeCalMode" />
    <result column="extension_option" jdbcType="VARCHAR" property="extensionOption" />
    <result column="extension_count" jdbcType="VARCHAR" property="extensionCount" />
    <result column="trade_channel" jdbcType="VARCHAR" property="tradeChannel" />
    <result column="app_dt" jdbcType="VARCHAR" property="appDt" />
    <result column="app_tm" jdbcType="VARCHAR" property="appTm" />
    <result column="fund_code" jdbcType="VARCHAR" property="fundCode" />
    <result column="fund_abbr" jdbcType="VARCHAR" property="fundAbbr" />
    <result column="midle_busi_code" jdbcType="VARCHAR" property="midleBusiCode" />
    <result column="fund_tx_acct_no" jdbcType="VARCHAR" property="fundTxAcctNo" />
    <result column="full_tx_acc_code" jdbcType="VARCHAR" property="fullTxAccCode" />
    <result column="fund_tx_acc_type" jdbcType="VARCHAR" property="fundTxAccType" />
    <result column="prebook_deal_no" jdbcType="VARCHAR" property="prebookDealNo" />
    <result column="prebook_discount" jdbcType="DECIMAL" property="prebookDiscount" />
    <result column="estimate_fee" jdbcType="DECIMAL" property="estimateFee" />
    <result column="pre_app_amt" jdbcType="DECIMAL" property="preAppAmt" />
    <result column="outlet_code" jdbcType="VARCHAR" property="outletCode" />
    <result column="crm_file_id" jdbcType="VARCHAR" property="crmFileId" />
    <result column="revoke_type" jdbcType="VARCHAR" property="revokeType" />
    <result column="revoke_reason" jdbcType="VARCHAR" property="revokeReason" />
    <result column="in_hk_cust_no" jdbcType="VARCHAR" property="inHkCustNo" />
    <result column="in_fund_tx_acct_no" jdbcType="VARCHAR" property="inFundTxAcctNo" />
    <result column="in_fund_code" jdbcType="VARCHAR" property="inFundCode" />
    <result column="in_fund_abbr" jdbcType="VARCHAR" property="inFundAbbr" />
    <result column="in_cust_chinese_name" jdbcType="VARCHAR" property="inCustChineseName" />
    <result column="in_id_no_digest" jdbcType="VARCHAR" property="inIdNoDigest" />
    <result column="in_id_type" jdbcType="VARCHAR" property="inIdType" />
    <result column="transfer_vol" jdbcType="DECIMAL" property="transferVol" />
    <result column="transfer_actual_amt" jdbcType="DECIMAL" property="transferActualAmt" />
    <result column="transfer_subs_amt" jdbcType="DECIMAL" property="transferSubsAmt" />
    <result column="transfer_amt" jdbcType="DECIMAL" property="transferAmt" />
    <result column="audit_remark" jdbcType="VARCHAR" property="auditRemark" />
    <result column="deal_no" jdbcType="VARCHAR" property="dealNo" />
    <result column="deal_no_dtl" jdbcType="VARCHAR" property="dealNoDtl" />
    <result column="in_deal_no" jdbcType="VARCHAR" property="inDealNo" />
    <result column="vol_dtl_no" jdbcType="VARCHAR" property="volDtlNo" />
    <result column="pre_submit_ta_dt" jdbcType="VARCHAR" property="preSubmitTaDt" />
    <result column="pre_submit_ta_tm" jdbcType="VARCHAR" property="preSubmitTaTm" />
    <result column="open_dt" jdbcType="VARCHAR" property="openDt" />
    <result column="sub_amt" jdbcType="DECIMAL" property="subAmt" />
    <result column="cum_paid_amt" jdbcType="DECIMAL" property="cumPaidAmt" />
    <result column="force_subtract_rule" jdbcType="CHAR" property="forceSubtractRule" />
    <result column="force_add_rule" jdbcType="CHAR" property="forceAddRule" />
    <result column="serial_number" jdbcType="VARCHAR" property="serialNumber" />
    <result column="share_reg_dt" jdbcType="VARCHAR" property="shareRegDt" />
    <result column="nav_dt" jdbcType="VARCHAR" property="navDt" />
    <result column="nav" jdbcType="DECIMAL" property="nav" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="rec_stat" jdbcType="VARCHAR" property="recStat" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_timestamp" jdbcType="TIMESTAMP" property="createTimestamp" />
    <result column="update_timestamp" jdbcType="TIMESTAMP" property="updateTimestamp" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, app_serial_no, app_serial_dtl_no, hk_cust_no, cust_chinese_name, id_no_digest, 
    id_type, cp_acct_no, bank_acct_mask, bank_name, payment_type, redeem_type, redeem_direction, 
    app_amt, net_app_amt, app_vol, fee, app_discount, discount_type, discount_amt, fee_rate, 
    fee_cal_mode, extension_option, extension_count, trade_channel, app_dt, app_tm, fund_code, 
    fund_abbr, midle_busi_code, fund_tx_acct_no, full_tx_acc_code, fund_tx_acc_type, 
    prebook_deal_no, prebook_discount, estimate_fee, pre_app_amt, outlet_code, crm_file_id, 
    revoke_type, revoke_reason, in_hk_cust_no, in_fund_tx_acct_no, in_fund_code, in_fund_abbr, 
    in_cust_chinese_name, in_id_no_digest, in_id_type, transfer_vol, transfer_actual_amt, 
    transfer_subs_amt, transfer_amt, audit_remark, deal_no, deal_no_dtl, in_deal_no, 
    vol_dtl_no, pre_submit_ta_dt, pre_submit_ta_tm, open_dt, sub_amt, cum_paid_amt, force_subtract_rule, 
    force_add_rule, serial_number, share_reg_dt, nav_dt, nav, remark, rec_stat, creator, 
    modifier, create_timestamp, update_timestamp
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from hw_counter_audit_order_dtl
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from hw_counter_audit_order_dtl
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderDtlPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hw_counter_audit_order_dtl (app_serial_no, app_serial_dtl_no, hk_cust_no, 
      cust_chinese_name, id_no_digest, id_type, 
      cp_acct_no, bank_acct_mask, bank_name, 
      payment_type, redeem_type, redeem_direction, 
      app_amt, net_app_amt, app_vol, 
      fee, app_discount, discount_type, 
      discount_amt, fee_rate, fee_cal_mode, 
      extension_option, extension_count, trade_channel, 
      app_dt, app_tm, fund_code, 
      fund_abbr, midle_busi_code, fund_tx_acct_no, 
      full_tx_acc_code, fund_tx_acc_type, prebook_deal_no, 
      prebook_discount, estimate_fee, pre_app_amt, 
      outlet_code, crm_file_id, revoke_type, 
      revoke_reason, in_hk_cust_no, in_fund_tx_acct_no, 
      in_fund_code, in_fund_abbr, in_cust_chinese_name, 
      in_id_no_digest, in_id_type, transfer_vol, 
      transfer_actual_amt, transfer_subs_amt, transfer_amt, 
      audit_remark, deal_no, deal_no_dtl, 
      in_deal_no, vol_dtl_no, pre_submit_ta_dt, 
      pre_submit_ta_tm, open_dt, sub_amt, 
      cum_paid_amt, force_subtract_rule, force_add_rule, 
      serial_number, share_reg_dt, nav_dt, 
      nav, remark, rec_stat, 
      creator, modifier, create_timestamp, 
      update_timestamp)
    values (#{appSerialNo,jdbcType=VARCHAR}, #{appSerialDtlNo,jdbcType=VARCHAR}, #{hkCustNo,jdbcType=VARCHAR}, 
      #{custChineseName,jdbcType=VARCHAR}, #{idNoDigest,jdbcType=VARCHAR}, #{idType,jdbcType=VARCHAR}, 
      #{cpAcctNo,jdbcType=VARCHAR}, #{bankAcctMask,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR}, 
      #{paymentType,jdbcType=VARCHAR}, #{redeemType,jdbcType=VARCHAR}, #{redeemDirection,jdbcType=VARCHAR}, 
      #{appAmt,jdbcType=DECIMAL}, #{netAppAmt,jdbcType=DECIMAL}, #{appVol,jdbcType=DECIMAL}, 
      #{fee,jdbcType=DECIMAL}, #{appDiscount,jdbcType=DECIMAL}, #{discountType,jdbcType=CHAR}, 
      #{discountAmt,jdbcType=DECIMAL}, #{feeRate,jdbcType=DECIMAL}, #{feeCalMode,jdbcType=VARCHAR}, 
      #{extensionOption,jdbcType=VARCHAR}, #{extensionCount,jdbcType=VARCHAR}, #{tradeChannel,jdbcType=VARCHAR}, 
      #{appDt,jdbcType=VARCHAR}, #{appTm,jdbcType=VARCHAR}, #{fundCode,jdbcType=VARCHAR}, 
      #{fundAbbr,jdbcType=VARCHAR}, #{midleBusiCode,jdbcType=VARCHAR}, #{fundTxAcctNo,jdbcType=VARCHAR}, 
      #{fullTxAccCode,jdbcType=VARCHAR}, #{fundTxAccType,jdbcType=VARCHAR}, #{prebookDealNo,jdbcType=VARCHAR}, 
      #{prebookDiscount,jdbcType=DECIMAL}, #{estimateFee,jdbcType=DECIMAL}, #{preAppAmt,jdbcType=DECIMAL}, 
      #{outletCode,jdbcType=VARCHAR}, #{crmFileId,jdbcType=VARCHAR}, #{revokeType,jdbcType=VARCHAR}, 
      #{revokeReason,jdbcType=VARCHAR}, #{inHkCustNo,jdbcType=VARCHAR}, #{inFundTxAcctNo,jdbcType=VARCHAR}, 
      #{inFundCode,jdbcType=VARCHAR}, #{inFundAbbr,jdbcType=VARCHAR}, #{inCustChineseName,jdbcType=VARCHAR}, 
      #{inIdNoDigest,jdbcType=VARCHAR}, #{inIdType,jdbcType=VARCHAR}, #{transferVol,jdbcType=DECIMAL}, 
      #{transferActualAmt,jdbcType=DECIMAL}, #{transferSubsAmt,jdbcType=DECIMAL}, #{transferAmt,jdbcType=DECIMAL}, 
      #{auditRemark,jdbcType=VARCHAR}, #{dealNo,jdbcType=VARCHAR}, #{dealNoDtl,jdbcType=VARCHAR}, 
      #{inDealNo,jdbcType=VARCHAR}, #{volDtlNo,jdbcType=VARCHAR}, #{preSubmitTaDt,jdbcType=VARCHAR}, 
      #{preSubmitTaTm,jdbcType=VARCHAR}, #{openDt,jdbcType=VARCHAR}, #{subAmt,jdbcType=DECIMAL}, 
      #{cumPaidAmt,jdbcType=DECIMAL}, #{forceSubtractRule,jdbcType=CHAR}, #{forceAddRule,jdbcType=CHAR}, 
      #{serialNumber,jdbcType=VARCHAR}, #{shareRegDt,jdbcType=VARCHAR}, #{navDt,jdbcType=VARCHAR}, 
      #{nav,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, #{recStat,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, #{createTimestamp,jdbcType=TIMESTAMP}, 
      #{updateTimestamp,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderDtlPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hw_counter_audit_order_dtl
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appSerialNo != null">
        app_serial_no,
      </if>
      <if test="appSerialDtlNo != null">
        app_serial_dtl_no,
      </if>
      <if test="hkCustNo != null">
        hk_cust_no,
      </if>
      <if test="custChineseName != null">
        cust_chinese_name,
      </if>
      <if test="idNoDigest != null">
        id_no_digest,
      </if>
      <if test="idType != null">
        id_type,
      </if>
      <if test="cpAcctNo != null">
        cp_acct_no,
      </if>
      <if test="bankAcctMask != null">
        bank_acct_mask,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="paymentType != null">
        payment_type,
      </if>
      <if test="redeemType != null">
        redeem_type,
      </if>
      <if test="redeemDirection != null">
        redeem_direction,
      </if>
      <if test="appAmt != null">
        app_amt,
      </if>
      <if test="netAppAmt != null">
        net_app_amt,
      </if>
      <if test="appVol != null">
        app_vol,
      </if>
      <if test="fee != null">
        fee,
      </if>
      <if test="appDiscount != null">
        app_discount,
      </if>
      <if test="discountType != null">
        discount_type,
      </if>
      <if test="discountAmt != null">
        discount_amt,
      </if>
      <if test="feeRate != null">
        fee_rate,
      </if>
      <if test="feeCalMode != null">
        fee_cal_mode,
      </if>
      <if test="extensionOption != null">
        extension_option,
      </if>
      <if test="extensionCount != null">
        extension_count,
      </if>
      <if test="tradeChannel != null">
        trade_channel,
      </if>
      <if test="appDt != null">
        app_dt,
      </if>
      <if test="appTm != null">
        app_tm,
      </if>
      <if test="fundCode != null">
        fund_code,
      </if>
      <if test="fundAbbr != null">
        fund_abbr,
      </if>
      <if test="midleBusiCode != null">
        midle_busi_code,
      </if>
      <if test="fundTxAcctNo != null">
        fund_tx_acct_no,
      </if>
      <if test="fullTxAccCode != null">
        full_tx_acc_code,
      </if>
      <if test="fundTxAccType != null">
        fund_tx_acc_type,
      </if>
      <if test="prebookDealNo != null">
        prebook_deal_no,
      </if>
      <if test="prebookDiscount != null">
        prebook_discount,
      </if>
      <if test="estimateFee != null">
        estimate_fee,
      </if>
      <if test="preAppAmt != null">
        pre_app_amt,
      </if>
      <if test="outletCode != null">
        outlet_code,
      </if>
      <if test="crmFileId != null">
        crm_file_id,
      </if>
      <if test="revokeType != null">
        revoke_type,
      </if>
      <if test="revokeReason != null">
        revoke_reason,
      </if>
      <if test="inHkCustNo != null">
        in_hk_cust_no,
      </if>
      <if test="inFundTxAcctNo != null">
        in_fund_tx_acct_no,
      </if>
      <if test="inFundCode != null">
        in_fund_code,
      </if>
      <if test="inFundAbbr != null">
        in_fund_abbr,
      </if>
      <if test="inCustChineseName != null">
        in_cust_chinese_name,
      </if>
      <if test="inIdNoDigest != null">
        in_id_no_digest,
      </if>
      <if test="inIdType != null">
        in_id_type,
      </if>
      <if test="transferVol != null">
        transfer_vol,
      </if>
      <if test="transferActualAmt != null">
        transfer_actual_amt,
      </if>
      <if test="transferSubsAmt != null">
        transfer_subs_amt,
      </if>
      <if test="transferAmt != null">
        transfer_amt,
      </if>
      <if test="auditRemark != null">
        audit_remark,
      </if>
      <if test="dealNo != null">
        deal_no,
      </if>
      <if test="dealNoDtl != null">
        deal_no_dtl,
      </if>
      <if test="inDealNo != null">
        in_deal_no,
      </if>
      <if test="volDtlNo != null">
        vol_dtl_no,
      </if>
      <if test="preSubmitTaDt != null">
        pre_submit_ta_dt,
      </if>
      <if test="preSubmitTaTm != null">
        pre_submit_ta_tm,
      </if>
      <if test="openDt != null">
        open_dt,
      </if>
      <if test="subAmt != null">
        sub_amt,
      </if>
      <if test="cumPaidAmt != null">
        cum_paid_amt,
      </if>
      <if test="forceSubtractRule != null">
        force_subtract_rule,
      </if>
      <if test="forceAddRule != null">
        force_add_rule,
      </if>
      <if test="serialNumber != null">
        serial_number,
      </if>
      <if test="shareRegDt != null">
        share_reg_dt,
      </if>
      <if test="navDt != null">
        nav_dt,
      </if>
      <if test="nav != null">
        nav,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="recStat != null">
        rec_stat,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="createTimestamp != null">
        create_timestamp,
      </if>
      <if test="updateTimestamp != null">
        update_timestamp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appSerialNo != null">
        #{appSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="appSerialDtlNo != null">
        #{appSerialDtlNo,jdbcType=VARCHAR},
      </if>
      <if test="hkCustNo != null">
        #{hkCustNo,jdbcType=VARCHAR},
      </if>
      <if test="custChineseName != null">
        #{custChineseName,jdbcType=VARCHAR},
      </if>
      <if test="idNoDigest != null">
        #{idNoDigest,jdbcType=VARCHAR},
      </if>
      <if test="idType != null">
        #{idType,jdbcType=VARCHAR},
      </if>
      <if test="cpAcctNo != null">
        #{cpAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="bankAcctMask != null">
        #{bankAcctMask,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=VARCHAR},
      </if>
      <if test="redeemType != null">
        #{redeemType,jdbcType=VARCHAR},
      </if>
      <if test="redeemDirection != null">
        #{redeemDirection,jdbcType=VARCHAR},
      </if>
      <if test="appAmt != null">
        #{appAmt,jdbcType=DECIMAL},
      </if>
      <if test="netAppAmt != null">
        #{netAppAmt,jdbcType=DECIMAL},
      </if>
      <if test="appVol != null">
        #{appVol,jdbcType=DECIMAL},
      </if>
      <if test="fee != null">
        #{fee,jdbcType=DECIMAL},
      </if>
      <if test="appDiscount != null">
        #{appDiscount,jdbcType=DECIMAL},
      </if>
      <if test="discountType != null">
        #{discountType,jdbcType=CHAR},
      </if>
      <if test="discountAmt != null">
        #{discountAmt,jdbcType=DECIMAL},
      </if>
      <if test="feeRate != null">
        #{feeRate,jdbcType=DECIMAL},
      </if>
      <if test="feeCalMode != null">
        #{feeCalMode,jdbcType=VARCHAR},
      </if>
      <if test="extensionOption != null">
        #{extensionOption,jdbcType=VARCHAR},
      </if>
      <if test="extensionCount != null">
        #{extensionCount,jdbcType=VARCHAR},
      </if>
      <if test="tradeChannel != null">
        #{tradeChannel,jdbcType=VARCHAR},
      </if>
      <if test="appDt != null">
        #{appDt,jdbcType=VARCHAR},
      </if>
      <if test="appTm != null">
        #{appTm,jdbcType=VARCHAR},
      </if>
      <if test="fundCode != null">
        #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="fundAbbr != null">
        #{fundAbbr,jdbcType=VARCHAR},
      </if>
      <if test="midleBusiCode != null">
        #{midleBusiCode,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAcctNo != null">
        #{fundTxAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="fullTxAccCode != null">
        #{fullTxAccCode,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAccType != null">
        #{fundTxAccType,jdbcType=VARCHAR},
      </if>
      <if test="prebookDealNo != null">
        #{prebookDealNo,jdbcType=VARCHAR},
      </if>
      <if test="prebookDiscount != null">
        #{prebookDiscount,jdbcType=DECIMAL},
      </if>
      <if test="estimateFee != null">
        #{estimateFee,jdbcType=DECIMAL},
      </if>
      <if test="preAppAmt != null">
        #{preAppAmt,jdbcType=DECIMAL},
      </if>
      <if test="outletCode != null">
        #{outletCode,jdbcType=VARCHAR},
      </if>
      <if test="crmFileId != null">
        #{crmFileId,jdbcType=VARCHAR},
      </if>
      <if test="revokeType != null">
        #{revokeType,jdbcType=VARCHAR},
      </if>
      <if test="revokeReason != null">
        #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="inHkCustNo != null">
        #{inHkCustNo,jdbcType=VARCHAR},
      </if>
      <if test="inFundTxAcctNo != null">
        #{inFundTxAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="inFundCode != null">
        #{inFundCode,jdbcType=VARCHAR},
      </if>
      <if test="inFundAbbr != null">
        #{inFundAbbr,jdbcType=VARCHAR},
      </if>
      <if test="inCustChineseName != null">
        #{inCustChineseName,jdbcType=VARCHAR},
      </if>
      <if test="inIdNoDigest != null">
        #{inIdNoDigest,jdbcType=VARCHAR},
      </if>
      <if test="inIdType != null">
        #{inIdType,jdbcType=VARCHAR},
      </if>
      <if test="transferVol != null">
        #{transferVol,jdbcType=DECIMAL},
      </if>
      <if test="transferActualAmt != null">
        #{transferActualAmt,jdbcType=DECIMAL},
      </if>
      <if test="transferSubsAmt != null">
        #{transferSubsAmt,jdbcType=DECIMAL},
      </if>
      <if test="transferAmt != null">
        #{transferAmt,jdbcType=DECIMAL},
      </if>
      <if test="auditRemark != null">
        #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="dealNo != null">
        #{dealNo,jdbcType=VARCHAR},
      </if>
      <if test="dealNoDtl != null">
        #{dealNoDtl,jdbcType=VARCHAR},
      </if>
      <if test="inDealNo != null">
        #{inDealNo,jdbcType=VARCHAR},
      </if>
      <if test="volDtlNo != null">
        #{volDtlNo,jdbcType=VARCHAR},
      </if>
      <if test="preSubmitTaDt != null">
        #{preSubmitTaDt,jdbcType=VARCHAR},
      </if>
      <if test="preSubmitTaTm != null">
        #{preSubmitTaTm,jdbcType=VARCHAR},
      </if>
      <if test="openDt != null">
        #{openDt,jdbcType=VARCHAR},
      </if>
      <if test="subAmt != null">
        #{subAmt,jdbcType=DECIMAL},
      </if>
      <if test="cumPaidAmt != null">
        #{cumPaidAmt,jdbcType=DECIMAL},
      </if>
      <if test="forceSubtractRule != null">
        #{forceSubtractRule,jdbcType=CHAR},
      </if>
      <if test="forceAddRule != null">
        #{forceAddRule,jdbcType=CHAR},
      </if>
      <if test="serialNumber != null">
        #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="shareRegDt != null">
        #{shareRegDt,jdbcType=VARCHAR},
      </if>
      <if test="navDt != null">
        #{navDt,jdbcType=VARCHAR},
      </if>
      <if test="nav != null">
        #{nav,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="recStat != null">
        #{recStat,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTimestamp != null">
        #{updateTimestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderDtlPO">
    <!--@mbg.generated-->
    update hw_counter_audit_order_dtl
    <set>
      <if test="appSerialNo != null">
        app_serial_no = #{appSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="appSerialDtlNo != null">
        app_serial_dtl_no = #{appSerialDtlNo,jdbcType=VARCHAR},
      </if>
      <if test="hkCustNo != null">
        hk_cust_no = #{hkCustNo,jdbcType=VARCHAR},
      </if>
      <if test="custChineseName != null">
        cust_chinese_name = #{custChineseName,jdbcType=VARCHAR},
      </if>
      <if test="idNoDigest != null">
        id_no_digest = #{idNoDigest,jdbcType=VARCHAR},
      </if>
      <if test="idType != null">
        id_type = #{idType,jdbcType=VARCHAR},
      </if>
      <if test="cpAcctNo != null">
        cp_acct_no = #{cpAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="bankAcctMask != null">
        bank_acct_mask = #{bankAcctMask,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="paymentType != null">
        payment_type = #{paymentType,jdbcType=VARCHAR},
      </if>
      <if test="redeemType != null">
        redeem_type = #{redeemType,jdbcType=VARCHAR},
      </if>
      <if test="redeemDirection != null">
        redeem_direction = #{redeemDirection,jdbcType=VARCHAR},
      </if>
      <if test="appAmt != null">
        app_amt = #{appAmt,jdbcType=DECIMAL},
      </if>
      <if test="netAppAmt != null">
        net_app_amt = #{netAppAmt,jdbcType=DECIMAL},
      </if>
      <if test="appVol != null">
        app_vol = #{appVol,jdbcType=DECIMAL},
      </if>
      <if test="fee != null">
        fee = #{fee,jdbcType=DECIMAL},
      </if>
      <if test="appDiscount != null">
        app_discount = #{appDiscount,jdbcType=DECIMAL},
      </if>
      <if test="discountType != null">
        discount_type = #{discountType,jdbcType=CHAR},
      </if>
      <if test="discountAmt != null">
        discount_amt = #{discountAmt,jdbcType=DECIMAL},
      </if>
      <if test="feeRate != null">
        fee_rate = #{feeRate,jdbcType=DECIMAL},
      </if>
      <if test="feeCalMode != null">
        fee_cal_mode = #{feeCalMode,jdbcType=VARCHAR},
      </if>
      <if test="extensionOption != null">
        extension_option = #{extensionOption,jdbcType=VARCHAR},
      </if>
      <if test="extensionCount != null">
        extension_count = #{extensionCount,jdbcType=VARCHAR},
      </if>
      <if test="tradeChannel != null">
        trade_channel = #{tradeChannel,jdbcType=VARCHAR},
      </if>
      <if test="appDt != null">
        app_dt = #{appDt,jdbcType=VARCHAR},
      </if>
      <if test="appTm != null">
        app_tm = #{appTm,jdbcType=VARCHAR},
      </if>
      <if test="fundCode != null">
        fund_code = #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="fundAbbr != null">
        fund_abbr = #{fundAbbr,jdbcType=VARCHAR},
      </if>
      <if test="midleBusiCode != null">
        midle_busi_code = #{midleBusiCode,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAcctNo != null">
        fund_tx_acct_no = #{fundTxAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="fullTxAccCode != null">
        full_tx_acc_code = #{fullTxAccCode,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAccType != null">
        fund_tx_acc_type = #{fundTxAccType,jdbcType=VARCHAR},
      </if>
      <if test="prebookDealNo != null">
        prebook_deal_no = #{prebookDealNo,jdbcType=VARCHAR},
      </if>
      <if test="prebookDiscount != null">
        prebook_discount = #{prebookDiscount,jdbcType=DECIMAL},
      </if>
      <if test="estimateFee != null">
        estimate_fee = #{estimateFee,jdbcType=DECIMAL},
      </if>
      <if test="preAppAmt != null">
        pre_app_amt = #{preAppAmt,jdbcType=DECIMAL},
      </if>
      <if test="outletCode != null">
        outlet_code = #{outletCode,jdbcType=VARCHAR},
      </if>
      <if test="crmFileId != null">
        crm_file_id = #{crmFileId,jdbcType=VARCHAR},
      </if>
      <if test="revokeType != null">
        revoke_type = #{revokeType,jdbcType=VARCHAR},
      </if>
      <if test="revokeReason != null">
        revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="inHkCustNo != null">
        in_hk_cust_no = #{inHkCustNo,jdbcType=VARCHAR},
      </if>
      <if test="inFundTxAcctNo != null">
        in_fund_tx_acct_no = #{inFundTxAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="inFundCode != null">
        in_fund_code = #{inFundCode,jdbcType=VARCHAR},
      </if>
      <if test="inFundAbbr != null">
        in_fund_abbr = #{inFundAbbr,jdbcType=VARCHAR},
      </if>
      <if test="inCustChineseName != null">
        in_cust_chinese_name = #{inCustChineseName,jdbcType=VARCHAR},
      </if>
      <if test="inIdNoDigest != null">
        in_id_no_digest = #{inIdNoDigest,jdbcType=VARCHAR},
      </if>
      <if test="inIdType != null">
        in_id_type = #{inIdType,jdbcType=VARCHAR},
      </if>
      <if test="transferVol != null">
        transfer_vol = #{transferVol,jdbcType=DECIMAL},
      </if>
      <if test="transferActualAmt != null">
        transfer_actual_amt = #{transferActualAmt,jdbcType=DECIMAL},
      </if>
      <if test="transferSubsAmt != null">
        transfer_subs_amt = #{transferSubsAmt,jdbcType=DECIMAL},
      </if>
      <if test="transferAmt != null">
        transfer_amt = #{transferAmt,jdbcType=DECIMAL},
      </if>
      <if test="auditRemark != null">
        audit_remark = #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="dealNo != null">
        deal_no = #{dealNo,jdbcType=VARCHAR},
      </if>
      <if test="dealNoDtl != null">
        deal_no_dtl = #{dealNoDtl,jdbcType=VARCHAR},
      </if>
      <if test="inDealNo != null">
        in_deal_no = #{inDealNo,jdbcType=VARCHAR},
      </if>
      <if test="volDtlNo != null">
        vol_dtl_no = #{volDtlNo,jdbcType=VARCHAR},
      </if>
      <if test="preSubmitTaDt != null">
        pre_submit_ta_dt = #{preSubmitTaDt,jdbcType=VARCHAR},
      </if>
      <if test="preSubmitTaTm != null">
        pre_submit_ta_tm = #{preSubmitTaTm,jdbcType=VARCHAR},
      </if>
      <if test="openDt != null">
        open_dt = #{openDt,jdbcType=VARCHAR},
      </if>
      <if test="subAmt != null">
        sub_amt = #{subAmt,jdbcType=DECIMAL},
      </if>
      <if test="cumPaidAmt != null">
        cum_paid_amt = #{cumPaidAmt,jdbcType=DECIMAL},
      </if>
      <if test="forceSubtractRule != null">
        force_subtract_rule = #{forceSubtractRule,jdbcType=CHAR},
      </if>
      <if test="forceAddRule != null">
        force_add_rule = #{forceAddRule,jdbcType=CHAR},
      </if>
      <if test="serialNumber != null">
        serial_number = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="shareRegDt != null">
        share_reg_dt = #{shareRegDt,jdbcType=VARCHAR},
      </if>
      <if test="navDt != null">
        nav_dt = #{navDt,jdbcType=VARCHAR},
      </if>
      <if test="nav != null">
        nav = #{nav,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="recStat != null">
        rec_stat = #{recStat,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTimestamp != null">
        update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderDtlPO">
    <!--@mbg.generated-->
    update hw_counter_audit_order_dtl
    set app_serial_no = #{appSerialNo,jdbcType=VARCHAR},
      app_serial_dtl_no = #{appSerialDtlNo,jdbcType=VARCHAR},
      hk_cust_no = #{hkCustNo,jdbcType=VARCHAR},
      cust_chinese_name = #{custChineseName,jdbcType=VARCHAR},
      id_no_digest = #{idNoDigest,jdbcType=VARCHAR},
      id_type = #{idType,jdbcType=VARCHAR},
      cp_acct_no = #{cpAcctNo,jdbcType=VARCHAR},
      bank_acct_mask = #{bankAcctMask,jdbcType=VARCHAR},
      bank_name = #{bankName,jdbcType=VARCHAR},
      payment_type = #{paymentType,jdbcType=VARCHAR},
      redeem_type = #{redeemType,jdbcType=VARCHAR},
      redeem_direction = #{redeemDirection,jdbcType=VARCHAR},
      app_amt = #{appAmt,jdbcType=DECIMAL},
      net_app_amt = #{netAppAmt,jdbcType=DECIMAL},
      app_vol = #{appVol,jdbcType=DECIMAL},
      fee = #{fee,jdbcType=DECIMAL},
      app_discount = #{appDiscount,jdbcType=DECIMAL},
      discount_type = #{discountType,jdbcType=CHAR},
      discount_amt = #{discountAmt,jdbcType=DECIMAL},
      fee_rate = #{feeRate,jdbcType=DECIMAL},
      fee_cal_mode = #{feeCalMode,jdbcType=VARCHAR},
      extension_option = #{extensionOption,jdbcType=VARCHAR},
      extension_count = #{extensionCount,jdbcType=VARCHAR},
      trade_channel = #{tradeChannel,jdbcType=VARCHAR},
      app_dt = #{appDt,jdbcType=VARCHAR},
      app_tm = #{appTm,jdbcType=VARCHAR},
      fund_code = #{fundCode,jdbcType=VARCHAR},
      fund_abbr = #{fundAbbr,jdbcType=VARCHAR},
      midle_busi_code = #{midleBusiCode,jdbcType=VARCHAR},
      fund_tx_acct_no = #{fundTxAcctNo,jdbcType=VARCHAR},
      full_tx_acc_code = #{fullTxAccCode,jdbcType=VARCHAR},
      fund_tx_acc_type = #{fundTxAccType,jdbcType=VARCHAR},
      prebook_deal_no = #{prebookDealNo,jdbcType=VARCHAR},
      prebook_discount = #{prebookDiscount,jdbcType=DECIMAL},
      estimate_fee = #{estimateFee,jdbcType=DECIMAL},
      pre_app_amt = #{preAppAmt,jdbcType=DECIMAL},
      outlet_code = #{outletCode,jdbcType=VARCHAR},
      crm_file_id = #{crmFileId,jdbcType=VARCHAR},
      revoke_type = #{revokeType,jdbcType=VARCHAR},
      revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      in_hk_cust_no = #{inHkCustNo,jdbcType=VARCHAR},
      in_fund_tx_acct_no = #{inFundTxAcctNo,jdbcType=VARCHAR},
      in_fund_code = #{inFundCode,jdbcType=VARCHAR},
      in_fund_abbr = #{inFundAbbr,jdbcType=VARCHAR},
      in_cust_chinese_name = #{inCustChineseName,jdbcType=VARCHAR},
      in_id_no_digest = #{inIdNoDigest,jdbcType=VARCHAR},
      in_id_type = #{inIdType,jdbcType=VARCHAR},
      transfer_vol = #{transferVol,jdbcType=DECIMAL},
      transfer_actual_amt = #{transferActualAmt,jdbcType=DECIMAL},
      transfer_subs_amt = #{transferSubsAmt,jdbcType=DECIMAL},
      transfer_amt = #{transferAmt,jdbcType=DECIMAL},
      audit_remark = #{auditRemark,jdbcType=VARCHAR},
      deal_no = #{dealNo,jdbcType=VARCHAR},
      deal_no_dtl = #{dealNoDtl,jdbcType=VARCHAR},
      in_deal_no = #{inDealNo,jdbcType=VARCHAR},
      vol_dtl_no = #{volDtlNo,jdbcType=VARCHAR},
      pre_submit_ta_dt = #{preSubmitTaDt,jdbcType=VARCHAR},
      pre_submit_ta_tm = #{preSubmitTaTm,jdbcType=VARCHAR},
      open_dt = #{openDt,jdbcType=VARCHAR},
      sub_amt = #{subAmt,jdbcType=DECIMAL},
      cum_paid_amt = #{cumPaidAmt,jdbcType=DECIMAL},
      force_subtract_rule = #{forceSubtractRule,jdbcType=CHAR},
      force_add_rule = #{forceAddRule,jdbcType=CHAR},
      serial_number = #{serialNumber,jdbcType=VARCHAR},
      share_reg_dt = #{shareRegDt,jdbcType=VARCHAR},
      nav_dt = #{navDt,jdbcType=VARCHAR},
      nav = #{nav,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR},
      rec_stat = #{recStat,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
      update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>