/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

/**
 * <AUTHOR>
 * @description: 赎回方式 1-按份额、2-按金额
 * @date 2024/4/18 14:38
 * @since JDK 1.8
 */
public enum RedeemTypeEnum {

    BY_SHARE("1", "按份额"),
    BY_AMOUNT("2", "按金额");

    private String code;
    private String desc;

    RedeemTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static RedeemTypeEnum getEnumByCode(String code) {
        for (RedeemTypeEnum redeemTypeEnum : RedeemTypeEnum.values()) {
            if (redeemTypeEnum.getCode().equals(code)) {
                return redeemTypeEnum;
            }
        }
        return null;
    }

}
