package com.howbuy.dtms.manager.outservice.settle.dto;

import com.howbuy.dtms.settle.client.facade.validate.fundforceadd.FundForceAddValidateRequest;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:23:42+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class FundForceAddValidateDTOConvertMapperImpl implements FundForceAddValidateDTOConvertMapper {

    @Override
    public FundForceAddValidateRequest convert(FundForceAddValidateDTO dto) {
        if ( dto == null ) {
            return null;
        }

        FundForceAddValidateRequest fundForceAddValidateRequest = new FundForceAddValidateRequest();

        fundForceAddValidateRequest.setAckVol( dto.getAckVol() );
        fundForceAddValidateRequest.setForceAddRule( dto.getForceAddRule() );
        fundForceAddValidateRequest.setFundCode( dto.getFundCode() );
        fundForceAddValidateRequest.setFundTxAcctNo( dto.getFundTxAcctNo() );
        fundForceAddValidateRequest.setHkCustNo( dto.getHkCustNo() );
        fundForceAddValidateRequest.setNav( dto.getNav() );
        fundForceAddValidateRequest.setNavDt( dto.getNavDt() );
        fundForceAddValidateRequest.setSerialNumber( dto.getSerialNumber() );
        fundForceAddValidateRequest.setShareRegDt( dto.getShareRegDt() );
        fundForceAddValidateRequest.setTradeDt( dto.getTradeDt() );
        fundForceAddValidateRequest.setVolDtlNo( dto.getVolDtlNo() );

        return fundForceAddValidateRequest;
    }
}
