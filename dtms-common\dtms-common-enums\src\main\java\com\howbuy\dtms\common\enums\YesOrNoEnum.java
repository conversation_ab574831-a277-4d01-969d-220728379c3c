/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/4/11 16:04
 * @since JDK 1.8
 */
public enum YesOrNoEnum {

    NO("0"),
    YES("1");

    private  final String value;

    YesOrNoEnum(String value) {
        this.value = value;
    }

    public static YesOrNoEnum getValue(String value) {
        YesOrNoEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            YesOrNoEnum yesOrNoEnum = var1[var3];
            if (yesOrNoEnum.toString().equals(value)) {
                return yesOrNoEnum;
            }
        }

        return null;
    }

    public String getValue() {
        return value;
    }

    public String toString() {
        return this.value;
    }

}
