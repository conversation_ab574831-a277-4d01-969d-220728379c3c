<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.HwCounterAuditOrderRecordMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderRecordPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="appSerialNo" column="app_serial_no" jdbcType="VARCHAR"/>
            <result property="auditSerialNo" column="audit_serial_no" jdbcType="VARCHAR"/>
            <result property="operator" column="operator" jdbcType="VARCHAR"/>
            <result property="operatorType" column="operator_type" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="auditStatus" column="audit_status" jdbcType="VARCHAR"/>
            <result property="auditSnapshootJson" column="audit_snapshoot_json" jdbcType="VARCHAR"/>
            <result property="returnCode" column="return_code" jdbcType="VARCHAR"/>
            <result property="returnMsg" column="return_msg" jdbcType="VARCHAR"/>
            <result property="recStat" column="rec_stat" jdbcType="VARCHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="createTimestamp" column="create_timestamp" jdbcType="TIMESTAMP"/>
            <result property="updateTimestamp" column="update_timestamp" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,app_serial_no,audit_serial_no,
        operator,operator_type,remark,
        audit_status,audit_snapshoot_json,return_code,
        return_msg,rec_stat,creator,
        modifier,create_timestamp,update_timestamp
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hw_counter_audit_order_record
        where  id = #{id,jdbcType=BIGINT} 
    </select>
    <select id="queryAuditRecordListByAppSerialNo"
            resultType="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderRecordPO">
        select
        <include refid="Base_Column_List" />
        from hw_counter_audit_order_record
        where app_serial_no = #{appSerialNo,jdbcType=VARCHAR} order by create_timestamp asc
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from hw_counter_audit_order_record
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderRecordPO" useGeneratedKeys="true">
        insert into hw_counter_audit_order_record
        ( id,app_serial_no,audit_serial_no
        ,operator,operator_type,remark
        ,audit_status,audit_snapshoot_json,return_code
        ,return_msg,rec_stat,creator
        ,modifier,create_timestamp,update_timestamp
        )
        values (#{id,jdbcType=BIGINT},#{appSerialNo,jdbcType=VARCHAR},#{auditSerialNo,jdbcType=VARCHAR}
        ,#{operator,jdbcType=VARCHAR},#{operatorType,jdbcType=VARCHAR},#{remark,jdbcType=VARCHAR}
        ,#{auditStatus,jdbcType=VARCHAR},#{auditSnapshootJson,jdbcType=VARCHAR},#{returnCode,jdbcType=VARCHAR}
        ,#{returnMsg,jdbcType=VARCHAR},#{recStat,jdbcType=VARCHAR},#{creator,jdbcType=VARCHAR}
        ,#{modifier,jdbcType=VARCHAR},#{createTimestamp,jdbcType=TIMESTAMP},#{updateTimestamp,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderRecordPO" useGeneratedKeys="true">
        insert into hw_counter_audit_order_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="appSerialNo != null">app_serial_no,</if>
                <if test="auditSerialNo != null">audit_serial_no,</if>
                <if test="operator != null">operator,</if>
                <if test="operatorType != null">operator_type,</if>
                <if test="remark != null">remark,</if>
                <if test="auditStatus != null">audit_status,</if>
                <if test="auditSnapshootJson != null">audit_snapshoot_json,</if>
                <if test="returnCode != null">return_code,</if>
                <if test="returnMsg != null">return_msg,</if>
                <if test="recStat != null">rec_stat,</if>
                <if test="creator != null">creator,</if>
                <if test="modifier != null">modifier,</if>
                <if test="createTimestamp != null">create_timestamp,</if>
                <if test="updateTimestamp != null">update_timestamp,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="appSerialNo != null">#{appSerialNo,jdbcType=VARCHAR},</if>
                <if test="auditSerialNo != null">#{auditSerialNo,jdbcType=VARCHAR},</if>
                <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
                <if test="operatorType != null">#{operatorType,jdbcType=VARCHAR},</if>
                <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
                <if test="auditStatus != null">#{auditStatus,jdbcType=VARCHAR},</if>
                <if test="auditSnapshootJson != null">#{auditSnapshootJson,jdbcType=VARCHAR},</if>
                <if test="returnCode != null">#{returnCode,jdbcType=VARCHAR},</if>
                <if test="returnMsg != null">#{returnMsg,jdbcType=VARCHAR},</if>
                <if test="recStat != null">#{recStat,jdbcType=VARCHAR},</if>
                <if test="creator != null">#{creator,jdbcType=VARCHAR},</if>
                <if test="modifier != null">#{modifier,jdbcType=VARCHAR},</if>
                <if test="createTimestamp != null">#{createTimestamp,jdbcType=TIMESTAMP},</if>
                <if test="updateTimestamp != null">#{updateTimestamp,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderRecordPO">
        update hw_counter_audit_order_record
        <set>
                <if test="appSerialNo != null">
                    app_serial_no = #{appSerialNo,jdbcType=VARCHAR},
                </if>
                <if test="auditSerialNo != null">
                    audit_serial_no = #{auditSerialNo,jdbcType=VARCHAR},
                </if>
                <if test="operator != null">
                    operator = #{operator,jdbcType=VARCHAR},
                </if>
                <if test="operatorType != null">
                    operator_type = #{operatorType,jdbcType=VARCHAR},
                </if>
                <if test="remark != null">
                    remark = #{remark,jdbcType=VARCHAR},
                </if>
                <if test="auditStatus != null">
                    audit_status = #{auditStatus,jdbcType=VARCHAR},
                </if>
                <if test="auditSnapshootJson != null">
                    audit_snapshoot_json = #{auditSnapshootJson,jdbcType=VARCHAR},
                </if>
                <if test="returnCode != null">
                    return_code = #{returnCode,jdbcType=VARCHAR},
                </if>
                <if test="returnMsg != null">
                    return_msg = #{returnMsg,jdbcType=VARCHAR},
                </if>
                <if test="recStat != null">
                    rec_stat = #{recStat,jdbcType=VARCHAR},
                </if>
                <if test="creator != null">
                    creator = #{creator,jdbcType=VARCHAR},
                </if>
                <if test="modifier != null">
                    modifier = #{modifier,jdbcType=VARCHAR},
                </if>
                <if test="createTimestamp != null">
                    create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTimestamp != null">
                    update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderRecordPO">
        update hw_counter_audit_order_record
        set 
            app_serial_no =  #{appSerialNo,jdbcType=VARCHAR},
            audit_serial_no =  #{auditSerialNo,jdbcType=VARCHAR},
            operator =  #{operator,jdbcType=VARCHAR},
            operator_type =  #{operatorType,jdbcType=VARCHAR},
            remark =  #{remark,jdbcType=VARCHAR},
            audit_status =  #{auditStatus,jdbcType=VARCHAR},
            audit_snapshoot_json =  #{auditSnapshootJson,jdbcType=VARCHAR},
            return_code =  #{returnCode,jdbcType=VARCHAR},
            return_msg =  #{returnMsg,jdbcType=VARCHAR},
            rec_stat =  #{recStat,jdbcType=VARCHAR},
            creator =  #{creator,jdbcType=VARCHAR},
            modifier =  #{modifier,jdbcType=VARCHAR},
            create_timestamp =  #{createTimestamp,jdbcType=TIMESTAMP},
            update_timestamp =  #{updateTimestamp,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
