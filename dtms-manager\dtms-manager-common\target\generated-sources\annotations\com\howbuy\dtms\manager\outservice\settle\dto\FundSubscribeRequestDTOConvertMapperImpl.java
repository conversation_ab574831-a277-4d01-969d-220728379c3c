package com.howbuy.dtms.manager.outservice.settle.dto;

import com.howbuy.dtms.settle.client.facade.trade.subscribe.FundSubscribeRequest;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:23:42+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class FundSubscribeRequestDTOConvertMapperImpl implements FundSubscribeRequestDTOConvertMapper {

    @Override
    public FundSubscribeRequest convert(FundSubscribeRequestDTO dto) {
        if ( dto == null ) {
            return null;
        }

        FundSubscribeRequest fundSubscribeRequest = new FundSubscribeRequest();

        fundSubscribeRequest.setTraceId( dto.getTraceId() );
        fundSubscribeRequest.setAppAmt( dto.getAppAmt() );
        fundSubscribeRequest.setAppDt( dto.getAppDt() );
        fundSubscribeRequest.setAppStatus( dto.getAppStatus() );
        fundSubscribeRequest.setAppTm( dto.getAppTm() );
        fundSubscribeRequest.setBusiCode( dto.getBusiCode() );
        fundSubscribeRequest.setCpAcctNo( dto.getCpAcctNo() );
        fundSubscribeRequest.setCurrency( dto.getCurrency() );
        fundSubscribeRequest.setDealDtlNo( dto.getDealDtlNo() );
        fundSubscribeRequest.setDiscountRate( dto.getDiscountRate() );
        fundSubscribeRequest.setEsitmateFee( dto.getEsitmateFee() );
        fundSubscribeRequest.setFeeRate( dto.getFeeRate() );
        fundSubscribeRequest.setFundCode( dto.getFundCode() );
        fundSubscribeRequest.setFundTxAcctNo( dto.getFundTxAcctNo() );
        fundSubscribeRequest.setHkCustNo( dto.getHkCustNo() );
        fundSubscribeRequest.setInvstType( dto.getInvstType() );
        fundSubscribeRequest.setMiddleOrderNo( dto.getMiddleOrderNo() );
        fundSubscribeRequest.setNetAppAmt( dto.getNetAppAmt() );
        fundSubscribeRequest.setOpenDt( dto.getOpenDt() );
        fundSubscribeRequest.setPreSubmitTaDt( dto.getPreSubmitTaDt() );
        fundSubscribeRequest.setPreSubmitTaTm( dto.getPreSubmitTaTm() );
        fundSubscribeRequest.setSubmitTaDt( dto.getSubmitTaDt() );

        return fundSubscribeRequest;
    }
}
