<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="info" monitorInterval="30">

	<properties>
		<property name="logPath">/data/logs/dtms-manager-remote</property>
		<property name="rollingLogName">dtms-manager-remote</property>
		<property name="consoleMainLogName">dtms-manager-console-main</property>
		<property name="counterMainLogName">dtms-manager--counter-main</property>
		<property name="sqlLevel">debug</property>
		<property name="cacheLogName">cache-state-collect</property>
		<property name="wechatLoggerName">dtms-wechat</property>
	</properties>

	<Appenders>
		<Console name="Console" target="SYSTEM_OUT">
			<Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{0} line%L %X{requestUuid} - %msg%n</Pattern>
		</Console>

		<RollingFile name="RollingFile" filename="${logPath}/${rollingLogName}.log" filepattern="${logPath}/%d{yyyyMMdd}/${rollingLogName}-%i.log">
			<CustomPatternLayout pattern='{"time":"%d{yyyy-MM-dd HH:mm:ss.SSS}","custNo":"%X{custNo}","tid":"%X{traceId}","status":"%X{status}","labelName":"%markerSimpleName","thread":"%t","level":"%-5p","class_line":"%c:%L","msg":"%enc{%msg}{JSON}"}%n'>
				<replaces>
					<replace regex='(\\"idNo\\":\\")(\d{6})\d{1,8}(\d{4})' replacement="$1$2********$3" />
					<replace regex='(\\"bankAcct\\":\\")(\d{6})\d{1,6}(\d{4})' replacement="$1$2********$3" />
					<replace regex='(\\"fileBytes\\":\\")[^\\"]*' replacement="$1***" />
				</replaces>
			</CustomPatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
				<SizeBasedTriggeringPolicy size="1024 MB" />
			</Policies>
			<DefaultRolloverStrategy max="100" />
		</RollingFile>

		<RollingFile name="CounterMainLogger" filename="${logPath}/${counterMainLogName}.log"
			filepattern="${logPath}/%d{yyyyMMdd}/${counterMainLogName}.log">
			<PatternLayout pattern="%msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
			</Policies>
			<DefaultRolloverStrategy max="30" />
		</RollingFile>
		<RollingFile name="ConsoleMainLogger" filename="${logPath}/${consoleMainLogName}.log"
					 filepattern="${logPath}/%d{yyyyMMdd}/${consoleMainLogName}.log">
			<PatternLayout pattern="%msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
			</Policies>
			<DefaultRolloverStrategy max="30" />
		</RollingFile>

		<!-- 企业微信日志 -->
		<RollingFile name="wechatLogger" filename="${logPath}/${wechatLoggerName}.log"
					 filepattern="${logPath}/%d{yyyyMMdd}/${wechatLoggerName}.log">
			<PatternLayout pattern="%msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
			</Policies>
			<DefaultRolloverStrategy max="30" />
		</RollingFile>
		<RollingFile name="alertLogger" filename="${logPath}/${alertLog}.log"
					 filepattern="${logPath}/%d{yyyyMMdd}/${alertLog}.log">
			<PatternLayout pattern="%msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
			</Policies>
			<DefaultRolloverStrategy max="30" />
		</RollingFile>

		<RollingFile name="CacheStatCollectLogger" filename="${logPath}/${cacheLogName}.log"
			filepattern="${logPath}/%d{yyyyMMdd}/${cacheLogName}.log">
			<PatternLayout pattern="%msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
			</Policies>
			<DefaultRolloverStrategy max="30" />
		</RollingFile>


	</Appenders>

	<Loggers>
		<AsyncLogger name="com.howbuy" level="info" additivity="false">
			<appender-ref ref="RollingFile" />
			<appender-ref ref="Console" />
		</AsyncLogger>
		<AsyncLogger name="consoleMainLog" level="info" additivity="false">
             <appender-ref ref="ConsoleMainLogger"/>
        </AsyncLogger>
		<AsyncLogger name="counterMainLog" level="info" additivity="false">
			<appender-ref ref="CounterMainLogger"/>
		</AsyncLogger>
		<AsyncLogger name="wechatLogger" level="info" additivity="false">
			<appender-ref ref="wechatLogger"/>
		</AsyncLogger>
		<AsyncLogger name="COST_TIME_COLLECT_LOGGER" level="info" additivity="false">
			<AppenderRef ref="CacheStatCollectLogger" />
		</AsyncLogger>
		<root level="info">
			<appender-ref ref="Console" />
			<appender-ref ref="RollingFile" />
		</root>
	</Loggers>
</Configuration>
