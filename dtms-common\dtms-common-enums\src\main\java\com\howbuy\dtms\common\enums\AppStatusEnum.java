/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

/**
 * <AUTHOR>
 * @description: 申请状态 0-申请成功；1-申请失败；2-自行撤销；3-强制取消
 * @date 2024/4/18 14:32
 * @since JDK 1.8
 */
public enum AppStatusEnum {

    APPLY_SUCCESS("0", "申请成功"),
    APPLY_FAIL("1", "申请失败"),
    SELF_CANCEL("2", "自行撤销"),
    FORCE_CANCEL("3", "强制取消");

    private String code;
    private String desc;

    AppStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

}
