package com.howbuy.dtms.manager.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/6 20:01
 * @since JDK 1.8
 */
@Getter
@Component
public class SystemCommonConfig {

    /**
     * 业务延迟时间（分钟）
     */
    @Value("${business.delay.buffer.time}")
    private String bufferTime;
    @Value("${business.generate.pdf.timeout}")
    private String generatePdfTimeout;
    @Value("${business.generate.pdf.wait.time}")
    private String generatePdfWaitTime;
    @Value("${business.generate.pdf.wait.type}")
    private String generatePdfWaitType;
    /**
     * 月结单生成日期范围，默认第三个工作日
     */
    @Value("${business.generate.month.statement.date.index:3}")
    private int monthStatementGenerateDateIndex;
}
