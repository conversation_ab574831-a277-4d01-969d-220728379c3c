package com.howbuy.dtms.manager.outservice.settle.dto;

import com.howbuy.dtms.settle.client.facade.trade.payin.FundPayInRequest;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:23:43+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class FundPayInRequestDTOConvertMapperImpl implements FundPayInRequestDTOConvertMapper {

    @Override
    public FundPayInRequest convert(FundPayInRequestDTO dto) {
        if ( dto == null ) {
            return null;
        }

        FundPayInRequest fundPayInRequest = new FundPayInRequest();

        fundPayInRequest.setTraceId( dto.getTraceId() );
        fundPayInRequest.setAppAmt( dto.getAppAmt() );
        fundPayInRequest.setAppDt( dto.getAppDt() );
        fundPayInRequest.setAppStatus( dto.getAppStatus() );
        fundPayInRequest.setAppTm( dto.getAppTm() );
        fundPayInRequest.setBusiCode( dto.getBusiCode() );
        fundPayInRequest.setCpAcctNo( dto.getCpAcctNo() );
        fundPayInRequest.setCurrency( dto.getCurrency() );
        fundPayInRequest.setDealDtlNo( dto.getDealDtlNo() );
        fundPayInRequest.setDiscountRate( dto.getDiscountRate() );
        fundPayInRequest.setEsitmateFee( dto.getEsitmateFee() );
        fundPayInRequest.setFeeRate( dto.getFeeRate() );
        fundPayInRequest.setFundCode( dto.getFundCode() );
        fundPayInRequest.setFundTxAcctNo( dto.getFundTxAcctNo() );
        fundPayInRequest.setHkCustNo( dto.getHkCustNo() );
        fundPayInRequest.setInvstType( dto.getInvstType() );
        fundPayInRequest.setMiddleOrderNo( dto.getMiddleOrderNo() );
        fundPayInRequest.setNetAppAmt( dto.getNetAppAmt() );
        fundPayInRequest.setOpenDt( dto.getOpenDt() );
        fundPayInRequest.setPreSubmitTaDt( dto.getPreSubmitTaDt() );
        fundPayInRequest.setPreSubmitTaTm( dto.getPreSubmitTaTm() );
        fundPayInRequest.setSubmitTaDt( dto.getSubmitTaDt() );

        return fundPayInRequest;
    }
}
