<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.HwDealOrderDtlMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwDealOrderDtlPO">
        <!--@Table hw_deal_order_dtl-->
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="dealDtlNo" column="deal_dtl_no" jdbcType="BIGINT"/>
        <result property="dealNo" column="deal_no" jdbcType="BIGINT"/>
        <result property="hkCustNo" column="hk_cust_no" jdbcType="VARCHAR"/>
        <result property="cpAcctNo" column="cp_acct_no" jdbcType="VARCHAR"/>
        <result property="fundCode" column="fund_code" jdbcType="VARCHAR"/>
        <result property="fundName" column="fund_name" jdbcType="VARCHAR"/>
        <result property="fundAbbr" column="fund_abbr" jdbcType="VARCHAR"/>
        <result property="mainFundCode" column="main_fund_code" jdbcType="VARCHAR"/>
        <result property="fundCategory" column="fund_category" jdbcType="VARCHAR"/>
        <result property="fundRiskLevel" column="fund_risk_level" jdbcType="VARCHAR"/>
        <result property="redeemType" column="redeem_type" jdbcType="VARCHAR"/>
        <result property="redeemDirectionList" column="redeem_direction_list" jdbcType="VARCHAR"/>
        <result property="middleBusiCode" column="middle_busi_code" jdbcType="VARCHAR"/>
        <result property="appAmt" column="app_amt" jdbcType="NUMERIC"/>
        <result property="netAppAmt" column="net_app_amt" jdbcType="NUMERIC"/>
        <result property="appVol" column="app_vol" jdbcType="NUMERIC"/>
        <result property="estimateFee" column="estimate_fee" jdbcType="NUMERIC"/>
        <result property="prebookDiscount" column="prebook_discount" jdbcType="NUMERIC"/>
        <result property="discountRate" column="discount_rate" jdbcType="NUMERIC"/>
        <result property="discountType" column="discount_type" jdbcType="VARCHAR"/>
        <result property="discountAmt" column="discount_amt" jdbcType="NUMERIC"/>
        <result property="feeRate" column="fee_rate" jdbcType="NUMERIC"/>
        <result property="feeCalMode" column="fee_cal_mode" jdbcType="VARCHAR"/>
        <result property="fee" column="fee" jdbcType="NUMERIC"/>
        <result property="ackAmt" column="ack_amt" jdbcType="NUMERIC"/>
        <result property="ackVol" column="ack_vol" jdbcType="NUMERIC"/>
        <result property="ackNav" column="ack_nav" jdbcType="NUMERIC"/>
        <result property="ackNavDt" column="ack_nav_dt" jdbcType="VARCHAR"/>
        <result property="currency" column="currency" jdbcType="VARCHAR"/>
        <result property="appStatus" column="app_status" jdbcType="VARCHAR"/>
        <result property="ackStatus" column="ack_status" jdbcType="VARCHAR"/>
        <result property="transferPrice" column="transfer_price" jdbcType="NUMERIC"/>
        <result property="fundDivMode" column="fund_div_mode" jdbcType="VARCHAR"/>
        <result property="openDt" column="open_dt" jdbcType="VARCHAR"/>
        <result property="payEndDt" column="pay_end_dt" jdbcType="VARCHAR"/>
        <result property="payEndTm" column="pay_end_tm" jdbcType="VARCHAR"/>
        <result property="productPayEndDt" column="product_pay_end_dt" jdbcType="VARCHAR"/>
        <result property="productPayEndDm" column="product_pay_end_dm" jdbcType="VARCHAR"/>
        <result property="taTradeDt" column="ta_trade_dt" jdbcType="VARCHAR"/>
        <result property="ackDt" column="ack_dt" jdbcType="VARCHAR"/>
        <result property="taAckNo" column="ta_ack_no" jdbcType="VARCHAR"/>
        <result property="submitStatus" column="submit_status" jdbcType="VARCHAR"/>
        <result property="preSubmitTaDt" column="pre_submit_ta_dt" jdbcType="VARCHAR"/>
        <result property="preSubmitTaTm" column="pre_submit_ta_tm" jdbcType="VARCHAR"/>
        <result property="fundManCode" column="fund_man_code" jdbcType="VARCHAR"/>
        <result property="extOption" column="ext_option" jdbcType="VARCHAR"/>
        <result property="extControlType" column="ext_control_type" jdbcType="VARCHAR"/>
        <result property="extControlNum" column="ext_control_num" jdbcType="VARCHAR"/>
        <result property="cancelDate" column="cancel_date" jdbcType="VARCHAR"/>
        <result property="cancelCause" column="cancel_cause" jdbcType="VARCHAR"/>
        <result property="cancelCpAcctNo" column="cancel_cp_acct_no" jdbcType="VARCHAR"/>
        <result property="fundTxAcctNo" column="fund_tx_acct_no" jdbcType="VARCHAR"/>
        <result property="intoFundCode" column="into_fund_code" jdbcType="VARCHAR"/>
        <result property="intoMainFundCode" column="into_main_fund_code" jdbcType="VARCHAR"/>
        <result property="intoFundAbbr" column="into_fund_abbr" jdbcType="VARCHAR"/>
        <result property="intoCurrency" column="into_currency" jdbcType="VARCHAR"/>
        <result property="intoAckAmt" column="into_ack_amt" jdbcType="NUMERIC"/>
        <result property="intoAckVol" column="into_ack_vol" jdbcType="NUMERIC"/>
        <result property="intoAckNav" column="into_ack_nav" jdbcType="NUMERIC"/>
        <result property="intoAckNavDt" column="into_ack_nav_dt" jdbcType="VARCHAR"/>
        <result property="intoFundTxAcctNo" column="into_fund_tx_acct_no" jdbcType="VARCHAR"/>
        <result property="relationalDealDtlNo" column="relational_deal_dtl_no" jdbcType="BIGINT"/>
        <result property="volDtlNo" column="vol_dtl_no" jdbcType="VARCHAR"/>
        <result property="subAmt" column="sub_amt" jdbcType="NUMERIC"/>
        <result property="recStat" column="rec_stat" jdbcType="VARCHAR"/>
        <result property="createTimestamp" column="create_timestamp" jdbcType="TIMESTAMP"/>
        <result property="updateTimestamp" column="update_timestamp" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="extResultMap" type="com.howbuy.dtms.manager.dao.po.HwDealOrderDtlRelPO" extends="BaseResultMap">
        <result column="app_dt" property="appDt" jdbcType="VARCHAR"/>
        <result column="pay_status" property="payStatus" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        deal_dtl_no, 
        deal_no, 
        hk_cust_no, 
        cp_acct_no, 
        fund_code, 
        fund_name, 
        fund_abbr, 
        main_fund_code, 
        fund_category, 
        fund_risk_level, 
        redeem_type, 
        redeem_direction_list, 
        middle_busi_code, 
        app_amt, 
        net_app_amt, 
        app_vol, 
        estimate_fee, 
        prebook_discount, 
        discount_rate, 
        discount_type, 
        discount_amt, 
        fee_rate, 
        fee_cal_mode, 
        fee, 
        ack_amt, 
        ack_vol, 
        ack_nav, 
        ack_nav_dt, 
        currency, 
        app_status, 
        ack_status, 
        transfer_price, 
        fund_div_mode, 
        open_dt, 
        pay_end_dt, 
        pay_end_tm, 
        product_pay_end_dt, 
        product_pay_end_dm, 
        ta_trade_dt, 
        ack_dt, 
        ta_ack_no, 
        submit_status, 
        pre_submit_ta_dt, 
        pre_submit_ta_tm, 
        fund_man_code, 
        ext_option, 
        ext_control_type, 
        ext_control_num, 
        cancel_date, 
        cancel_cause, 
        cancel_cp_acct_no, 
        fund_tx_acct_no, 
        into_fund_code, 
        into_main_fund_code, 
        into_fund_abbr, 
        into_currency, 
        into_ack_amt, 
        into_ack_vol, 
        into_ack_nav, 
        into_ack_nav_dt, 
        into_fund_tx_acct_no, 
        relational_deal_dtl_no, 
        vol_dtl_no, 
        sub_amt, 
        rec_stat, 
        create_timestamp, 
        update_timestamp
    </sql>

    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_deal_order_dtl
        where id = #{id}
    </select>

    <!-- 查询符合条件的数据 -->
    <select id="selectBySelective" parameterType="com.howbuy.dtms.manager.dao.po.HwDealOrderDtlPO"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_deal_order_dtl
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="dealDtlNo != null">
                and deal_dtl_no = #{dealDtlNo}
            </if>
            <if test="dealNo != null">
                and deal_no = #{dealNo}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and hk_cust_no = #{hkCustNo}
            </if>
            <if test="cpAcctNo != null and cpAcctNo != ''">
                and cp_acct_no = #{cpAcctNo}
            </if>
            <if test="fundCode != null and fundCode != ''">
                and fund_code = #{fundCode}
            </if>
            <if test="fundName != null and fundName != ''">
                and fund_name = #{fundName}
            </if>
            <if test="fundAbbr != null and fundAbbr != ''">
                and fund_abbr = #{fundAbbr}
            </if>
            <if test="mainFundCode != null and mainFundCode != ''">
                and main_fund_code = #{mainFundCode}
            </if>
            <if test="fundCategory != null and fundCategory != ''">
                and fund_category = #{fundCategory}
            </if>
            <if test="fundRiskLevel != null and fundRiskLevel != ''">
                and fund_risk_level = #{fundRiskLevel}
            </if>
            <if test="redeemType != null and redeemType != ''">
                and redeem_type = #{redeemType}
            </if>
            <if test="redeemDirectionList != null and redeemDirectionList != ''">
                and redeem_direction_list = #{redeemDirectionList}
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                and middle_busi_code = #{middleBusiCode}
            </if>
            <if test="appAmt != null">
                and app_amt = #{appAmt}
            </if>
            <if test="netAppAmt != null">
                and net_app_amt = #{netAppAmt}
            </if>
            <if test="appVol != null">
                and app_vol = #{appVol}
            </if>
            <if test="estimateFee != null">
                and estimate_fee = #{estimateFee}
            </if>
            <if test="prebookDiscount != null">
                and prebook_discount = #{prebookDiscount}
            </if>
            <if test="discountRate != null">
                and discount_rate = #{discountRate}
            </if>
            <if test="discountType != null and discountType != ''">
                and discount_type = #{discountType}
            </if>
            <if test="discountAmt != null">
                and discount_amt = #{discountAmt}
            </if>
            <if test="feeRate != null">
                and fee_rate = #{feeRate}
            </if>
            <if test="feeCalMode != null and feeCalMode != ''">
                and fee_cal_mode = #{feeCalMode}
            </if>
            <if test="fee != null">
                and fee = #{fee}
            </if>
            <if test="ackAmt != null">
                and ack_amt = #{ackAmt}
            </if>
            <if test="ackVol != null">
                and ack_vol = #{ackVol}
            </if>
            <if test="ackNav != null">
                and ack_nav = #{ackNav}
            </if>
            <if test="ackNavDt != null and ackNavDt != ''">
                and ack_nav_dt = #{ackNavDt}
            </if>
            <if test="currency != null and currency != ''">
                and currency = #{currency}
            </if>
            <if test="appStatus != null and appStatus != ''">
                and app_status = #{appStatus}
            </if>
            <if test="ackStatus != null and ackStatus != ''">
                and ack_status = #{ackStatus}
            </if>
            <if test="transferPrice != null">
                and transfer_price = #{transferPrice}
            </if>
            <if test="fundDivMode != null and fundDivMode != ''">
                and fund_div_mode = #{fundDivMode}
            </if>
            <if test="openDt != null and openDt != ''">
                and open_dt = #{openDt}
            </if>
            <if test="payEndDt != null and payEndDt != ''">
                and pay_end_dt = #{payEndDt}
            </if>
            <if test="payEndTm != null and payEndTm != ''">
                and pay_end_tm = #{payEndTm}
            </if>
            <if test="productPayEndDt != null and productPayEndDt != ''">
                and product_pay_end_dt = #{productPayEndDt}
            </if>
            <if test="productPayEndDm != null and productPayEndDm != ''">
                and product_pay_end_dm = #{productPayEndDm}
            </if>
            <if test="taTradeDt != null and taTradeDt != ''">
                and ta_trade_dt = #{taTradeDt}
            </if>
            <if test="ackDt != null and ackDt != ''">
                and ack_dt = #{ackDt}
            </if>
            <if test="taAckNo != null and taAckNo != ''">
                and ta_ack_no = #{taAckNo}
            </if>
            <if test="submitStatus != null and submitStatus != ''">
                and submit_status = #{submitStatus}
            </if>
            <if test="preSubmitTaDt != null and preSubmitTaDt != ''">
                and pre_submit_ta_dt = #{preSubmitTaDt}
            </if>
            <if test="preSubmitTaTm != null and preSubmitTaTm != ''">
                and pre_submit_ta_tm = #{preSubmitTaTm}
            </if>
            <if test="fundManCode != null and fundManCode != ''">
                and fund_man_code = #{fundManCode}
            </if>
            <if test="extOption != null and extOption != ''">
                and ext_option = #{extOption}
            </if>
            <if test="extControlType != null and extControlType != ''">
                and ext_control_type = #{extControlType}
            </if>
            <if test="extControlNum != null and extControlNum != ''">
                and ext_control_num = #{extControlNum}
            </if>
            <if test="cancelDate != null and cancelDate != ''">
                and cancel_date = #{cancelDate}
            </if>
            <if test="cancelCause != null and cancelCause != ''">
                and cancel_cause = #{cancelCause}
            </if>
            <if test="cancelCpAcctNo != null and cancelCpAcctNo != ''">
                and cancel_cp_acct_no = #{cancelCpAcctNo}
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                and fund_tx_acct_no = #{fundTxAcctNo}
            </if>
            <if test="intoFundCode != null and intoFundCode != ''">
                and into_fund_code = #{intoFundCode}
            </if>
            <if test="intoMainFundCode != null and intoMainFundCode != ''">
                and into_main_fund_code = #{intoMainFundCode}
            </if>
            <if test="intoFundAbbr != null and intoFundAbbr != ''">
                and into_fund_abbr = #{intoFundAbbr}
            </if>
            <if test="intoCurrency != null and intoCurrency != ''">
                and into_currency = #{intoCurrency}
            </if>
            <if test="intoAckAmt != null">
                and into_ack_amt = #{intoAckAmt}
            </if>
            <if test="intoAckVol != null">
                and into_ack_vol = #{intoAckVol}
            </if>
            <if test="intoAckNav != null">
                and into_ack_nav = #{intoAckNav}
            </if>
            <if test="intoAckNavDt != null and intoAckNavDt != ''">
                and into_ack_nav_dt = #{intoAckNavDt}
            </if>
            <if test="intoFundTxAcctNo != null and intoFundTxAcctNo != ''">
                and into_fund_tx_acct_no = #{intoFundTxAcctNo}
            </if>
            <if test="relationalDealDtlNo != null">
                and relational_deal_dtl_no = #{relationalDealDtlNo}
            </if>
            <if test="volDtlNo != null and volDtlNo != ''">
                and vol_dtl_no = #{volDtlNo}
            </if>
            <if test="subAmt != null">
                and sub_amt = #{subAmt}
            </if>
            <if test="recStat != null and recStat != ''">
                and rec_stat = #{recStat}
            </if>
            <if test="createTimestamp != null">
                and create_timestamp = #{createTimestamp}
            </if>
            <if test="updateTimestamp != null">
                and update_timestamp = #{updateTimestamp}
            </if>
        </where>
        order by id
    </select>


    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(*)
        from hw_deal_order_dtl
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="dealDtlNo != null">
                and deal_dtl_no = #{dealDtlNo}
            </if>
            <if test="dealNo != null">
                and deal_no = #{dealNo}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and hk_cust_no = #{hkCustNo}
            </if>
            <if test="cpAcctNo != null and cpAcctNo != ''">
                and cp_acct_no = #{cpAcctNo}
            </if>
            <if test="fundCode != null and fundCode != ''">
                and fund_code = #{fundCode}
            </if>
            <if test="fundName != null and fundName != ''">
                and fund_name = #{fundName}
            </if>
            <if test="fundAbbr != null and fundAbbr != ''">
                and fund_abbr = #{fundAbbr}
            </if>
            <if test="mainFundCode != null and mainFundCode != ''">
                and main_fund_code = #{mainFundCode}
            </if>
            <if test="fundCategory != null and fundCategory != ''">
                and fund_category = #{fundCategory}
            </if>
            <if test="fundRiskLevel != null and fundRiskLevel != ''">
                and fund_risk_level = #{fundRiskLevel}
            </if>
            <if test="redeemType != null and redeemType != ''">
                and redeem_type = #{redeemType}
            </if>
            <if test="redeemDirectionList != null and redeemDirectionList != ''">
                and redeem_direction_list = #{redeemDirectionList}
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                and middle_busi_code = #{middleBusiCode}
            </if>
            <if test="appAmt != null">
                and app_amt = #{appAmt}
            </if>
            <if test="netAppAmt != null">
                and net_app_amt = #{netAppAmt}
            </if>
            <if test="appVol != null">
                and app_vol = #{appVol}
            </if>
            <if test="estimateFee != null">
                and estimate_fee = #{estimateFee}
            </if>
            <if test="prebookDiscount != null">
                and prebook_discount = #{prebookDiscount}
            </if>
            <if test="discountRate != null">
                and discount_rate = #{discountRate}
            </if>
            <if test="discountType != null and discountType != ''">
                and discount_type = #{discountType}
            </if>
            <if test="discountAmt != null">
                and discount_amt = #{discountAmt}
            </if>
            <if test="feeRate != null">
                and fee_rate = #{feeRate}
            </if>
            <if test="feeCalMode != null and feeCalMode != ''">
                and fee_cal_mode = #{feeCalMode}
            </if>
            <if test="fee != null">
                and fee = #{fee}
            </if>
            <if test="ackAmt != null">
                and ack_amt = #{ackAmt}
            </if>
            <if test="ackVol != null">
                and ack_vol = #{ackVol}
            </if>
            <if test="ackNav != null">
                and ack_nav = #{ackNav}
            </if>
            <if test="ackNavDt != null and ackNavDt != ''">
                and ack_nav_dt = #{ackNavDt}
            </if>
            <if test="currency != null and currency != ''">
                and currency = #{currency}
            </if>
            <if test="appStatus != null and appStatus != ''">
                and app_status = #{appStatus}
            </if>
            <if test="ackStatus != null and ackStatus != ''">
                and ack_status = #{ackStatus}
            </if>
            <if test="transferPrice != null">
                and transfer_price = #{transferPrice}
            </if>
            <if test="fundDivMode != null and fundDivMode != ''">
                and fund_div_mode = #{fundDivMode}
            </if>
            <if test="openDt != null and openDt != ''">
                and open_dt = #{openDt}
            </if>
            <if test="payEndDt != null and payEndDt != ''">
                and pay_end_dt = #{payEndDt}
            </if>
            <if test="payEndTm != null and payEndTm != ''">
                and pay_end_tm = #{payEndTm}
            </if>
            <if test="productPayEndDt != null and productPayEndDt != ''">
                and product_pay_end_dt = #{productPayEndDt}
            </if>
            <if test="productPayEndDm != null and productPayEndDm != ''">
                and product_pay_end_dm = #{productPayEndDm}
            </if>
            <if test="taTradeDt != null and taTradeDt != ''">
                and ta_trade_dt = #{taTradeDt}
            </if>
            <if test="ackDt != null and ackDt != ''">
                and ack_dt = #{ackDt}
            </if>
            <if test="taAckNo != null and taAckNo != ''">
                and ta_ack_no = #{taAckNo}
            </if>
            <if test="submitStatus != null and submitStatus != ''">
                and submit_status = #{submitStatus}
            </if>
            <if test="preSubmitTaDt != null and preSubmitTaDt != ''">
                and pre_submit_ta_dt = #{preSubmitTaDt}
            </if>
            <if test="preSubmitTaTm != null and preSubmitTaTm != ''">
                and pre_submit_ta_tm = #{preSubmitTaTm}
            </if>
            <if test="fundManCode != null and fundManCode != ''">
                and fund_man_code = #{fundManCode}
            </if>
            <if test="extOption != null and extOption != ''">
                and ext_option = #{extOption}
            </if>
            <if test="extControlType != null and extControlType != ''">
                and ext_control_type = #{extControlType}
            </if>
            <if test="extControlNum != null and extControlNum != ''">
                and ext_control_num = #{extControlNum}
            </if>
            <if test="cancelDate != null and cancelDate != ''">
                and cancel_date = #{cancelDate}
            </if>
            <if test="cancelCause != null and cancelCause != ''">
                and cancel_cause = #{cancelCause}
            </if>
            <if test="cancelCpAcctNo != null and cancelCpAcctNo != ''">
                and cancel_cp_acct_no = #{cancelCpAcctNo}
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                and fund_tx_acct_no = #{fundTxAcctNo}
            </if>
            <if test="intoFundCode != null and intoFundCode != ''">
                and into_fund_code = #{intoFundCode}
            </if>
            <if test="intoMainFundCode != null and intoMainFundCode != ''">
                and into_main_fund_code = #{intoMainFundCode}
            </if>
            <if test="intoFundAbbr != null and intoFundAbbr != ''">
                and into_fund_abbr = #{intoFundAbbr}
            </if>
            <if test="intoCurrency != null and intoCurrency != ''">
                and into_currency = #{intoCurrency}
            </if>
            <if test="intoAckAmt != null">
                and into_ack_amt = #{intoAckAmt}
            </if>
            <if test="intoAckVol != null">
                and into_ack_vol = #{intoAckVol}
            </if>
            <if test="intoAckNav != null">
                and into_ack_nav = #{intoAckNav}
            </if>
            <if test="intoAckNavDt != null and intoAckNavDt != ''">
                and into_ack_nav_dt = #{intoAckNavDt}
            </if>
            <if test="intoFundTxAcctNo != null and intoFundTxAcctNo != ''">
                and into_fund_tx_acct_no = #{intoFundTxAcctNo}
            </if>
            <if test="relationalDealDtlNo != null">
                and relational_deal_dtl_no = #{relationalDealDtlNo}
            </if>
            <if test="volDtlNo != null and volDtlNo != ''">
                and vol_dtl_no = #{volDtlNo}
            </if>
            <if test="subAmt != null">
                and sub_amt = #{subAmt}
            </if>
            <if test="recStat != null and recStat != ''">
                and rec_stat = #{recStat}
            </if>
            <if test="createTimestamp != null">
                and create_timestamp = #{createTimestamp}
            </if>
            <if test="updateTimestamp != null">
                and update_timestamp = #{updateTimestamp}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into hw_deal_order_dtl(deal_dtl_no, deal_no, hk_cust_no, cp_acct_no, fund_code, fund_name, fund_abbr,
                                      main_fund_code, fund_category, fund_risk_level, redeem_type,
                                      redeem_direction_list, middle_busi_code, app_amt, net_app_amt, app_vol,
                                      estimate_fee, prebook_discount, discount_rate, discount_type, discount_amt,
                                      fee_rate, fee_cal_mode, fee, ack_amt, ack_vol, ack_nav, ack_nav_dt, currency,
                                      app_status, ack_status, transfer_price, fund_div_mode, open_dt, pay_end_dt,
                                      pay_end_tm, product_pay_end_dt, product_pay_end_dm, ta_trade_dt, ack_dt,
                                      ta_ack_no, submit_status, pre_submit_ta_dt, pre_submit_ta_tm, fund_man_code,
                                      ext_option, ext_control_type, ext_control_num, cancel_date, cancel_cause,
                                      cancel_cp_acct_no, fund_tx_acct_no, into_fund_code, into_main_fund_code,
                                      into_fund_abbr, into_currency, into_ack_amt, into_ack_vol, into_ack_nav,
                                      into_ack_nav_dt, into_fund_tx_acct_no, relational_deal_dtl_no, vol_dtl_no,
                                      sub_amt, rec_stat, create_timestamp, update_timestamp)
        values (#{dealDtlNo}, #{dealNo}, #{hkCustNo}, #{cpAcctNo}, #{fundCode}, #{fundName}, #{fundAbbr},
                #{mainFundCode}, #{fundCategory}, #{fundRiskLevel}, #{redeemType}, #{redeemDirectionList},
                #{middleBusiCode}, #{appAmt}, #{netAppAmt}, #{appVol}, #{estimateFee}, #{prebookDiscount},
                #{discountRate}, #{discountType}, #{discountAmt}, #{feeRate}, #{feeCalMode}, #{fee}, #{ackAmt},
                #{ackVol}, #{ackNav}, #{ackNavDt}, #{currency}, #{appStatus}, #{ackStatus}, #{transferPrice},
                #{fundDivMode}, #{openDt}, #{payEndDt}, #{payEndTm}, #{productPayEndDt}, #{productPayEndDm},
                #{taTradeDt}, #{ackDt}, #{taAckNo}, #{submitStatus}, #{preSubmitTaDt}, #{preSubmitTaTm}, #{fundManCode},
                #{extOption}, #{extControlType}, #{extControlNum}, #{cancelDate}, #{cancelCause}, #{cancelCpAcctNo},
                #{fundTxAcctNo}, #{intoFundCode}, #{intoMainFundCode}, #{intoFundAbbr}, #{intoCurrency}, #{intoAckAmt},
                #{intoAckVol}, #{intoAckNav}, #{intoAckNavDt}, #{intoFundTxAcctNo}, #{relationalDealDtlNo}, #{volDtlNo},
                #{subAmt}, #{recStat}, #{createTimestamp}, #{updateTimestamp})
    </insert>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true">
        insert into hw_deal_order_dtl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dealDtlNo != null">
                deal_dtl_no,
            </if>
            <if test="dealNo != null">
                deal_no,
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                hk_cust_no,
            </if>
            <if test="cpAcctNo != null and cpAcctNo != ''">
                cp_acct_no,
            </if>
            <if test="fundCode != null and fundCode != ''">
                fund_code,
            </if>
            <if test="fundName != null and fundName != ''">
                fund_name,
            </if>
            <if test="fundAbbr != null and fundAbbr != ''">
                fund_abbr,
            </if>
            <if test="mainFundCode != null and mainFundCode != ''">
                main_fund_code,
            </if>
            <if test="fundCategory != null and fundCategory != ''">
                fund_category,
            </if>
            <if test="fundRiskLevel != null and fundRiskLevel != ''">
                fund_risk_level,
            </if>
            <if test="redeemType != null and redeemType != ''">
                redeem_type,
            </if>
            <if test="redeemDirectionList != null and redeemDirectionList != ''">
                redeem_direction_list,
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                middle_busi_code,
            </if>
            <if test="appAmt != null">
                app_amt,
            </if>
            <if test="netAppAmt != null">
                net_app_amt,
            </if>
            <if test="appVol != null">
                app_vol,
            </if>
            <if test="estimateFee != null">
                estimate_fee,
            </if>
            <if test="prebookDiscount != null">
                prebook_discount,
            </if>
            <if test="discountRate != null">
                discount_rate,
            </if>
            <if test="discountType != null and discountType != ''">
                discount_type,
            </if>
            <if test="discountAmt != null">
                discount_amt,
            </if>
            <if test="feeRate != null">
                fee_rate,
            </if>
            <if test="feeCalMode != null and feeCalMode != ''">
                fee_cal_mode,
            </if>
            <if test="fee != null">
                fee,
            </if>
            <if test="ackAmt != null">
                ack_amt,
            </if>
            <if test="ackVol != null">
                ack_vol,
            </if>
            <if test="ackNav != null">
                ack_nav,
            </if>
            <if test="ackNavDt != null and ackNavDt != ''">
                ack_nav_dt,
            </if>
            <if test="currency != null and currency != ''">
                currency,
            </if>
            <if test="appStatus != null and appStatus != ''">
                app_status,
            </if>
            <if test="ackStatus != null and ackStatus != ''">
                ack_status,
            </if>
            <if test="transferPrice != null">
                transfer_price,
            </if>
            <if test="fundDivMode != null and fundDivMode != ''">
                fund_div_mode,
            </if>
            <if test="openDt != null and openDt != ''">
                open_dt,
            </if>
            <if test="payEndDt != null and payEndDt != ''">
                pay_end_dt,
            </if>
            <if test="payEndTm != null and payEndTm != ''">
                pay_end_tm,
            </if>
            <if test="productPayEndDt != null and productPayEndDt != ''">
                product_pay_end_dt,
            </if>
            <if test="productPayEndDm != null and productPayEndDm != ''">
                product_pay_end_dm,
            </if>
            <if test="taTradeDt != null and taTradeDt != ''">
                ta_trade_dt,
            </if>
            <if test="ackDt != null and ackDt != ''">
                ack_dt,
            </if>
            <if test="taAckNo != null and taAckNo != ''">
                ta_ack_no,
            </if>
            <if test="submitStatus != null and submitStatus != ''">
                submit_status,
            </if>
            <if test="preSubmitTaDt != null and preSubmitTaDt != ''">
                pre_submit_ta_dt,
            </if>
            <if test="preSubmitTaTm != null and preSubmitTaTm != ''">
                pre_submit_ta_tm,
            </if>
            <if test="fundManCode != null and fundManCode != ''">
                fund_man_code,
            </if>
            <if test="extOption != null and extOption != ''">
                ext_option,
            </if>
            <if test="extControlType != null and extControlType != ''">
                ext_control_type,
            </if>
            <if test="extControlNum != null and extControlNum != ''">
                ext_control_num,
            </if>
            <if test="cancelDate != null and cancelDate != ''">
                cancel_date,
            </if>
            <if test="cancelCause != null and cancelCause != ''">
                cancel_cause,
            </if>
            <if test="cancelCpAcctNo != null and cancelCpAcctNo != ''">
                cancel_cp_acct_no,
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                fund_tx_acct_no,
            </if>
            <if test="intoFundCode != null and intoFundCode != ''">
                into_fund_code,
            </if>
            <if test="intoMainFundCode != null and intoMainFundCode != ''">
                into_main_fund_code,
            </if>
            <if test="intoFundAbbr != null and intoFundAbbr != ''">
                into_fund_abbr,
            </if>
            <if test="intoCurrency != null and intoCurrency != ''">
                into_currency,
            </if>
            <if test="intoAckAmt != null">
                into_ack_amt,
            </if>
            <if test="intoAckVol != null">
                into_ack_vol,
            </if>
            <if test="intoAckNav != null">
                into_ack_nav,
            </if>
            <if test="intoAckNavDt != null and intoAckNavDt != ''">
                into_ack_nav_dt,
            </if>
            <if test="intoFundTxAcctNo != null and intoFundTxAcctNo != ''">
                into_fund_tx_acct_no,
            </if>
            <if test="relationalDealDtlNo != null">
                relational_deal_dtl_no,
            </if>
            <if test="volDtlNo != null and volDtlNo != ''">
                vol_dtl_no,
            </if>
            <if test="subAmt != null">
                sub_amt,
            </if>
            <if test="recStat != null and recStat != ''">
                rec_stat,
            </if>
            <if test="createTimestamp != null">
                create_timestamp,
            </if>
            <if test="updateTimestamp != null">
                update_timestamp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dealDtlNo != null">
                #{dealDtlNo},
            </if>
            <if test="dealNo != null">
                #{dealNo},
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                #{hkCustNo},
            </if>
            <if test="cpAcctNo != null and cpAcctNo != ''">
                #{cpAcctNo},
            </if>
            <if test="fundCode != null and fundCode != ''">
                #{fundCode},
            </if>
            <if test="fundName != null and fundName != ''">
                #{fundName},
            </if>
            <if test="fundAbbr != null and fundAbbr != ''">
                #{fundAbbr},
            </if>
            <if test="mainFundCode != null and mainFundCode != ''">
                #{mainFundCode},
            </if>
            <if test="fundCategory != null and fundCategory != ''">
                #{fundCategory},
            </if>
            <if test="fundRiskLevel != null and fundRiskLevel != ''">
                #{fundRiskLevel},
            </if>
            <if test="redeemType != null and redeemType != ''">
                #{redeemType},
            </if>
            <if test="redeemDirectionList != null and redeemDirectionList != ''">
                #{redeemDirectionList},
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                #{middleBusiCode},
            </if>
            <if test="appAmt != null">
                #{appAmt},
            </if>
            <if test="netAppAmt != null">
                #{netAppAmt},
            </if>
            <if test="appVol != null">
                #{appVol},
            </if>
            <if test="estimateFee != null">
                #{estimateFee},
            </if>
            <if test="prebookDiscount != null">
                #{prebookDiscount},
            </if>
            <if test="discountRate != null">
                #{discountRate},
            </if>
            <if test="discountType != null and discountType != ''">
                #{discountType},
            </if>
            <if test="discountAmt != null">
                #{discountAmt},
            </if>
            <if test="feeRate != null">
                #{feeRate},
            </if>
            <if test="feeCalMode != null and feeCalMode != ''">
                #{feeCalMode},
            </if>
            <if test="fee != null">
                #{fee},
            </if>
            <if test="ackAmt != null">
                #{ackAmt},
            </if>
            <if test="ackVol != null">
                #{ackVol},
            </if>
            <if test="ackNav != null">
                #{ackNav},
            </if>
            <if test="ackNavDt != null and ackNavDt != ''">
                #{ackNavDt},
            </if>
            <if test="currency != null and currency != ''">
                #{currency},
            </if>
            <if test="appStatus != null and appStatus != ''">
                #{appStatus},
            </if>
            <if test="ackStatus != null and ackStatus != ''">
                #{ackStatus},
            </if>
            <if test="transferPrice != null">
                #{transferPrice},
            </if>
            <if test="fundDivMode != null and fundDivMode != ''">
                #{fundDivMode},
            </if>
            <if test="openDt != null and openDt != ''">
                #{openDt},
            </if>
            <if test="payEndDt != null and payEndDt != ''">
                #{payEndDt},
            </if>
            <if test="payEndTm != null and payEndTm != ''">
                #{payEndTm},
            </if>
            <if test="productPayEndDt != null and productPayEndDt != ''">
                #{productPayEndDt},
            </if>
            <if test="productPayEndDm != null and productPayEndDm != ''">
                #{productPayEndDm},
            </if>
            <if test="taTradeDt != null and taTradeDt != ''">
                #{taTradeDt},
            </if>
            <if test="ackDt != null and ackDt != ''">
                #{ackDt},
            </if>
            <if test="taAckNo != null and taAckNo != ''">
                #{taAckNo},
            </if>
            <if test="submitStatus != null and submitStatus != ''">
                #{submitStatus},
            </if>
            <if test="preSubmitTaDt != null and preSubmitTaDt != ''">
                #{preSubmitTaDt},
            </if>
            <if test="preSubmitTaTm != null and preSubmitTaTm != ''">
                #{preSubmitTaTm},
            </if>
            <if test="fundManCode != null and fundManCode != ''">
                #{fundManCode},
            </if>
            <if test="extOption != null and extOption != ''">
                #{extOption},
            </if>
            <if test="extControlType != null and extControlType != ''">
                #{extControlType},
            </if>
            <if test="extControlNum != null and extControlNum != ''">
                #{extControlNum},
            </if>
            <if test="cancelDate != null and cancelDate != ''">
                #{cancelDate},
            </if>
            <if test="cancelCause != null and cancelCause != ''">
                #{cancelCause},
            </if>
            <if test="cancelCpAcctNo != null and cancelCpAcctNo != ''">
                #{cancelCpAcctNo},
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                #{fundTxAcctNo},
            </if>
            <if test="intoFundCode != null and intoFundCode != ''">
                #{intoFundCode},
            </if>
            <if test="intoMainFundCode != null and intoMainFundCode != ''">
                #{intoMainFundCode},
            </if>
            <if test="intoFundAbbr != null and intoFundAbbr != ''">
                #{intoFundAbbr},
            </if>
            <if test="intoCurrency != null and intoCurrency != ''">
                #{intoCurrency},
            </if>
            <if test="intoAckAmt != null">
                #{intoAckAmt},
            </if>
            <if test="intoAckVol != null">
                #{intoAckVol},
            </if>
            <if test="intoAckNav != null">
                #{intoAckNav},
            </if>
            <if test="intoAckNavDt != null and intoAckNavDt != ''">
                #{intoAckNavDt},
            </if>
            <if test="intoFundTxAcctNo != null and intoFundTxAcctNo != ''">
                #{intoFundTxAcctNo},
            </if>
            <if test="relationalDealDtlNo != null">
                #{relationalDealDtlNo},
            </if>
            <if test="volDtlNo != null and volDtlNo != ''">
                #{volDtlNo},
            </if>
            <if test="subAmt != null">
                #{subAmt},
            </if>
            <if test="recStat != null and recStat != ''">
                #{recStat},
            </if>
            <if test="createTimestamp != null">
                #{createTimestamp},
            </if>
            <if test="updateTimestamp != null">
                #{updateTimestamp},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into hw_deal_order_dtl(deal_dtl_no, deal_no, hk_cust_no, cp_acct_no, fund_code, fund_name, fund_abbr,
        main_fund_code, fund_category, fund_risk_level, redeem_type, redeem_direction_list, middle_busi_code, app_amt,
        net_app_amt, app_vol, estimate_fee, prebook_discount, discount_rate, discount_type, discount_amt, fee_rate,
        fee_cal_mode, fee, ack_amt, ack_vol, ack_nav, ack_nav_dt, currency, app_status, ack_status, transfer_price,
        fund_div_mode, open_dt, pay_end_dt, pay_end_tm, product_pay_end_dt, product_pay_end_dm, ta_trade_dt, ack_dt,
        ta_ack_no, submit_status, pre_submit_ta_dt, pre_submit_ta_tm, fund_man_code, ext_option, ext_control_type,
        ext_control_num, cancel_date, cancel_cause, cancel_cp_acct_no, fund_tx_acct_no, into_fund_code,
        into_main_fund_code, into_fund_abbr, into_currency, into_ack_amt, into_ack_vol, into_ack_nav, into_ack_nav_dt,
        into_fund_tx_acct_no, relational_deal_dtl_no, vol_dtl_no, sub_amt, rec_stat, create_timestamp, update_timestamp)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.dealDtlNo}, #{entity.dealNo}, #{entity.hkCustNo}, #{entity.cpAcctNo}, #{entity.fundCode},
            #{entity.fundName}, #{entity.fundAbbr}, #{entity.mainFundCode}, #{entity.fundCategory},
            #{entity.fundRiskLevel}, #{entity.redeemType}, #{entity.redeemDirectionList}, #{entity.middleBusiCode},
            #{entity.appAmt}, #{entity.netAppAmt}, #{entity.appVol}, #{entity.estimateFee}, #{entity.prebookDiscount},
            #{entity.discountRate}, #{entity.discountType}, #{entity.discountAmt}, #{entity.feeRate},
            #{entity.feeCalMode}, #{entity.fee}, #{entity.ackAmt}, #{entity.ackVol}, #{entity.ackNav},
            #{entity.ackNavDt}, #{entity.currency}, #{entity.appStatus}, #{entity.ackStatus}, #{entity.transferPrice},
            #{entity.fundDivMode}, #{entity.openDt}, #{entity.payEndDt}, #{entity.payEndTm}, #{entity.productPayEndDt},
            #{entity.productPayEndDm}, #{entity.taTradeDt}, #{entity.ackDt}, #{entity.taAckNo}, #{entity.submitStatus},
            #{entity.preSubmitTaDt}, #{entity.preSubmitTaTm}, #{entity.fundManCode}, #{entity.extOption},
            #{entity.extControlType}, #{entity.extControlNum}, #{entity.cancelDate}, #{entity.cancelCause},
            #{entity.cancelCpAcctNo}, #{entity.fundTxAcctNo}, #{entity.intoFundCode}, #{entity.intoMainFundCode},
            #{entity.intoFundAbbr}, #{entity.intoCurrency}, #{entity.intoAckAmt}, #{entity.intoAckVol},
            #{entity.intoAckNav}, #{entity.intoAckNavDt}, #{entity.intoFundTxAcctNo}, #{entity.relationalDealDtlNo},
            #{entity.volDtlNo}, #{entity.subAmt}, #{entity.recStat}, #{entity.createTimestamp},
            #{entity.updateTimestamp})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update hw_deal_order_dtl
        <set>
            <if test="dealDtlNo != null">
                deal_dtl_no = #{dealDtlNo},
            </if>
            <if test="dealNo != null">
                deal_no = #{dealNo},
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                hk_cust_no = #{hkCustNo},
            </if>
            <if test="cpAcctNo != null and cpAcctNo != ''">
                cp_acct_no = #{cpAcctNo},
            </if>
            <if test="fundCode != null and fundCode != ''">
                fund_code = #{fundCode},
            </if>
            <if test="fundName != null and fundName != ''">
                fund_name = #{fundName},
            </if>
            <if test="fundAbbr != null and fundAbbr != ''">
                fund_abbr = #{fundAbbr},
            </if>
            <if test="mainFundCode != null and mainFundCode != ''">
                main_fund_code = #{mainFundCode},
            </if>
            <if test="fundCategory != null and fundCategory != ''">
                fund_category = #{fundCategory},
            </if>
            <if test="fundRiskLevel != null and fundRiskLevel != ''">
                fund_risk_level = #{fundRiskLevel},
            </if>
            <if test="redeemType != null and redeemType != ''">
                redeem_type = #{redeemType},
            </if>
            <if test="redeemDirectionList != null and redeemDirectionList != ''">
                redeem_direction_list = #{redeemDirectionList},
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                middle_busi_code = #{middleBusiCode},
            </if>
            <if test="appAmt != null">
                app_amt = #{appAmt},
            </if>
            <if test="netAppAmt != null">
                net_app_amt = #{netAppAmt},
            </if>
            <if test="appVol != null">
                app_vol = #{appVol},
            </if>
            <if test="estimateFee != null">
                estimate_fee = #{estimateFee},
            </if>
            <if test="prebookDiscount != null">
                prebook_discount = #{prebookDiscount},
            </if>
            <if test="discountRate != null">
                discount_rate = #{discountRate},
            </if>
            <if test="discountType != null and discountType != ''">
                discount_type = #{discountType},
            </if>
            <if test="discountAmt != null">
                discount_amt = #{discountAmt},
            </if>
            <if test="feeRate != null">
                fee_rate = #{feeRate},
            </if>
            <if test="feeCalMode != null and feeCalMode != ''">
                fee_cal_mode = #{feeCalMode},
            </if>
            <if test="fee != null">
                fee = #{fee},
            </if>
            <if test="ackAmt != null">
                ack_amt = #{ackAmt},
            </if>
            <if test="ackVol != null">
                ack_vol = #{ackVol},
            </if>
            <if test="ackNav != null">
                ack_nav = #{ackNav},
            </if>
            <if test="ackNavDt != null and ackNavDt != ''">
                ack_nav_dt = #{ackNavDt},
            </if>
            <if test="currency != null and currency != ''">
                currency = #{currency},
            </if>
            <if test="appStatus != null and appStatus != ''">
                app_status = #{appStatus},
            </if>
            <if test="ackStatus != null and ackStatus != ''">
                ack_status = #{ackStatus},
            </if>
            <if test="transferPrice != null">
                transfer_price = #{transferPrice},
            </if>
            <if test="fundDivMode != null and fundDivMode != ''">
                fund_div_mode = #{fundDivMode},
            </if>
            <if test="openDt != null and openDt != ''">
                open_dt = #{openDt},
            </if>
            <if test="payEndDt != null and payEndDt != ''">
                pay_end_dt = #{payEndDt},
            </if>
            <if test="payEndTm != null and payEndTm != ''">
                pay_end_tm = #{payEndTm},
            </if>
            <if test="productPayEndDt != null and productPayEndDt != ''">
                product_pay_end_dt = #{productPayEndDt},
            </if>
            <if test="productPayEndDm != null and productPayEndDm != ''">
                product_pay_end_dm = #{productPayEndDm},
            </if>
            <if test="taTradeDt != null and taTradeDt != ''">
                ta_trade_dt = #{taTradeDt},
            </if>
            <if test="ackDt != null and ackDt != ''">
                ack_dt = #{ackDt},
            </if>
            <if test="taAckNo != null and taAckNo != ''">
                ta_ack_no = #{taAckNo},
            </if>
            <if test="submitStatus != null and submitStatus != ''">
                submit_status = #{submitStatus},
            </if>
            <if test="preSubmitTaDt != null and preSubmitTaDt != ''">
                pre_submit_ta_dt = #{preSubmitTaDt},
            </if>
            <if test="preSubmitTaTm != null and preSubmitTaTm != ''">
                pre_submit_ta_tm = #{preSubmitTaTm},
            </if>
            <if test="fundManCode != null and fundManCode != ''">
                fund_man_code = #{fundManCode},
            </if>
            <if test="extOption != null and extOption != ''">
                ext_option = #{extOption},
            </if>
            <if test="extControlType != null and extControlType != ''">
                ext_control_type = #{extControlType},
            </if>
            <if test="extControlNum != null and extControlNum != ''">
                ext_control_num = #{extControlNum},
            </if>
            <if test="cancelDate != null and cancelDate != ''">
                cancel_date = #{cancelDate},
            </if>
            <if test="cancelCause != null and cancelCause != ''">
                cancel_cause = #{cancelCause},
            </if>
            <if test="cancelCpAcctNo != null and cancelCpAcctNo != ''">
                cancel_cp_acct_no = #{cancelCpAcctNo},
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                fund_tx_acct_no = #{fundTxAcctNo},
            </if>
            <if test="intoFundCode != null and intoFundCode != ''">
                into_fund_code = #{intoFundCode},
            </if>
            <if test="intoMainFundCode != null and intoMainFundCode != ''">
                into_main_fund_code = #{intoMainFundCode},
            </if>
            <if test="intoFundAbbr != null and intoFundAbbr != ''">
                into_fund_abbr = #{intoFundAbbr},
            </if>
            <if test="intoCurrency != null and intoCurrency != ''">
                into_currency = #{intoCurrency},
            </if>
            <if test="intoAckAmt != null">
                into_ack_amt = #{intoAckAmt},
            </if>
            <if test="intoAckVol != null">
                into_ack_vol = #{intoAckVol},
            </if>
            <if test="intoAckNav != null">
                into_ack_nav = #{intoAckNav},
            </if>
            <if test="intoAckNavDt != null and intoAckNavDt != ''">
                into_ack_nav_dt = #{intoAckNavDt},
            </if>
            <if test="intoFundTxAcctNo != null and intoFundTxAcctNo != ''">
                into_fund_tx_acct_no = #{intoFundTxAcctNo},
            </if>
            <if test="relationalDealDtlNo != null">
                relational_deal_dtl_no = #{relationalDealDtlNo},
            </if>
            <if test="volDtlNo != null and volDtlNo != ''">
                vol_dtl_no = #{volDtlNo},
            </if>
            <if test="subAmt != null">
                sub_amt = #{subAmt},
            </if>
            <if test="recStat != null and recStat != ''">
                rec_stat = #{recStat},
            </if>
            <if test="createTimestamp != null">
                create_timestamp = #{createTimestamp},
            </if>
            <if test="updateTimestamp != null">
                update_timestamp = #{updateTimestamp},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from hw_deal_order_dtl
        where id = #{id}
    </delete>

    <sql id="comme_where">
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="dealDtlNo != null">
            and deal_dtl_no = #{dealDtlNo}
        </if>
        <if test="dealNo != null">
            and deal_no = #{dealNo}
        </if>
        <if test="hkCustNo != null and hkCustNo != ''">
            and hk_cust_no = #{hkCustNo}
        </if>
        <if test="cpAcctNo != null and cpAcctNo != ''">
            and cp_acct_no = #{cpAcctNo}
        </if>
        <if test="fundCode != null and fundCode != ''">
            and fund_code = #{fundCode}
        </if>
        <if test="fundName != null and fundName != ''">
            and fund_name = #{fundName}
        </if>
        <if test="fundAbbr != null and fundAbbr != ''">
            and fund_abbr = #{fundAbbr}
        </if>
        <if test="mainFundCode != null and mainFundCode != ''">
            and main_fund_code = #{mainFundCode}
        </if>
        <if test="fundCategory != null and fundCategory != ''">
            and fund_category = #{fundCategory}
        </if>
        <if test="fundRiskLevel != null and fundRiskLevel != ''">
            and fund_risk_level = #{fundRiskLevel}
        </if>
        <if test="redeemType != null and redeemType != ''">
            and redeem_type = #{redeemType}
        </if>
        <if test="redeemDirectionList != null and redeemDirectionList != ''">
            and redeem_direction_list = #{redeemDirectionList}
        </if>
        <if test="middleBusiCode != null and middleBusiCode != ''">
            and middle_busi_code = #{middleBusiCode}
        </if>
        <if test="appAmt != null">
            and app_amt = #{appAmt}
        </if>
        <if test="netAppAmt != null">
            and net_app_amt = #{netAppAmt}
        </if>
        <if test="appVol != null">
            and app_vol = #{appVol}
        </if>
        <if test="estimateFee != null">
            and estimate_fee = #{estimateFee}
        </if>
        <if test="prebookDiscount != null">
            and prebook_discount = #{prebookDiscount}
        </if>
        <if test="discountRate != null">
            and discount_rate = #{discountRate}
        </if>
        <if test="discountType != null and discountType != ''">
            and discount_type = #{discountType}
        </if>
        <if test="discountAmt != null">
            and discount_amt = #{discountAmt}
        </if>
        <if test="feeRate != null">
            and fee_rate = #{feeRate}
        </if>
        <if test="feeCalMode != null and feeCalMode != ''">
            and fee_cal_mode = #{feeCalMode}
        </if>
        <if test="fee != null">
            and fee = #{fee}
        </if>
        <if test="ackAmt != null">
            and ack_amt = #{ackAmt}
        </if>
        <if test="ackVol != null">
            and ack_vol = #{ackVol}
        </if>
        <if test="ackNav != null">
            and ack_nav = #{ackNav}
        </if>
        <if test="ackNavDt != null and ackNavDt != ''">
            and ack_nav_dt = #{ackNavDt}
        </if>
        <if test="currency != null and currency != ''">
            and currency = #{currency}
        </if>
        <if test="appStatus != null and appStatus != ''">
            and app_status = #{appStatus}
        </if>
        <if test="ackStatus != null and ackStatus != ''">
            and ack_status = #{ackStatus}
        </if>
        <if test="transferPrice != null">
            and transfer_price = #{transferPrice}
        </if>
        <if test="fundDivMode != null and fundDivMode != ''">
            and fund_div_mode = #{fundDivMode}
        </if>
        <if test="openDt != null and openDt != ''">
            and open_dt = #{openDt}
        </if>
        <if test="payEndDt != null and payEndDt != ''">
            and pay_end_dt = #{payEndDt}
        </if>
        <if test="payEndTm != null and payEndTm != ''">
            and pay_end_tm = #{payEndTm}
        </if>
        <if test="taTradeDt != null and taTradeDt != ''">
            and ta_trade_dt = #{taTradeDt}
        </if>
        <if test="ackDt != null and ackDt != ''">
            and ack_dt = #{ackDt}
        </if>
        <if test="taAckNo != null and taAckNo != ''">
            and ta_ack_no = #{taAckNo}
        </if>
        <if test="submitStatus != null and submitStatus != ''">
            and submit_status = #{submitStatus}
        </if>
        <if test="preSubmitTaDt != null and preSubmitTaDt != ''">
            and pre_submit_ta_dt = #{preSubmitTaDt}
        </if>
        <if test="preSubmitTaTm != null and preSubmitTaTm != ''">
            and pre_submit_ta_tm = #{preSubmitTaTm}
        </if>
        <if test="fundManCode != null and fundManCode != ''">
            and fund_man_code = #{fundManCode}
        </if>
        <if test="extOption != null and extOption != ''">
            and ext_option = #{extOption}
        </if>
        <if test="extControlType != null and extControlType != ''">
            and ext_control_type = #{extControlType}
        </if>
        <if test="extControlNum != null and extControlNum != ''">
            and ext_control_num = #{extControlNum}
        </if>
        <if test="cancelDate != null and cancelDate != ''">
            and cancel_date = #{cancelDate}
        </if>
        <if test="cancelCause != null and cancelCause != ''">
            and cancel_cause = #{cancelCause}
        </if>
        <if test="cancelCpAcctNo != null and cancelCpAcctNo != ''">
            and cancel_cp_acct_no = #{cancelCpAcctNo}
        </if>
        <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
            and fund_tx_acct_no = #{fundTxAcctNo}
        </if>
        <if test="intoFundCode != null and intoFundCode != ''">
            and into_fund_code = #{intoFundCode}
        </if>
        <if test="intoMainFundCode != null and intoMainFundCode != ''">
            and into_main_fund_code = #{intoMainFundCode}
        </if>
        <if test="intoFundAbbr != null and intoFundAbbr != ''">
            and into_fund_abbr = #{intoFundAbbr}
        </if>
        <if test="intoCurrency != null and intoCurrency != ''">
            and into_currency = #{intoCurrency}
        </if>
        <if test="intoAckAmt != null">
            and into_ack_amt = #{intoAckAmt}
        </if>
        <if test="intoAckVol != null">
            and into_ack_vol = #{intoAckVol}
        </if>
        <if test="intoAckNav != null">
            and into_ack_nav = #{intoAckNav}
        </if>
        <if test="intoAckNavDt != null and intoAckNavDt != ''">
            and into_ack_nav_dt = #{intoAckNavDt}
        </if>
        <if test="intoFundTxAcctNo != null and intoFundTxAcctNo != ''">
            and into_fund_tx_acct_no = #{intoFundTxAcctNo}
        </if>
        <if test="relationalDealDtlNo != null">
            and relational_deal_dtl_no = #{relationalDealDtlNo}
        </if>
        <if test="volDtlNo != null and volDtlNo != ''">
            and vol_dtl_no = #{volDtlNo}
        </if>
        <if test="subAmt != null">
            and sub_amt = #{subAmt}
        </if>
        <if test="recStat != null and recStat != ''">
            and rec_stat = #{recStat}
        </if>
        <if test="createTimestamp != null">
            and create_timestamp = #{createTimestamp}
        </if>
        <if test="updateTimestamp != null">
            and update_timestamp = #{updateTimestamp}
        </if>
        <if test="idList != null and idList.size > 0">
            and id in
            <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="fundCodeList != null and fundCodeList.size > 0">
            and fund_code in
            <foreach item="item" index="index" collection="fundCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="middleBusiCodeList != null and middleBusiCodeList.size > 0">
            and middle_busi_code in
            <foreach item="item" index="index" collection="middleBusiCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="appStatusList != null and appStatusList.size > 0">
            and app_status in
            <foreach item="item" index="index" collection="appStatusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="ackStatusList != null and ackStatusList.size > 0">
            and ack_status in
            <foreach item="item" index="index" collection="ackStatusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="submitStatusList != null and submitStatusList.size > 0">
            and submit_status in
            <foreach item="item" index="index" collection="submitStatusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="redeemTypeList != null and redeemTypeList.size > 0">
            and redeem_type in
            <foreach item="item" index="index" collection="redeemTypeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="fundManCodeList != null and fundManCodeList.size > 0">
            and fund_man_code in
            <foreach item="item" index="index" collection="fundManCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="preSubmitTaDtRangeStart != null and preSubmitTaDtRangeStart != '' ">
            <![CDATA[
                and pre_submit_ta_dt >= #{preSubmitTaDtRangeStart}
                ]]>
        </if>
        <if test="preSubmitTaDtRangeEnd != null and preSubmitTaDtRangeEnd != '' ">
            <![CDATA[
                and pre_submit_ta_dt <= #{preSubmitTaDtRangeEnd}
                ]]>
        </if>
        <if test="openDtRangeStart != null and openDtRangeStart != '' ">
            <![CDATA[
                and open_dt >= #{openDtRangeStart}
                ]]>
        </if>
        <if test="openDtRangeEnd != null and openDtRangeEnd != '' ">
            <![CDATA[
                and open_dt <= #{openDtRangeEnd}
                ]]>
        </if>
        <if test="redeemDirectionExList != null and redeemDirectionExList.size > 0">
            and redeem_direction_list in
            <foreach item="item" index="index" collection="redeemDirectionExList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=' isPiggyFund == "1" and piggyFundCodeList != null and piggyFundCodeList.size > 0'>
            and fund_code in
            <foreach item="item" index="index" collection="piggyFundCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=' isPiggyFund == "0" and piggyFundCodeList != null and piggyFundCodeList.size > 0'>
            and fund_code not in
            <foreach item="item" index="index" collection="piggyFundCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="fundTxAcctNoList != null and fundTxAcctNoList.size() > 0">
            and fund_tx_acct_no in
            <foreach item="item" index="index" collection="fundTxAcctNoList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and rec_stat = '0'
    </sql>
    <!--分页查询，根据主键ID升序排序-->
    <select id="selectWithPage" parameterType="com.howbuy.dtms.manager.dao.query.HwDealOrderDtlQuery"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hw_deal_order_dtl
        <where>
            <include refid="comme_where" />
        </where>
        order by pre_submit_ta_dt desc, hk_cust_no, middle_busi_code, fund_code, currency, redeem_type, deal_dtl_no, ID
    </select>
    <select id="summaryByFundCode" parameterType="com.howbuy.dtms.manager.dao.query.HwDealOrderDtlQuery"
            resultType="com.howbuy.dtms.manager.dao.bo.HwDealOrderDtlSummaryBO">
        select
        fund_code as fundCode,
        count(fund_code) as totalCount,
        sum(ifnull(app_amt, 0)) as totalAppAmt,
        sum(ifnull(app_vol, 0)) as totalAppVol
        from hw_deal_order_dtl
        <where>
            <include refid="comme_where" />
        </where>
        group by fund_code
    </select>
    <select id="queryOrderDtlRelList" resultMap="extResultMap"
            parameterType="com.howbuy.dtms.manager.dao.query.HwDealOrderDtlRelQuery">
        select t1.*, t2.app_dt, t2.pay_status
        from hw_deal_order_dtl t1
        left join hw_deal_order t2 on t1.deal_no = t2.deal_no
        <where>
            <if test="hkCustNo != null and hkCustNo != ''">
                and t1.hk_cust_no = #{hkCustNo}
            </if>
            <if test="appStartDt != null and appStartDt != '' ">
                <![CDATA[ and t2.app_dt >= #{appStartDt} ]]>
            </if>
            <if test="appEndDt != null and appEndDt != '' ">
                <![CDATA[ and t2.app_dt <= #{appEndDt} ]]>
            </if>
            <if test="ackStartDt != null and ackStartDt != '' ">
                <![CDATA[ and t1.ack_dt >= #{ackStartDt} ]]>
            </if>
            <if test="ackEndDt != null and ackEndDt != '' ">
                <![CDATA[ and t1.ack_dt <= #{ackEndDt} ]]>
            </if>
            <if test="fundTxAcctNoList != null and fundTxAcctNoList.size() > 0">
                and t1.fund_tx_acct_no in
                <foreach item="item" index="index" collection="fundTxAcctNoList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="appStatusList != null and appStatusList.size() > 0">
                and t1.app_status in
                <foreach item="item" index="index" collection="appStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ackStatusList != null and ackStatusList.size() > 0">
                and t1.ack_status in
                <foreach item="item" index="index" collection="ackStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="payStatusList != null and payStatusList.size() > 0">
                and t2.pay_status in
                <foreach item="item" index="index" collection="payStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="submitStatusList != null and submitStatusList.size() > 0">
                and t1.submit_status in
                <foreach item="item" index="index" collection="submitStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="gtAckDtValue != null and gtAckDtValue != '' ">
                <![CDATA[ and t1.ack_dt > #{gtAckDtValue} ]]>
            </if>
            <if test="middleBusiCodeList != null and middleBusiCodeList.size > 0">
                and t1.middle_busi_code in
                <foreach item="item" index="index" collection="middleBusiCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="gtTaTradeDtValue != null and gtTaTradeDtValue != '' ">
                <![CDATA[ and t1.ta_trade_dt > #{gtTaTradeDtValue} ]]>
            </if>
            <if test="taTradeDtRangeStartDt != null and taTradeDtRangeStartDt != '' ">
                <![CDATA[ and t1.ta_trade_dt >= #{taTradeDtRangeStartDt} ]]>
            </if>
            <if test="taTradeDtRangeEndDt != null and taTradeDtRangeEndDt != '' ">
                <![CDATA[ and t1.ta_trade_dt <= #{taTradeDtRangeEndDt} ]]>
            </if>
            and t1.rec_stat = '0'
        </where>
    </select>

</mapper>

