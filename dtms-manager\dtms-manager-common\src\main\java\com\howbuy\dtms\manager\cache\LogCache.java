package com.howbuy.dtms.manager.cache;


import com.google.common.base.Strings;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.howbuy.dtms.manager.log.ProcessLog;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Description:
 * <AUTHOR> jiong.peng
 * @Date: 2024/11/15
*/
public class LogCache {

    private static Cache<String,List<ProcessLog>> cache = CacheBuilder.newBuilder()
            .expireAfterAccess(1, TimeUnit.HOURS).build();

    /**
     * 把日志信息放入内存中
     * @param log
     */
    public static synchronized void put(ProcessLog log) {
        List<ProcessLog> list = cache.getIfPresent(log.getRequestId());
        if(null == list){
            list = new ArrayList<>();
        }
        list.add(log);
        cache.put(log.getRequestId(),list);
    }


    /**
     * 读取已加载到内存的日志信息
     * @param requestId
     * @return
     */
    public static synchronized List<ProcessLog> get(String requestId){
        return cache.getIfPresent(requestId);
    }

    /**
     * 删除
     * @param requestId
     */
    public static synchronized void delete(String requestId){
        if(!Strings.isNullOrEmpty(requestId)){
            cache.invalidate(requestId);
        }
    }
}
