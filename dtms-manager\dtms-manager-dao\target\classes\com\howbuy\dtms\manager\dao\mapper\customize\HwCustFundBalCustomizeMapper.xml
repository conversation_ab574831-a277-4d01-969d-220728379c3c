<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.customize.HwCustFundBalCustomizeMapper">

    <select id="batchQueryCustsWithPosition" resultType="java.lang.String">
        SELECT DISTINCT
            hk_cust_no
        FROM 
            hw_cust_fund_bal
        WHERE 
            hk_cust_no IN
            <foreach collection="hkCustNoList" item="hkCustNo" open="(" separator="," close=")">
                #{hkCustNo,jdbcType=VARCHAR}
            </foreach>
            AND balance_vol > 0
            and fund_code = #{fundCode,jdbcType=VARCHAR}
            AND rec_stat = '1'
    </select>

    <select id="queryPositionCustsByFundCode" resultType="java.lang.String">
        SELECT DISTINCT
            hk_cust_no
        FROM 
            hw_cust_fund_bal
        WHERE 
            fund_code = #{fundCode,jdbcType=VARCHAR}
            AND balance_vol > 0
            AND rec_stat = '1'
    </select>
</mapper> 