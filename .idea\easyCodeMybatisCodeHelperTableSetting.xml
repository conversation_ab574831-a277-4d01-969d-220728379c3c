<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="EasyCodeTableSetting">
    <option name="tableInfoMap">
      <map>
        <entry key="docker_it01_zhzx_0.ac_asset_certificate_file_0">
          <value>
            <TableInfoDTO>
              <option name="comment" value="资产证明文件" />
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="id" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="一账通号" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="hboneNo" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="文件编号" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="fileNo" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="文件路径" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="filePath" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="审核状态，1通过，2不通过" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="auditStatus" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="version" />
                    <option name="type" value="java.lang.Object" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createDate" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="updateDate" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="virtualDbId" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="AcAssetCertificateFile0" />
              <option name="preName" value="" />
              <option name="saveModelName" value="" />
              <option name="savePackageName" value="" />
              <option name="savePath" value="" />
              <option name="templateGroupName" value="" />
            </TableInfoDTO>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>