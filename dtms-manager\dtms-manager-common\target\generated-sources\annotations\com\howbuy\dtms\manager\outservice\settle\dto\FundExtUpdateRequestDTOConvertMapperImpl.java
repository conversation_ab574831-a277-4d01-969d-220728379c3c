package com.howbuy.dtms.manager.outservice.settle.dto;

import com.howbuy.dtms.settle.client.facade.trade.fundext.FundExtUpdateRequest;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:23:42+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class FundExtUpdateRequestDTOConvertMapperImpl implements FundExtUpdateRequestDTOConvertMapper {

    @Override
    public FundExtUpdateRequest convert(FundExtUpdateRequestDTO dto) {
        if ( dto == null ) {
            return null;
        }

        FundExtUpdateRequest fundExtUpdateRequest = new FundExtUpdateRequest();

        fundExtUpdateRequest.setTraceId( dto.getTraceId() );
        fundExtUpdateRequest.setAppDt( dto.getAppDt() );
        fundExtUpdateRequest.setAppTm( dto.getAppTm() );
        fundExtUpdateRequest.setBusiCode( dto.getBusiCode() );
        fundExtUpdateRequest.setDealDtlNo( dto.getDealDtlNo() );
        fundExtUpdateRequest.setExtControlNum( dto.getExtControlNum() );
        fundExtUpdateRequest.setExtControlType( dto.getExtControlType() );
        fundExtUpdateRequest.setExtOption( dto.getExtOption() );
        fundExtUpdateRequest.setFundCode( dto.getFundCode() );
        fundExtUpdateRequest.setFundTxAcctNo( dto.getFundTxAcctNo() );
        fundExtUpdateRequest.setHkCustNo( dto.getHkCustNo() );
        fundExtUpdateRequest.setMiddleOrderNo( dto.getMiddleOrderNo() );
        fundExtUpdateRequest.setPreSubmitTaDt( dto.getPreSubmitTaDt() );
        fundExtUpdateRequest.setSubmitTaDt( dto.getSubmitTaDt() );
        fundExtUpdateRequest.setVolDtlNo( dto.getVolDtlNo() );

        return fundExtUpdateRequest;
    }
}
