<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.customize.HwDealOrderDtlCustomizeMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwDealOrderDtlPO">
        <!--@Table hw_deal_order_dtl-->
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="dealDtlNo" column="deal_dtl_no" jdbcType="BIGINT"/>
        <result property="dealNo" column="deal_no" jdbcType="BIGINT"/>
        <result property="hkCustNo" column="hk_cust_no" jdbcType="VARCHAR"/>
        <result property="cpAcctNo" column="cp_acct_no" jdbcType="VARCHAR"/>
        <result property="fundCode" column="fund_code" jdbcType="VARCHAR"/>
        <result property="fundName" column="fund_name" jdbcType="VARCHAR"/>
        <result property="fundAbbr" column="fund_abbr" jdbcType="VARCHAR"/>
        <result property="mainFundCode" column="main_fund_code" jdbcType="VARCHAR"/>
        <result property="fundCategory" column="fund_category" jdbcType="VARCHAR"/>
        <result property="fundRiskLevel" column="fund_risk_level" jdbcType="VARCHAR"/>
        <result property="redeemType" column="redeem_type" jdbcType="VARCHAR"/>
        <result property="redeemDirectionList" column="redeem_direction_list" jdbcType="VARCHAR"/>
        <result property="middleBusiCode" column="middle_busi_code" jdbcType="VARCHAR"/>
        <result property="appAmt" column="app_amt" jdbcType="NUMERIC"/>
        <result property="netAppAmt" column="net_app_amt" jdbcType="NUMERIC"/>
        <result property="appVol" column="app_vol" jdbcType="NUMERIC"/>
        <result property="estimateFee" column="estimate_fee" jdbcType="NUMERIC"/>
        <result property="prebookDiscount" column="prebook_discount" jdbcType="NUMERIC"/>
        <result property="discountRate" column="discount_rate" jdbcType="NUMERIC"/>
        <result property="discountType" column="discount_type" jdbcType="VARCHAR"/>
        <result property="discountAmt" column="discount_amt" jdbcType="NUMERIC"/>
        <result property="feeRate" column="fee_rate" jdbcType="NUMERIC"/>
        <result property="feeCalMode" column="fee_cal_mode" jdbcType="VARCHAR"/>
        <result property="fee" column="fee" jdbcType="NUMERIC"/>
        <result property="ackAmt" column="ack_amt" jdbcType="NUMERIC"/>
        <result property="ackVol" column="ack_vol" jdbcType="NUMERIC"/>
        <result property="ackNav" column="ack_nav" jdbcType="NUMERIC"/>
        <result property="ackNavDt" column="ack_nav_dt" jdbcType="VARCHAR"/>
        <result property="currency" column="currency" jdbcType="VARCHAR"/>
        <result property="appStatus" column="app_status" jdbcType="VARCHAR"/>
        <result property="ackStatus" column="ack_status" jdbcType="VARCHAR"/>
        <result property="transferPrice" column="transfer_price" jdbcType="NUMERIC"/>
        <result property="fundDivMode" column="fund_div_mode" jdbcType="VARCHAR"/>
        <result property="openDt" column="open_dt" jdbcType="VARCHAR"/>
        <result property="payEndDt" column="pay_end_dt" jdbcType="VARCHAR"/>
        <result property="payEndTm" column="pay_end_tm" jdbcType="VARCHAR"/>
        <result property="taTradeDt" column="ta_trade_dt" jdbcType="VARCHAR"/>
        <result property="ackDt" column="ack_dt" jdbcType="VARCHAR"/>
        <result property="taAckNo" column="ta_ack_no" jdbcType="VARCHAR"/>
        <result property="submitStatus" column="submit_status" jdbcType="VARCHAR"/>
        <result property="preSubmitTaDt" column="pre_submit_ta_dt" jdbcType="VARCHAR"/>
        <result property="preSubmitTaTm" column="pre_submit_ta_tm" jdbcType="VARCHAR"/>
        <result property="fundManCode" column="fund_man_code" jdbcType="VARCHAR"/>
        <result property="extOption" column="ext_option" jdbcType="VARCHAR"/>
        <result property="extControlType" column="ext_control_type" jdbcType="VARCHAR"/>
        <result property="extControlNum" column="ext_control_num" jdbcType="VARCHAR"/>
        <result property="cancelDate" column="cancel_date" jdbcType="VARCHAR"/>
        <result property="cancelCause" column="cancel_cause" jdbcType="VARCHAR"/>
        <result property="cancelCpAcctNo" column="cancel_cp_acct_no" jdbcType="VARCHAR"/>
        <result property="fundTxAcctNo" column="fund_tx_acct_no" jdbcType="VARCHAR"/>
        <result property="intoFundCode" column="into_fund_code" jdbcType="VARCHAR"/>
        <result property="intoMainFundCode" column="into_main_fund_code" jdbcType="VARCHAR"/>
        <result property="intoFundAbbr" column="into_fund_abbr" jdbcType="VARCHAR"/>
        <result property="intoCurrency" column="into_currency" jdbcType="VARCHAR"/>
        <result property="intoAckAmt" column="into_ack_amt" jdbcType="NUMERIC"/>
        <result property="intoAckVol" column="into_ack_vol" jdbcType="NUMERIC"/>
        <result property="intoAckNav" column="into_ack_nav" jdbcType="NUMERIC"/>
        <result property="intoAckNavDt" column="into_ack_nav_dt" jdbcType="VARCHAR"/>
        <result property="intoFundTxAcctNo" column="into_fund_tx_acct_no" jdbcType="VARCHAR"/>
        <result property="relationalDealDtlNo" column="relational_deal_dtl_no" jdbcType="BIGINT"/>
        <result property="volDtlNo" column="vol_dtl_no" jdbcType="VARCHAR"/>
        <result property="subAmt" column="sub_amt" jdbcType="NUMERIC"/>
        <result property="recStat" column="rec_stat" jdbcType="VARCHAR"/>
        <result property="createTimestamp" column="create_timestamp" jdbcType="TIMESTAMP"/>
        <result property="updateTimestamp" column="update_timestamp" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="CustDealOrderAppResultMap" type="com.howbuy.dtms.manager.dao.bo.CustDealOrderAppBO" extends="BaseResultMap">
        <result property="custChineseName" column="cust_chinese_name" jdbcType="VARCHAR"/>
        <result property="idNoMask" column="id_no_mask" jdbcType="VARCHAR"/>
        <result property="paymentTypeList"  column="payment_type_list" jdbcType="VARCHAR" />
        <result property="payStatus" column="pay_status" jdbcType="VARCHAR" />
        <result property="appDt" column="app_dt" jdbcType="VARCHAR" />
        <result property="appTm" column="app_tm" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
        t.deal_dtl_no,
        t.deal_no,
        t.hk_cust_no,
        t.cp_acct_no,
        t.fund_code,
        t.fund_name,
        t.fund_abbr,
        t.main_fund_code,
        t.fund_category,
        t.fund_risk_level,
        t.redeem_type,
        t.redeem_direction_list,
        t.middle_busi_code,
        t.app_amt,
        t.net_app_amt,
        t.app_vol,
        t.estimate_fee,
        t.prebook_discount,
        t.discount_rate,
        t.discount_type,
        t.discount_amt,
        t.fee_rate,
        t.fee_cal_mode,
        t.fee,
        t.ack_amt,
        t.ack_vol,
        t.ack_nav,
        t.ack_nav_dt,
        t.currency,
        t.app_status,
        t.ack_status,
        t.transfer_price,
        t.fund_div_mode,
        t.open_dt,
        t.pay_end_dt,
        t.pay_end_tm,
        t.ta_trade_dt,
        t.ack_dt,
        t.ta_ack_no,
        t.submit_status,
        t.pre_submit_ta_dt,
        t.pre_submit_ta_tm,
        t.fund_man_code,
        t.ext_option,
        t.ext_control_type,
        t.ext_control_num,
        t.cancel_date,
        t.cancel_cause,
        t.cancel_cp_acct_no,
        t.fund_tx_acct_no,
        t.into_fund_code,
        t.into_main_fund_code,
        t.into_fund_abbr,
        t.into_currency,
        t.into_ack_amt,
        t.into_ack_vol,
        t.into_ack_nav,
        t.into_ack_nav_dt,
        t.into_fund_tx_acct_no,
        t.relational_deal_dtl_no,
        t.vol_dtl_no,
        t.sub_amt,
        t.rec_stat,
        t.create_timestamp,
        t.update_timestamp
    </sql>

    <sql id="comme_where">
        <where>
            and t.rec_stat = '0'
            and o.rec_stat = '0'
            <if test="id != null">
                and t.id = #{id}
            </if>
            <if test="dealDtlNo != null">
                and t.deal_dtl_no = #{dealDtlNo}
            </if>
            <if test="dealNo != null">
                and t.deal_no = #{dealNo}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                and t.hk_cust_no = #{hkCustNo}
            </if>
            <if test="cpAcctNo != null and cpAcctNo != ''">
                and t.cp_acct_no = #{cpAcctNo}
            </if>
            <if test="fundCode != null and fundCode != ''">
                and t.fund_code = #{fundCode}
            </if>
            <if test="fundName != null and fundName != ''">
                and t.fund_name = #{fundName}
            </if>
            <if test="fundAbbr != null and fundAbbr != ''">
                and t.fund_abbr = #{fundAbbr}
            </if>
            <if test="mainFundCode != null and mainFundCode != ''">
                and t.main_fund_code = #{mainFundCode}
            </if>
            <if test="fundCategory != null and fundCategory != ''">
                and t.fund_category = #{fundCategory}
            </if>
            <if test="fundRiskLevel != null and fundRiskLevel != ''">
                and t.fund_risk_level = #{fundRiskLevel}
            </if>
            <if test="redeemType != null and redeemType != ''">
                and t.redeem_type = #{redeemType}
            </if>
            <if test="redeemDirectionList != null and redeemDirectionList != ''">
                and t.redeem_direction_list = #{redeemDirectionList}
            </if>
            <if test="middleBusiCode != null and middleBusiCode != ''">
                and t.middle_busi_code = #{middleBusiCode}
            </if>
            <if test="appAmt != null">
                and t.app_amt = #{appAmt}
            </if>
            <if test="netAppAmt != null">
                and t.net_app_amt = #{netAppAmt}
            </if>
            <if test="appVol != null">
                and t.app_vol = #{appVol}
            </if>
            <if test="estimateFee != null">
                and t.estimate_fee = #{estimateFee}
            </if>
            <if test="prebookDiscount != null">
                and t.prebook_discount = #{prebookDiscount}
            </if>
            <if test="discountRate != null">
                and t.discount_rate = #{discountRate}
            </if>
            <if test="feeRate != null">
                and t.fee_rate = #{feeRate}
            </if>
            <if test="feeCalMode != null and feeCalMode != ''">
                and t.fee_cal_mode = #{feeCalMode}
            </if>
            <if test="fee != null">
                and t.fee = #{fee}
            </if>
            <if test="ackAmt != null">
                and t.ack_amt = #{ackAmt}
            </if>
            <if test="ackVol != null">
                and t.ack_vol = #{ackVol}
            </if>
            <if test="ackNav != null">
                and t.ack_nav = #{ackNav}
            </if>
            <if test="ackNavDt != null and ackNavDt != ''">
                and t.ack_nav_dt = #{ackNavDt}
            </if>
            <if test="currency != null and currency != ''">
                and t.currency = #{currency}
            </if>
            <if test="appStatus != null and appStatus != ''">
                and t.app_status = #{appStatus}
            </if>
            <if test="ackStatus != null and ackStatus != ''">
                and t.ack_status = #{ackStatus}
            </if>
            <if test="transferPrice != null">
                and t.transfer_price = #{transferPrice}
            </if>
            <if test="fundDivMode != null and fundDivMode != ''">
                and t.fund_div_mode = #{fundDivMode}
            </if>
            <if test="openDt != null and openDt != ''">
                and t.open_dt = #{openDt}
            </if>
            <if test="payEndDt != null and payEndDt != ''">
                and t.pay_end_dt = #{payEndDt}
            </if>
            <if test="payEndTm != null and payEndTm != ''">
                and t.pay_end_tm = #{payEndTm}
            </if>
            <if test="taTradeDt != null and taTradeDt != ''">
                and t.ta_trade_dt = #{taTradeDt}
            </if>
            <if test="ackDt != null and ackDt != ''">
                and t.ack_dt = #{ackDt}
            </if>
            <if test="taAckNo != null and taAckNo != ''">
                and t.ta_ack_no = #{taAckNo}
            </if>
            <if test="submitStatus != null and submitStatus != ''">
                and t.submit_status = #{submitStatus}
            </if>
            <if test="preSubmitTaDt != null and preSubmitTaDt != ''">
                and t.pre_submit_ta_dt = #{preSubmitTaDt}
            </if>
            <if test="preSubmitTaTm != null and preSubmitTaTm != ''">
                and t.pre_submit_ta_tm = #{preSubmitTaTm}
            </if>
            <if test="fundManCode != null and fundManCode != ''">
                and t.fund_man_code = #{fundManCode}
            </if>
            <if test="extOption != null and extOption != ''">
                and t.ext_option = #{extOption}
            </if>
            <if test="extControlType != null and extControlType != ''">
                and t.ext_control_type = #{extControlType}
            </if>
            <if test="extControlNum != null and extControlNum != ''">
                and t.ext_control_num = #{extControlNum}
            </if>
            <if test="cancelDate != null and cancelDate != ''">
                and t.cancel_date = #{cancelDate}
            </if>
            <if test="cancelCause != null and cancelCause != ''">
                and t.cancel_cause = #{cancelCause}
            </if>
            <if test="cancelCpAcctNo != null and cancelCpAcctNo != ''">
                and t.cancel_cp_acct_no = #{cancelCpAcctNo}
            </if>
            <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
                and t.fund_tx_acct_no = #{fundTxAcctNo}
            </if>
            <if test="intoFundCode != null and intoFundCode != ''">
                and t.into_fund_code = #{intoFundCode}
            </if>
            <if test="intoCurrency != null and intoCurrency != ''">
                and t.into_currency = #{intoCurrency}
            </if>
            <if test="intoFundAbbr != null and intoFundAbbr != ''">
                and t.into_fund_abbr = #{intoFundAbbr}
            </if>
            <if test="intoAckAmt != null">
                and t.into_ack_amt = #{intoAckAmt}
            </if>
            <if test="intoAckVol != null">
                and t.into_ack_vol = #{intoAckVol}
            </if>
            <if test="intoAckNav != null">
                and t.into_ack_nav = #{intoAckNav}
            </if>
            <if test="intoAckNavDt != null and intoAckNavDt != ''">
                and t.into_ack_nav_dt = #{intoAckNavDt}
            </if>
            <if test="intoFundTxAcctNo != null and intoFundTxAcctNo != ''">
                and t.into_fund_tx_acct_no = #{intoFundTxAcctNo}
            </if>
            <if test="relationalDealDtlNo != null">
                and t.relational_deal_dtl_no = #{relationalDealDtlNo}
            </if>
            <if test="recStat != null and recStat != ''">
                and t.rec_stat = #{recStat}
            </if>
            <if test="volDtlNo != null and volDtlNo != ''">
                and t.vol_dtl_no = #{volDtlNo}
            </if>
            <if test="createTimestamp != null">
                and t.create_timestamp = #{createTimestamp}
            </if>
            <if test="updateTimestamp != null">
                and t.update_timestamp = #{updateTimestamp}
            </if>
            <if test="orderStatusList != null and orderStatusList.size() != 0">
                and o.order_status in
                <foreach item="item" index="index" collection="orderStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="idList != null and idList.size > 0">
                and t.id in
                <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="fundCodeList != null and fundCodeList.size > 0">
                and t.fund_code in
                <foreach item="item" index="index" collection="fundCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="middleBusiCodeList != null and middleBusiCodeList.size > 0">
                and t.middle_busi_code in
                <foreach item="item" index="index" collection="middleBusiCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="appStatusList != null and appStatusList.size > 0">
                and t.app_status in
                <foreach item="item" index="index" collection="appStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ackStatusList != null and ackStatusList.size > 0">
                and t.ack_status in
                <foreach item="item" index="index" collection="ackStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="submitStatusList != null and submitStatusList.size > 0">
                and t.submit_status in
                <foreach item="item" index="index" collection="submitStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="redeemTypeList != null and redeemTypeList.size > 0">
                and t.redeem_type in
                <foreach item="item" index="index" collection="redeemTypeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="fundManCodeList != null and fundManCodeList.size > 0">
                and t.fund_man_code in
                <foreach item="item" index="index" collection="fundManCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="preSubmitTaDtRangeStart != null and preSubmitTaDtRangeStart != '' ">
                <![CDATA[
                and t.pre_submit_ta_dt >= #{preSubmitTaDtRangeStart}
                ]]>
            </if>
            <if test="preSubmitTaDtRangeEnd != null and preSubmitTaDtRangeEnd != '' ">
                <![CDATA[
                and t.pre_submit_ta_dt <= #{preSubmitTaDtRangeEnd}
                ]]>
            </if>
            <if test="openDtRangeStart != null and openDtRangeStart != '' ">
                <![CDATA[
                and t.open_dt >= #{openDtRangeStart}
                ]]>
            </if>
            <if test="openDtRangeEnd != null and openDtRangeEnd != '' ">
                <![CDATA[
                and t.open_dt <= #{openDtRangeEnd}
                ]]>
            </if>
            <if test="redeemDirectionExList != null and redeemDirectionExList.size > 0">
                and t.redeem_direction_list in
                <foreach item="item" index="index" collection="redeemDirectionExList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=' isPiggyFund == "1" and piggyFundCodeList != null and piggyFundCodeList.size > 0'>
                and t.fund_code in
                <foreach item="item" index="index" collection="piggyFundCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=' isPiggyFund == "0" and piggyFundCodeList != null and piggyFundCodeList.size > 0'>
                and t.fund_code not in
                <foreach item="item" index="index" collection="piggyFundCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="paymentTypeList != null and paymentTypeList != ''">
                and o.payment_type_list = #{paymentTypeList}
            </if>
            <if test="payStatus != null and payStatus != ''">
                and o.pay_status = #{payStatus}
            </if>
            <if test="payStatusList != null and payStatusList.size > 0">
                and o.pay_status in
                <foreach item="item" index="index" collection="payStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>
    <!--分页查询，根据主键ID升序排序-->
    <select id="queryCustDealOrderAppList" parameterType="com.howbuy.dtms.manager.dao.query.HwDealOrderDtlQuery"
            resultMap="CustDealOrderAppResultMap">
        select
        <include refid="Base_Column_List"/>,o.id_no_mask,o.cust_chinese_name,o.payment_type_list,o.pay_status,o.app_dt,o.app_tm
        from hw_deal_order_dtl t
        left join hw_deal_order o on t.deal_no = o.deal_no
        <include refid="comme_where" />
        order by t.pre_submit_ta_dt desc, t.hk_cust_no, t.middle_busi_code, t.fund_code, t.currency, t.redeem_type, t.deal_dtl_no, t.id
    </select>
    <select id="summary" parameterType="com.howbuy.dtms.manager.dao.query.HwDealOrderDtlQuery"
            resultType="com.howbuy.dtms.manager.dao.bo.HwDealOrderDtlSummaryBO">
        select
        count(*) as totalCount,
        sum(ifnull(t.net_app_amt, 0)) as totalNetAppAmt,
        sum(ifnull(t.app_amt,  0)) as totalAppAmt,
        sum(ifnull(t.app_vol, 0)) as totalAppVol
        from hw_deal_order_dtl t
        left join hw_deal_order o on t.deal_no = o.deal_no
        <include refid="comme_where" />
    </select>

    <select id="queryCounterCanRevokeTradeOrder" resultMap="com.howbuy.dtms.manager.dao.mapper.HwDealOrderMapper.UnionResultMap">
        select <include refid="com.howbuy.dtms.manager.dao.mapper.HwDealOrderMapper.Union_Column_List"/>
        from hw_deal_order a
                 left join hw_deal_order_dtl b on
            a.deal_no = b.deal_no
        where b.middle_busi_code in ('1120', '1122', '1124', '1136', '112A', '112B')
          and a.hk_cust_no = #{hkCustNo, jdbcType=VARCHAR}
        <if test="fundTxAcctNo != null and fundTxAcctNo != ''">
            and b.fund_tx_acct_no = #{fundTxAcctNo, jdbcType=VARCHAR}
        </if>
          and a.order_status = '1'
          and b.submit_status in ('0','3','4')
          and a.rec_stat = '0'
          and b.rec_stat = '0'
    </select>

    <select id="queryCounterRevokeTradeOrderByDealNo" resultMap="com.howbuy.dtms.manager.dao.mapper.HwDealOrderMapper.UnionResultMap">
        select <include refid="com.howbuy.dtms.manager.dao.mapper.HwDealOrderMapper.Union_Column_List"/>
        from hw_deal_order a
                 left join hw_deal_order_dtl b on
            a.deal_no = b.deal_no
        where b.middle_busi_code in ('1120', '1122', '1124', '1136', '112A', '112B')
          and a.rec_stat = '0'
          and b.rec_stat = '0'
          and a.deal_no = #{dealNo, jdbcType=VARCHAR}
    </select>
    <select id="queryCrmRevokeTradeOrderByPreDealNo" resultMap="com.howbuy.dtms.manager.dao.mapper.HwDealOrderMapper.UnionResultMap">
        select <include refid="com.howbuy.dtms.manager.dao.mapper.HwDealOrderMapper.Union_Column_List"/>
        from hw_deal_order a
                 left join hw_deal_order_dtl b on
            a.deal_no = b.deal_no
        where a.rec_stat = '0'
          and b.rec_stat = '0'
          and a.prebook_deal_no = #{preDealNo}
    </select>
</mapper>

