package com.howbuy.dtms.manager.enums.auditorder.execute;

public enum CounterOrderExecuteEnum {
    COMMON_COUNTER_ORDER_AUDIT_PASS_EXECUTE("COMMON_COUNTER_ORDER_AUDIT_PASS_EXECUTE", "通用审核通过执行器"),

    SUBS_REVISIT_AUDIT_PASS_EXECUTE("SUBS_REVISIT_AUDIT_PASS_EXECUTE", "认申购回访审核通过执行器"),

    REDEEM_AUDIT_PASS_WAIT_REVISIT_EXECUTE("REDEEM_AUDIT_PASS_WAIT_REVISIT_EXECUTE", "赎回回访审核通过执行器"),

    REDEEM_COUNTER_ORDER_AUDIT_PASS_EXECUTE("REDEEM_COUNTER_ORDER_AUDIT_PASS_EXECUTE", "赎回审核通过执行器"),

    REVOKE_COUNTER_ORDER_AUDIT_PASS_EXECUTE("REVOKE_COUNTER_ORDER_AUDIT_PASS_EXECUTE", "交易撤单审核通过执行器"),

    REVOKE_AUDIT_PASS_WAIT_REVISIT_EXECUTE("REVOKE_AUDIT_PASS_WAIT_REVISIT_EXECUTE", "交易撤单回访审核通过执行器"),

    EXTENSION_COUNTER_ORDER_AUDIT_PASS_EXECUTE("EXTENSION_COUNTER_ORDER_AUDIT_PASS_EXECUTE", "展期修改审核通过执行器"),

    EXTENSION_AUDIT_PASS_WAIT_REVISIT_EXECUTE("EXTENSION_AUDIT_PASS_WAIT_REVISIT_EXECUTE", "展期修改回访审核通过执行器"),

    FUND_TRANSFER_COUNTER_ORDER_AUDIT_PASS_EXECUTE("FUND_TRANSFER_COUNTER_ORDER_AUDIT_PASS_EXECUTE", "基金转换审核通过执行器"),
    FUND_TRANSFER_AUDIT_PASS_WAIT_REVISIT_EXECUTE("FUND_TRANSFER_AUDIT_PASS_WAIT_REVISIT_EXECUTE", "基金转换回访审核通过执行器"),

    FORCE_SUBTRACT_COUNTER_ORDER_AUDIT_PASS_EXECUTE("FORCE_SUBTRACT_COUNTER_ORDER_AUDIT_PASS_EXECUTE", "强减柜台审核通过执行器"),

    FORCE_ADD_COUNTER_ORDER_AUDIT_PASS_EXECUTE("FORCE_ADD_COUNTER_ORDER_AUDIT_PASS_EXECUTE", "强增柜台审核通过执行器"),

    BATCH_PAID_COUNTER_ORDER_AUDIT_PASS_EXECUTE("BATCH_PAID_COUNTER_ORDER_AUDIT_PASS_EXECUTE", "批量实缴柜台审核通过执行器"),

    FULL_BATCH_SUBSCRIBE_COUNTER_ORDER_AUDIT_PASS_EXECUTE("FULL_BATCH_SUBSCRIBE_COUNTER_ORDER_AUDIT_PASS_EXECUTE", "全委专户批量认申购柜台审核通过执行器"),

    FULL_BATCH_REDEEM_COUNTER_ORDER_AUDIT_PASS_EXECUTE("FULL_BATCH_REDEEM_COUNTER_ORDER_AUDIT_PASS_EXECUTE", "全委专户批量赎回柜台审核通过执行器"),

    COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE("COMMON_COUNTER_ORDER_AUDIT_NOT_PASS_EXECUTE", "通用审核不通过执行器"),

    PURCHASE_COUNTER_ORDER_SUBMIT("PURCHASE_COUNTER_ORDER_SUBMIT", "认购柜台下单执行器"),

    REDEEM_COUNTER_ORDER_SUBMIT("REDEEM_COUNTER_ORDER_SUBMIT", "赎回柜台下单执行器"),

    REVOKE_COUNTER_ORDER_SUBMIT("REVOKE_COUNTER_ORDER_SUBMIT", "交易撤单柜台下单执行器"),

    EXTENSION_COUNTER_ORDER_SUBMIT("EXTENSION_COUNTER_ORDER_SUBMIT", "展期修改柜台下单执行器"),

    FUND_TRANSFER_COUNTER_ORDER_SUBMIT("FUND_TRANSFER_COUNTER_ORDER_SUBMIT", "基金修改柜台下单执行器"),

    FORCE_SUBTRACT_COUNTER_ORDER_SUBMIT_EXECUTE("FORCE_SUBTRACT_COUNTER_ORDER_SUBMIT_EXECUTE", "强减柜台下单执行器"),

    FORCE_ADD_COUNTER_ORDER_SUBMIT_EXECUTE("FORCE_ADD_COUNTER_ORDER_SUBMIT_EXECUTE", "强增柜台下单执行器"),

    BATCH_PAID_COUNTER_ORDER_SUBMIT_EXECUTE("BATCH_PAID_COUNTER_ORDER_SUBMIT_EXECUTE", "批量实缴柜台下单执行器"),

    FULL_BATCH_SUBSCRIBE_COUNTER_ORDER_SUBMIT_EXECUTE("FULL_BATCH_SUBSCRIBE_COUNTER_ORDER_SUBMIT_EXECUTE", "全委专户批量认申购柜台下单执行器"),

    FULL_BATCH_REDEEM_COUNTER_ORDER_SUBMIT_EXECUTE("FULL_BATCH_REDEEM_COUNTER_ORDER_SUBMIT_EXECUTE", "全委专户批量赎回柜台下单执行器"),

    CRM_COUNTER_ORDER_FIRST_AUDIT_NOT_PASS_EXECUTE("CRM_COUNTER_ORDER_FIRST_AUDIT_NOT_PASS_EXECUTE", "CRM柜台认申购/赎回，初审审核不通过执行器"),

    CRM_COUNTER_ORDER_FS_AUDIT_NOT_PASS_EXECUTE("CRM_COUNTER_ORDER_FS_AUDIT_NOT_PASS_EXECUTE", "CRM柜台认申购/赎回，复审审核不通过执行器"),

    CRM_COUNTER_ORDER_REVISIT_AUDIT_NOT_PASS_EXECUTE("CRM_COUNTER_ORDER_REVISIT_AUDIT_NOT_PASS_EXECUTE", "CRM柜台认申购/赎回，回访审核不通过执行器"),
    CRM_SUBS_FS_AUDIT_PASS_EXECUTE("CRM_COUNTER_ORDER_FS_AUDIT_PASS_EXECUTE", "CRM柜台认申购，复审审核通过执行器"),

    CRM_REDEEM_FS_AUDIT_PASS_EXECUTE("CRM_COUNTER_ORDER_REDEEM_FS_AUDIT_PASS_EXECUTE", "CRM柜台赎回，复审审核通过执行器"),

    CRM_REVOKE_FS_AUDIT_PASS_EXECUTE("CRM_REVOKE_FS_AUDIT_PASS_EXECUTE", "CRM柜台赎回，复审审核通过执行器"),

    CRM_COUNTER_ORDER_REVISIT_AUDIT_PASS_EXECUTE("CRM_COUNTER_ORDER_REVISIT_AUDIT_PASS_EXECUTE", "CRM柜台认申购，回访审核通过执行器"),

    CRM_COUNTER_ORDER_REDEEM_REVISIT_AUDIT_PASS_EXECUTE("CRM_COUNTER_ORDER_REDEEM_REVISIT_AUDIT_PASS_EXECUTE", "CRM柜台认赎回，回访审核通过执行器"),

    CRM_REVOKE_REVISIT_AUDIT_PASS_EXECUTE("CRM_REVOKE_REVISIT_AUDIT_PASS_EXECUTE", "CRM柜台撤单，回访审核通过执行器"),
    FUND_TX_ACCT_NO_OPEN_COUNTER_ORDER_SUBMIT_EXECUTE("FUND_TX_ACCT_NO_OPEN_COUNTER_ORDER_SUBMIT_EXECUTE", "基金交易账号开通执行器"),

    FUND_TX_ACCT_NO_OPEN_AUDIT_PASS_EXECUTE("FUND_TX_ACCT_NO_OPEN_AUDIT_PASS_EXECUTE", "基金交易账号开通审核通过执行器");
    private final String code;

    private final String value;

    CounterOrderExecuteEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
