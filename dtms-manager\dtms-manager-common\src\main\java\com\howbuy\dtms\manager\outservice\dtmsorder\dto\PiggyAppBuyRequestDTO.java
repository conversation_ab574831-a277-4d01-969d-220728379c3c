package com.howbuy.dtms.manager.outservice.dtmsorder.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/14 17:48
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PiggyAppBuyRequestDTO extends DtmsOrderBaseRequestDTO{

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 支付方式  1-电汇、2-支票、3-海外储蓄罐
     */
    private String payMethod;

    /**
     * 买入金额
     */
    private BigDecimal buyAmt;

    /**
     * 预估手续费
     */
    private BigDecimal estimateFee;

    /**
     * 折扣率
     */
    private BigDecimal discountRate;

    /**
     * 关联订单号
     */
    private String relationalDealNo;

    /**
     * 操作人
     */
    private String operator;

}
