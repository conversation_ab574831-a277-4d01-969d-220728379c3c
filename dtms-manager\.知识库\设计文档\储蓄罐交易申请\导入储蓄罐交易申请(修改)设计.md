## 导入储蓄罐交易申请(修改)

- 请求地址

|                                 |      |                                          |                    |
| ------------------------------- | ---- | ---------------------------------------- | ------------------ |
| ImportHwPiggyTradeAppController | http | /counter/importHwPiggyTradeApp/importApp | 导入储蓄罐交易申请 |

- 修改逻辑(ImportHwPiggyTradeAppService#process())：
  - 导入的文件在购买金额列后面增加了一列：折扣率，相关文件解析、对账转换进行相应的修改
  - 字段校验针对认申购业务增加折扣率的必填校验
  - 构建PO对象
    - 折扣率字段的赋值
    - 储蓄罐申请来源字段的赋值-默认为PiggyAppSourceEnum.EXCEL.getCode
  - 项目中HwPiggyTradeAppImportPO.isGenerated的所有设置、比较的地方使用的枚举YesOrNoEnum替换为PiggyTradeAppGenerateEnum