/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

/**
 * <AUTHOR>
 * @description: 确认状态 0-无需确认；1-未确认；2-确认中；3-部分确认；4-确认成功；5-确认失败
 * @date 2024/4/18 14:32
 * @since JDK 1.8
 */
public enum AckStatusEnum {

    NO_NEED_CONFIRM("0", "无需确认"),
    UN_CONFIRM("1", "未确认"),
    CONFIRM_ING("2", "确认中"),
    PART_CONFIRM("3", "部分确认"),
    CONFIRM_SUCCESS("4", "确认成功"),
    CONFIRM_FAIL("5", "确认失败");

    private String code;
    private String desc;

    AckStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static AckStatusEnum getByCode(String code) {
        AckStatusEnum[] ackStatusEnums = values();

        for (int i = 0; i < ackStatusEnums.length; ++i) {
            AckStatusEnum ackStatusEnum = ackStatusEnums[i];
            if (ackStatusEnum.getCode().equals(code)) {
                return ackStatusEnum;
            }
        }

        return null;
    }

}
