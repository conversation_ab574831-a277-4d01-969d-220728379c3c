<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.HwCounterRevisitConfMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.dtms.manager.dao.po.HwCounterRevisitConfPO">
    <!--@mbg.generated-->
    <!--@Table hw_counter_revisit_conf-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dictionary_code" jdbcType="VARCHAR" property="dictionaryCode" />
    <result column="dictionary_name" jdbcType="VARCHAR" property="dictionaryName" />
    <result column="biz_type_code" jdbcType="VARCHAR" property="bizTypeCode" />
    <result column="biz_type_name" jdbcType="VARCHAR" property="bizTypeName" />
    <result column="trade_channel" jdbcType="VARCHAR" property="tradeChannel" />
    <result column="self_account_revisit" jdbcType="VARCHAR" property="selfAccountRevisit" />
    <result column="full_fund_tx_acct_revisit" jdbcType="CHAR" property="fullFundTxAcctRevisit" />
    <result column="rec_stat" jdbcType="VARCHAR" property="recStat" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_timestamp" jdbcType="TIMESTAMP" property="createTimestamp" />
    <result column="update_timestamp" jdbcType="TIMESTAMP" property="updateTimestamp" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, dictionary_code, dictionary_name, biz_type_code, biz_type_name, trade_channel, 
    self_account_revisit, full_fund_tx_acct_revisit, rec_stat, creator, modifier, create_timestamp, 
    update_timestamp
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from hw_counter_revisit_conf
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from hw_counter_revisit_conf
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.HwCounterRevisitConfPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hw_counter_revisit_conf (dictionary_code, dictionary_name, biz_type_code, 
      biz_type_name, trade_channel, self_account_revisit, 
      full_fund_tx_acct_revisit, rec_stat, creator, 
      modifier, create_timestamp, update_timestamp
      )
    values (#{dictionaryCode,jdbcType=VARCHAR}, #{dictionaryName,jdbcType=VARCHAR}, #{bizTypeCode,jdbcType=VARCHAR}, 
      #{bizTypeName,jdbcType=VARCHAR}, #{tradeChannel,jdbcType=VARCHAR}, #{selfAccountRevisit,jdbcType=VARCHAR}, 
      #{fullFundTxAcctRevisit,jdbcType=CHAR}, #{recStat,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{createTimestamp,jdbcType=TIMESTAMP}, #{updateTimestamp,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.manager.dao.po.HwCounterRevisitConfPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hw_counter_revisit_conf
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dictionaryCode != null">
        dictionary_code,
      </if>
      <if test="dictionaryName != null">
        dictionary_name,
      </if>
      <if test="bizTypeCode != null">
        biz_type_code,
      </if>
      <if test="bizTypeName != null">
        biz_type_name,
      </if>
      <if test="tradeChannel != null">
        trade_channel,
      </if>
      <if test="selfAccountRevisit != null">
        self_account_revisit,
      </if>
      <if test="fullFundTxAcctRevisit != null">
        full_fund_tx_acct_revisit,
      </if>
      <if test="recStat != null">
        rec_stat,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="createTimestamp != null">
        create_timestamp,
      </if>
      <if test="updateTimestamp != null">
        update_timestamp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dictionaryCode != null">
        #{dictionaryCode,jdbcType=VARCHAR},
      </if>
      <if test="dictionaryName != null">
        #{dictionaryName,jdbcType=VARCHAR},
      </if>
      <if test="bizTypeCode != null">
        #{bizTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="bizTypeName != null">
        #{bizTypeName,jdbcType=VARCHAR},
      </if>
      <if test="tradeChannel != null">
        #{tradeChannel,jdbcType=VARCHAR},
      </if>
      <if test="selfAccountRevisit != null">
        #{selfAccountRevisit,jdbcType=VARCHAR},
      </if>
      <if test="fullFundTxAcctRevisit != null">
        #{fullFundTxAcctRevisit,jdbcType=CHAR},
      </if>
      <if test="recStat != null">
        #{recStat,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTimestamp != null">
        #{updateTimestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.dtms.manager.dao.po.HwCounterRevisitConfPO">
    <!--@mbg.generated-->
    update hw_counter_revisit_conf
    <set>
      <if test="dictionaryCode != null">
        dictionary_code = #{dictionaryCode,jdbcType=VARCHAR},
      </if>
      <if test="dictionaryName != null">
        dictionary_name = #{dictionaryName,jdbcType=VARCHAR},
      </if>
      <if test="bizTypeCode != null">
        biz_type_code = #{bizTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="bizTypeName != null">
        biz_type_name = #{bizTypeName,jdbcType=VARCHAR},
      </if>
      <if test="tradeChannel != null">
        trade_channel = #{tradeChannel,jdbcType=VARCHAR},
      </if>
      <if test="selfAccountRevisit != null">
        self_account_revisit = #{selfAccountRevisit,jdbcType=VARCHAR},
      </if>
      <if test="fullFundTxAcctRevisit != null">
        full_fund_tx_acct_revisit = #{fullFundTxAcctRevisit,jdbcType=CHAR},
      </if>
      <if test="recStat != null">
        rec_stat = #{recStat,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTimestamp != null">
        update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.dtms.manager.dao.po.HwCounterRevisitConfPO">
    <!--@mbg.generated-->
    update hw_counter_revisit_conf
    set dictionary_code = #{dictionaryCode,jdbcType=VARCHAR},
      dictionary_name = #{dictionaryName,jdbcType=VARCHAR},
      biz_type_code = #{bizTypeCode,jdbcType=VARCHAR},
      biz_type_name = #{bizTypeName,jdbcType=VARCHAR},
      trade_channel = #{tradeChannel,jdbcType=VARCHAR},
      self_account_revisit = #{selfAccountRevisit,jdbcType=VARCHAR},
      full_fund_tx_acct_revisit = #{fullFundTxAcctRevisit,jdbcType=CHAR},
      rec_stat = #{recStat,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
      update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>