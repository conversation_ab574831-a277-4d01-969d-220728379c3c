package com.howbuy.dtms.manager.outservice.settle.dto;

import com.howbuy.dtms.settle.client.facade.batch.UpdateChannelAppCheckResultRequest;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:23:42+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class UpdateChannelAppCheckResultRequestDTOConvertMapperImpl implements UpdateChannelAppCheckResultRequestDTOConvertMapper {

    @Override
    public UpdateChannelAppCheckResultRequest convert(UpdateChannelAppCheckResultRequestDTO dto) {
        if ( dto == null ) {
            return null;
        }

        UpdateChannelAppCheckResultRequest updateChannelAppCheckResultRequest = new UpdateChannelAppCheckResultRequest();

        updateChannelAppCheckResultRequest.setTraceId( dto.getTraceId() );
        updateChannelAppCheckResultRequest.setCountDt( dto.getCountDt() );
        updateChannelAppCheckResultRequest.setFundCode( dto.getFundCode() );
        updateChannelAppCheckResultRequest.setTotalAppAmt( dto.getTotalAppAmt() );
        updateChannelAppCheckResultRequest.setTotalAppVol( dto.getTotalAppVol() );
        updateChannelAppCheckResultRequest.setTotalCount( dto.getTotalCount() );
        updateChannelAppCheckResultRequest.setTotalNetAppAmt( dto.getTotalNetAppAmt() );

        return updateChannelAppCheckResultRequest;
    }
}
