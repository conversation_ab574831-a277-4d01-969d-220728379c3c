package com.howbuy.dtms.manager.counter.request.hwpiggytradeappimport;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 修改储蓄罐交易申请购买金额请求参数
 *
 * <AUTHOR>
 * @date 2025-07-21 19:56:48
 * @since JDK 1.8
 */
@Getter
@Setter
public class UpdatePiggyTradeAppBuyAmtRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 买入金额
     */
    private String buyAmt;

    /**
     * 折扣率
     */
    private String discountRate;

    /**
     * 手续费
     */
    private String fee;

    /**
     * 备注
     */
    private String remark;
}
