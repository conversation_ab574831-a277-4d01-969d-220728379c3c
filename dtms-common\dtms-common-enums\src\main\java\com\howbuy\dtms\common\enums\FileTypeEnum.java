/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.enums;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/10 18:05
 * @since JDK 1.8
 */
public enum FileTypeEnum {
    BUY_CONTRACT_SIGN_FILE_TYPE("T1", "买入合同签署文件类型"),
    SELL_CONTRACT_SIGN_FILE_TYPE("T2", "卖出合同签署文件类型"),

    SUB_CONTRACT_SIGN_FILE_TYPE("T3", "认缴实缴合同签署文件类型"),

    ;

    private final String  code;
    private final String desc;

    FileTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
