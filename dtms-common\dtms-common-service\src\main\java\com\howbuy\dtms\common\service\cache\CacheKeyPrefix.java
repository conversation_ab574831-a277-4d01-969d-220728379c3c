/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.common.service.cache;

/**
 * @description: 缓存key前缀
 * <AUTHOR>
 * @date 2023/3/8 17:41
 * @since JDK 1.8
 */
public class CacheKeyPrefix {
    private CacheKeyPrefix() {
    }

    /**
     * 缓存锁key前缀
     */
    public static final String LOCK_KEY_PREFIX = "IDEMPOTENT_KEY|";
}