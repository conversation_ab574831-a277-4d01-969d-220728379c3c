---
description: Controller层api和接口文档实现规则
globs: *.java, *.md
alwaysApply: false
---
---
description: DTMS Counter控制器设计与APIDOC文档生成规范 (基于CounterOrderCommonController实现)
globs: 
  - "dtms-manager-counter/src/main/java/com/howbuy/dtms/manager/counter/controller/**/*Controller.java"
---

# DTMS Counter控制器与APIDOC文档生成规范

> 本规范基于`CounterOrderCommonController`的实现，总结了Controller层接口设计与APIDOC文档生成的最佳实践。
> 作者: hongdong.xie
> 日期: 2025-03-07 18:26:59

## 1. Controller类规范

### 1.1 类命名规范
- 控制器类名应使用`{业务域}[{子域}]Controller`格式，采用PascalCase命名法
- 业务域指明主要业务领域，子域进一步细分业务范围
- 名称应清晰表达该控制器负责的业务范围

```java
public class CounterOrderCommonController { ... }
public class CounterPreBookController { ... }
```

### 1.2 类注解规范
- 使用`@RestController`标明RESTful API控制器
- 使用`@RequestMapping`定义基础URL路径，应使用全小写
- URL路径应采用"/{业务域}/{子域}/"格式，使用名词表示资源

```java
@RestController
@RequestMapping("/counter/order/common/")
public class CounterOrderCommonController { ... }
```

### 1.3 依赖注入规范
- 使用`@Resource`进行Service层依赖注入
- 注入的服务命名应与控制器保持一致的领域概念
- 变量名应使用camelCase命名法

```java
@Resource
private CounterOrderCommonService counterOrderCommonService;
```

## 2. Controller方法规范

### 2.1 方法命名规范
- 方法名应使用camelCase命名法，以动词开头
- 采用`{操作动词}{业务实体}[{附加信息}]`格式
- 操作动词应准确表达接口的业务行为

常用操作动词：
- `query`: 查询信息
- `create`: 创建记录
- `update`: 更新记录
- `delete`: 删除记录
- `check`: 检查或验证
- `submit`: 提交数据
- `approve`: 审批操作
- `reject`: 拒绝操作
- `download`: 下载文件
- `upload`: 上传文件
- `preview`: 预览信息

```java
public Response<CounterOrderInfoVO> queryCounterInfo(...) { ... }
public Response<Boolean> submitCounterOrder(...) { ... }
```

### 2.2 方法注解规范
- 根据HTTP语义选择对应注解：`@PostMapping`、`@GetMapping`等
- URL路径应为方法名的全小写形式
- 查询操作优先使用POST而非GET，以支持复杂查询条件

```java
@PostMapping("querycounterinfo")
public Response<CounterOrderInfoVO> queryCounterInfo(...) { ... }
```

### 2.3 方法参数规范
- 使用`@RequestBody`注解接收JSON请求体
- 请求参数应封装为专用的Request对象
- 文件上传接口使用`MultipartFile`类型接收文件

```java
public Response<CounterOrderInfoVO> queryCounterInfo(@RequestBody QueryCounterOrderInfoRequest request) { ... }
public Response<UploadFileListVO> uploadFile(UploadFileRequest request) { ... }
```

### 2.4 方法返回值规范
- 统一使用`Response<T>`包装响应结果
- 泛型T应为专用的VO (View Object)对象
- 成功返回使用`Response.ok(data)`方法
- 错误返回使用`Response.error(code, message)`方法

```java
public Response<CounterOrderInfoVO> queryCounterInfo(...) {
    CounterOrderInfoVO vo = counterOrderCommonService.getCounterOrderInfo(request);
    return Response.ok(vo);
}
```

### 2.5 请求响应对象命名规范

#### 请求对象命名
- 格式：`{操作动词}{业务实体}[{附加信息}]Request`
- 示例：`QueryCounterOrderInfoRequest`、`CreateOrderRequest`

#### 响应对象命名
- 格式：`{业务实体}[{附加信息}]VO`
- 示例：`CounterOrderInfoVO`、`OrderDetailVO`

## 3. APIDOC文档规范

### 3.1 基本结构
APIDOC文档必须包含以下各部分：
- API路径和方法声明
- API分组和名称
- API功能描述
- 请求参数说明
- 响应结果说明
- 响应示例

```java
/**
 * @api {POST} /counter/order/common/querycounterinfo queryCounterInfo()
 * @apiVersion 1.0.0
 * @apiGroup CounterOrderCommonController
 * @apiName queryCounterInfo()
 * @apiDescription 柜台流水号查询柜台订单详细信息
 * 
 * {请求参数部分}
 * 
 * {响应结果部分}
 * 
 * {响应示例部分}
 */
```

### 3.2 请求参数文档规范
- 使用`@apiParam`标记请求参数
- 格式为`@apiParam (请求体) {参数类型} 参数名 参数描述`
- 应说明参数类型、名称和业务含义
- 对于枚举值，应说明可选值及含义

```java
/**
 * @apiParam (请求体) {String} appSerialNo 柜台订单号
 * @apiParam (请求体) {String} hkCustNo 香港客户号
 * @apiParam (请求体) {String} busiType 业务类型 1-认购 2-申购 3-赎回
 */
```

### 3.3 响应结果文档规范
- 使用`@apiSuccess`标记响应字段
- 格式为`@apiSuccess (响应结果) {字段类型} 字段路径 字段描述`
- 对于嵌套对象，使用点表示法标明路径
- 对于列表，应说明列表项的结构

```java
/**
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Object} data.hkCustInfoVO 香港客户信息
 * @apiSuccess (响应结果) {String} data.hkCustInfoVO.hkCustNo 香港客户号
 * @apiSuccess (响应结果) {Array} data.bankCardList 银行卡列表
 * @apiSuccess (响应结果) {String} data.bankCardList.bankCode 银行代码
 */
```

### 3.4 响应示例文档规范
- 使用`@apiSuccessExample`提供响应示例
- 示例应包含完整的JSON结构
- 示例数据应尽量真实，符合业务场景

```java
/**
 * @apiSuccessExample 响应结果示例
 * {"code":"I","data":{"hkCustNo":"123456","bankCardList":[{"bankCode":"001","bankName":"示例银行"}]},"description":"操作成功"}
 */
```

## 4. 实现规范

### 4.1 控制器职责
- 控制器只负责请求参数接收、调用业务服务、包装响应结果
- 不应在控制器中实现业务逻辑，而是委托给Service层
- 控制器方法应保持简洁，通常不超过5行代码

```java
@PostMapping("querycounterinfo")
public Response<CounterOrderInfoVO> queryCounterInfo(@RequestBody QueryCounterOrderInfoRequest request) {
    CounterOrderInfoVO vo = counterOrderCommonService.getCounterOrderInfo(request);
    return Response.ok(vo);
}
```

### 4.2 异常处理
- 使用全局异常处理器处理异常，而非在控制器中捕获
- 特殊业务异常可返回特定错误码
- 必要时可记录关键异常日志

### 4.3 数据安全处理
- 敏感数据应进行脱敏处理，如银行账号、证件号码等
- 对于敏感信息，应提供掩码版本(`xxxMask`)和摘要版本(`xxxDigest`)
- 完整敏感信息仅在必要时返回

## 5. 完整示例

### 5.1 控制器方法示例

```java
/**
 * @api {POST} /counter/order/common/queryorderdetail queryOrderDetail()
 * @apiVersion 1.0.0
 * @apiGroup CounterOrderCommonController
 * @apiName queryOrderDetail()
 * @apiDescription 查询订单详情
 * 
 * @apiParam (请求体) {String} orderId 订单ID
 * @apiParam (请求体) {String} hkCustNo 香港客户号
 * 
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {String} data.orderNo 订单号
 * @apiSuccess (响应结果) {String} data.orderStatus 订单状态
 * @apiSuccess (响应结果) {String} data.fundCode 基金代码
 * @apiSuccess (响应结果) {String} data.fundName 基金名称
 * 
 * @apiSuccessExample 响应结果示例
 * {"code":"I","data":{"orderNo":"123456","orderStatus":"1","fundCode":"000001","fundName":"示例基金"},"description":"操作成功"}
 */
@PostMapping("queryorderdetail")
public Response<OrderDetailVO> queryOrderDetail(@RequestBody QueryOrderDetailRequest request) {
    OrderDetailVO vo = counterOrderCommonService.getOrderDetail(request);
    return Response.ok(vo);
}
```

### 5.2 完整控制器类示例

```java
package com.howbuy.dtms.manager.counter.controller;

import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.howbuy.dtms.manager.common.Response;
import com.howbuy.dtms.manager.counter.service.CounterOrderService;
import com.howbuy.dtms.manager.counter.vo.OrderDetailVO;
import com.howbuy.dtms.manager.counter.vo.request.QueryOrderDetailRequest;

/**
 * 柜台订单控制器
 *
 * <AUTHOR>
 * @date 2025-03-07 18:26:59
 */
@RestController
@RequestMapping("/counter/order/")
public class CounterOrderController {

    @Resource
    private CounterOrderService counterOrderService;

    /**
     * @api {POST} /counter/order/queryorderdetail queryOrderDetail()
     * @apiVersion 1.0.0
     * @apiGroup CounterOrderController
     * @apiName queryOrderDetail()
     * @apiDescription 查询订单详情
     * 
     * @apiParam (请求体) {String} orderId 订单ID
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * 
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.orderNo 订单号
     * @apiSuccess (响应结果) {String} data.orderStatus 订单状态
     * @apiSuccess (响应结果) {String} data.fundCode 基金代码
     * @apiSuccess (响应结果) {String} data.fundName 基金名称
     * 
     * @apiSuccessExample 响应结果示例
     * {"code":"I","data":{"orderNo":"123456","orderStatus":"1","fundCode":"000001","fundName":"示例基金"},"description":"操作成功"}
     */
    @PostMapping("queryorderdetail")
    public Response<OrderDetailVO> queryOrderDetail(@RequestBody QueryOrderDetailRequest request) {
        OrderDetailVO vo = counterOrderService.getOrderDetail(request);
        return Response.ok(vo);
    }
}
```

## 6. 最佳实践

### 6.1 接口设计
- 接口应遵循单一职责原则，每个接口只完成一个业务功能
- 相关功能应归类到同一控制器中，保持业务内聚
- 复杂查询应提供条件过滤，避免全量查询
- 大量数据应分页返回，控制每页数据量

### 6.2 APIDOC文档
- 文档中的参数描述应准确反映业务含义
- 枚举值应说明所有可选值及含义
- 响应字段应说明数据格式和业务含义
- 复杂嵌套结构应清晰说明层级关系

### 6.3 代码质量
- 控制器应保持轻量级，复杂逻辑下沉到Service层
- 重复的代码应提取为公共方法
- 保持统一的错误处理和响应格式
- 重要操作应记录审计日志

## 7. 注意事项

- APIDOC文档中的参数和响应字段必须与实际DTO/VO类中的字段保持一致
- 所有接口都应返回统一的`Response<T>`包装类
- 控制器方法应保持简洁，复杂业务逻辑应放在Service层实现
- 敏感数据应进行适当脱敏处理
- 请求参数应进行合理的校验，确保数据有效性