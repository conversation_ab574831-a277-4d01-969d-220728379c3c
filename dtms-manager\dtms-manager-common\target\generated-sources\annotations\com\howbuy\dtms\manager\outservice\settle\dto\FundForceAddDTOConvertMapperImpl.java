package com.howbuy.dtms.manager.outservice.settle.dto;

import com.howbuy.dtms.settle.client.facade.trade.fundforceadd.FundForceAddRequest;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:23:43+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class FundForceAddDTOConvertMapperImpl implements FundForceAddDTOConvertMapper {

    @Override
    public FundForceAddRequest convert(FundForceAddDTO dto) {
        if ( dto == null ) {
            return null;
        }

        FundForceAddRequest fundForceAddRequest = new FundForceAddRequest();

        fundForceAddRequest.setAckVol( dto.getAckVol() );
        fundForceAddRequest.setForceAddRule( dto.getForceAddRule() );
        fundForceAddRequest.setFundCode( dto.getFundCode() );
        fundForceAddRequest.setFundTxAcctNo( dto.getFundTxAcctNo() );
        fundForceAddRequest.setHkCustNo( dto.getHkCustNo() );
        fundForceAddRequest.setNav( dto.getNav() );
        fundForceAddRequest.setNavDt( dto.getNavDt() );
        fundForceAddRequest.setSerialNumber( dto.getSerialNumber() );
        fundForceAddRequest.setShareRegDt( dto.getShareRegDt() );
        fundForceAddRequest.setTradeDt( dto.getTradeDt() );
        fundForceAddRequest.setVolDtlNo( dto.getVolDtlNo() );
        fundForceAddRequest.setCounterOrderNo( dto.getCounterOrderNo() );

        return fundForceAddRequest;
    }
}
