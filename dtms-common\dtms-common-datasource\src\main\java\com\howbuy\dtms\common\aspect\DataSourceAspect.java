package com.howbuy.dtms.common.aspect;


import com.howbuy.dtms.common.annotation.DataSource;
import com.howbuy.dtms.common.dynamicdatasource.RouteHolder;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Aspect
// 保证该切面在事务之前执行
@Order(Integer.MAX_VALUE - 5)
public class DataSourceAspect {

    //@annotation(org.guogai.dynamic_datasource.annotation.DataSource) 表示将有@DataSource注解的方法拦截下来
    //@within(org.guogai.dynamic_datasource.annotation.DataSource) 表示 如果类上面有@DataSource注解，那么将该类中的所有方法拦截下来
    @Pointcut("@annotation(com.howbuy.dtms.common.annotation.DataSource) || @within(com.howbuy.dtms.common.annotation.DataSource)")
    public void pc(){

    }
    @Around("pc()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        //获取方法上的有效注解
        DataSource dataSource = getDataSource(pjp);
        if(dataSource!=null){
            //拿到数据源名称
            String value = dataSource.value();
            RouteHolder.setRouteKey(value);
        }
        try {
            return pjp.proceed();
        } finally {
            RouteHolder.removeRouteKey();
        }
    }

    private DataSource getDataSource(ProceedingJoinPoint pjp) {
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        //查找方法上的注解
        DataSource annotation = AnnotationUtils.getAnnotation(signature.getMethod(), DataSource.class);
        if(annotation!=null){
            //说明方法上面有 DataSource注解
            return annotation;
        }
        //否则 查找类上是否有注解
        return AnnotationUtils.getAnnotation(signature.getDeclaringType(),DataSource.class);
    }

}
