<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.customize.HwCounterAuditOrderCustomizeMapper">

    <!--柜台审核订单列表查询-->
    <select id="queryCounterAuditOrderList" resultType="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderPO">
        select
        <include refid="com.howbuy.dtms.manager.dao.mapper.HwCounterAuditOrderMapper.Base_Column_List"/>
        from hw_counter_audit_order
        where rec_stat = '0'
        <if test="auditPageQuery.appStartDt != null and auditPageQuery.appStartDt != ''">
            and app_dt <![CDATA[>=]]> #{auditPageQuery.appStartDt}
        </if>
        <if test="auditPageQuery.appEndDt != null and auditPageQuery.appEndDt != ''">
            and app_dt <![CDATA[<=]]> #{auditPageQuery.appEndDt}
        </if>
        <if test="auditPageQuery.hkCustNo != null and auditPageQuery.hkCustNo != ''">
            and hk_cust_no = #{auditPageQuery.hkCustNo,jdbcType=VARCHAR}
        </if>
        <if test="auditPageQuery.creator != null and auditPageQuery.creator != ''">
            and creator = #{auditPageQuery.creator,jdbcType=VARCHAR}
        </if>
        <if test="auditPageQuery.auditStatusList != null and auditPageQuery.auditStatusList.size > 0">
            and audit_status in
            <foreach collection="auditPageQuery.auditStatusList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="auditPageQuery.custTypeList != null and auditPageQuery.custTypeList.size > 0">
            and invst_type in
            <foreach collection="auditPageQuery.custTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="auditPageQuery.counterBizTypeList != null and auditPageQuery.counterBizTypeList.size > 0">
            and counter_biz_type in
            <foreach collection="auditPageQuery.counterBizTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="auditPageQuery.revisitList != null and auditPageQuery.revisitList.size > 0">
            and revisit in
            <foreach collection="auditPageQuery.revisitList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <!-- 判断柜台收市，使用该接口的这个条件 -->
        <if test="auditPageQuery.fundCodeList != null and auditPageQuery.fundCodeList.size > 0">
            and fund_code in
            <foreach collection="auditPageQuery.fundCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by create_timestamp desc
    </select>
    <!--根据审核流水号和香港客户号查询审核订单信息-->
    <select id="queryCounterOrderByAppSerialNo"
            resultType="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderPO">
        select
        <include refid="com.howbuy.dtms.manager.dao.mapper.HwCounterAuditOrderMapper.Base_Column_List" />
        from hw_counter_audit_order
        where app_serial_no = #{appSerialNo,jdbcType=VARCHAR}
        and rec_stat = '0'
        <if test="hkCustNo != null and hkCustNo != '' " >
            and hk_cust_no = #{hkCustNo,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="queryAuditIngCounterOrderByPrebookDealNo"
            resultType="com.howbuy.dtms.manager.dao.po.HwCounterAuditOrderDtlPO">
        select
        <include refid="com.howbuy.dtms.manager.dao.mapper.HwCounterAuditOrderMapper.Base_Column_List" />
        from hw_counter_audit_order
        where prebook_deal_no = #{prebookDealNo,jdbcType=VARCHAR}
        and audit_status in ('2','8')
        and rec_stat = '0'
    </select>
</mapper>