<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.customize.HwCounterRevisitConfCustomizeMapper">


    <select id="queryRevisitConfByDictionaryCode"
            resultMap="com.howbuy.dtms.manager.dao.mapper.HwCounterRevisitConfMapper.BaseResultMap">
        select
        <include refid="com.howbuy.dtms.manager.dao.mapper.HwCounterRevisitConfMapper.Base_Column_List"/>
        from hw_counter_revisit_conf
        where dictionary_code = #{dictionaryCode,jdbcType=VARCHAR}
            and biz_type_code = #{bizTypeCode,jdbcType=VARCHAR}
            and trade_channel = #{tradeChannel,jdbcType=VARCHAR}
        <if test="selfAccountRevisit != null and selfAccountRevisit != ''">
            and self_account_revisit = #{selfAccountRevisit,jdbcType=VARCHAR}
        </if>
        <if test="fullFundTxAcctRevisit != null and fullFundTxAcctRevisit != ''">
            and full_fund_tx_acct_revisit = #{fullFundTxAcctRevisit,jdbcType=VARCHAR}
        </if>
        and rec_stat = '1'
    </select>
</mapper>