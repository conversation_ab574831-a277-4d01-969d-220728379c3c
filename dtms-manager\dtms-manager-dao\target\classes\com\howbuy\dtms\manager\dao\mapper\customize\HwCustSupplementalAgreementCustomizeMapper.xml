<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.manager.dao.mapper.customize.HwCustSupplementalAgreementCustomizeMapper">

    <select id="countByAgreementIdAndSignStatus" resultType="java.lang.Integer">
        select 
            count(1)
        from hw_cust_supplemental_agreement
        where agr_id = #{agrId,jdbcType=BIGINT}
        and sign_status = #{signStatus,jdbcType=VARCHAR}
        and rec_stat = '1'
    </select>
    <select id="batchQuerySignAgreementByAgreementIdAndHkCustNo"
            resultMap="com.howbuy.dtms.manager.dao.mapper.HwCustSupplementalAgreementMapper.BaseResultMap">
        select
            <include refid="com.howbuy.dtms.manager.dao.mapper.HwCustSupplementalAgreementMapper.Base_Column_List"/>
        from
            hw_cust_supplemental_agreement
        where
            agr_id = #{agrId,jdbcType=BIGINT}
            <if test="modifyCustNoList != null and modifyCustNoList.size() > 0">
                and hk_cust_no in
                <foreach collection="modifyCustNoList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            and sign_status = '1'
            and rec_stat = '1'
    </select>

    <select id="querySupplementalAgreementSignList" resultType="com.howbuy.dtms.manager.dao.bo.SupplementalAgreementConfigBO">
        SELECT
            a.agr_id as agrId,
            a.agr_dtl_id as agrDtlId,
        b.fund_code as fundCode,
        b.fund_abbr as fundAbbr,
        b.fund_man_code as fundManCode,
        b.agreement_name as agreementName,
        b.agreement_url as agreementUrl,
        b.agreement_sign_end_dt as agreementSignEndDt,
        b.agreement_description as agreementDescription,
        a.hk_cust_no as hkCustNo,
        a.cust_chinese_name as custChineseName,
        a.id_no_mask as idNoMask,
        a.id_no_cipher as idNoCipher,
        a.id_no_digest as idNoDigest,
        a.sign_status as signStatus,
        a.sign_method as signMethod,
        a.sign_date as signDate,
        a.trade_channel as tradeChannel
        FROM 
            hw_cust_supplemental_agreement a left join bp_supplemental_agreement_config b on a.agr_id = b.agr_id
        <where>
            a.rec_stat = '1'
            and b.rec_stat = '1'
            <if test="appStartTime != null and appStartTime != ''">
                AND DATE_FORMAT(a.create_time, '%Y%m%d') &gt;= #{appStartTime}
            </if>
            <if test="appEndTime != null and appEndTime != ''">
                AND DATE_FORMAT(a.create_time, '%Y%m%d') &lt;= #{appEndTime}
            </if>
            <if test="fundManCodeList != null and fundManCodeList.size > 0">
                AND b.fund_man_code IN
                <foreach collection="fundManCodeList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="agreementName != null and agreementName != ''">
                AND b.agreement_name LIKE CONCAT('%', #{agreementName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="signStatus != null and signStatus != ''">
                AND a.sign_status = #{signStatus,jdbcType=VARCHAR}
            </if>
            <if test="signMethod != null and signMethod != ''">
                AND a.sign_method = #{signMethod,jdbcType=VARCHAR}
            </if>
            <if test="hkCustNo != null and hkCustNo != ''">
                AND a.hk_cust_no = #{hkCustNo,jdbcType=VARCHAR}
            </if>
            <if test="fundCodeList != null and fundCodeList.size > 0">
                AND a.fund_code IN
                <foreach collection="fundCodeList" item="fundCode" open="(" separator="," close=")">
                    #{fundCode,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
        ORDER BY a.create_time DESC ,a.id DESC
    </select>
    <select id="querySignAgreementByAgreementIdAndHkCustNo"
            resultType="com.howbuy.dtms.manager.dao.po.HwCustSupplementalAgreementPO">
        select
            <include refid="com.howbuy.dtms.manager.dao.mapper.HwCustSupplementalAgreementMapper.Base_Column_List"/>
        from
            hw_cust_supplemental_agreement
        where
            agr_id = #{agrId,jdbcType=BIGINT}
            and hk_cust_no = #{hkCustNo,jdbcType=VARCHAR}
            and rec_stat = '1'
    </select>

    <update id="updateByAgrId" parameterType="com.howbuy.dtms.manager.dao.po.HwCustSupplementalAgreementPO">
        UPDATE hw_cust_supplemental_agreement
        SET sign_status = #{signStatus,jdbcType=VARCHAR},
            sign_method = #{signMethod,jdbcType=VARCHAR},
            sign_date = #{signDate,jdbcType=TIMESTAMP},
            modifier = #{modifier,jdbcType=VARCHAR},
            trade_channel = #{tradeChannel,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        WHERE agr_id = #{agrId,jdbcType=BIGINT}
          AND hk_cust_no = #{hkCustNo,jdbcType=VARCHAR}
          AND rec_stat = '1'
    </update>

</mapper> 