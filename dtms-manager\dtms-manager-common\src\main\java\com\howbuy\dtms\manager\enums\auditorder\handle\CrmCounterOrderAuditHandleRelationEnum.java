package com.howbuy.dtms.manager.enums.auditorder.handle;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.howbuy.dtms.common.enums.CounterOrderAuditStatusEnum;
import com.howbuy.dtms.manager.enums.auditorder.CounterBizTypeEnum;

public enum CrmCounterOrderAuditHandleRelationEnum {

     /************************************直接不通过 审核不通过**************************************/
    CRM_PURCHASE_TO_NOT_AUDIT_PASS(CounterOrderHandleEnum.CRM_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, null, CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.PURCHASE.getCode()),

    CRM_SUBS_TO_NOT_AUDIT_PASS(CounterOrderHandleEnum.CRM_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, null, CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.SUBS.getCode()),

    CRM_REDEEM_TO_NOT_AUDIT_PASS(CounterOrderHandleEnum.CRM_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, null, CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.REDEEM.getCode()),

    CRM_REVOKE_TO_NOT_AUDIT_PASS(CounterOrderHandleEnum.CRM_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, null, CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.REVOKE.getCode()),

    CRM_112A_TO_NOT_AUDIT_PASS(CounterOrderHandleEnum.CRM_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, null, CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum._112A.getCode()),
    CRM_112B_TO_NOT_AUDIT_PASS(CounterOrderHandleEnum.CRM_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, null, CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum._112B.getCode()),

    CRM_SUB_AND_FIRST_PAID_TO_NOT_AUDIT_PASS(CounterOrderHandleEnum.CRM_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, null, CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),

    /************************************等待复核>>>>>>>>审核通过**************************************/
    CRM_SUBS_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.SUBS.getCode()),

    CRM_PURCHASE_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.PURCHASE.getCode()),

    CRM_112A_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum._112A.getCode()),
    CRM_112B_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum._112B.getCode()),

    CRM_SUB_AND_FIRST_PAID_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),


    CRM_REDEEM_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.REDEEM_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.REDEEM.getCode()),

    REVOKE_COUNTER_ORDER_AUDIT_PASS(CounterOrderHandleEnum.REVOKE_COUNTER_ORDER_AUDIT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.REVOKE.getCode()),


    /************************************等待复核>>>>>>审核不通过**************************************/
    CRM_PURCHASE_COUNTER_ORDER_AUDIT_NOT_PASS_TO_FAILURE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.PURCHASE.getCode()),

    CRM_SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_TO_FAILURE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.SUBS.getCode()),

    CRM_112A_COUNTER_ORDER_AUDIT_NOT_PASS_TO_FAILURE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum._112A.getCode()),

    CRM_112B_COUNTER_ORDER_AUDIT_NOT_PASS_TO_FAILURE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum._112B.getCode()),

    CRM_SUB_AND_FIRST_PAID_COUNTER_ORDER_AUDIT_NOT_PASS_TO_FAILURE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),

    CRM_REDEEM_COUNTER_ORDER_AUDIT_NOT_PASS_TO_FAILURE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.REDEEM.getCode()),

    REVOKE_COUNTER_ORDER_AUDIT_NOT_PASS_TO__REJECT_TO_FIRST_EXAMINE(CounterOrderHandleEnum.SUBS_COUNTER_ORDER_AUDIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVIEW.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.REVOKE.getCode()),



    /************************************等待回访>>>>>>>>审核通过处理器**************************************/
    CRM_SUBS_AUDIT_PASS_WAIT_REVISIT_TO_APPROVE(CounterOrderHandleEnum.WAIT_REVISIT_TO_APPROVE_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.SUBS.getCode()),

    CRM_PURCHASE_AUDIT_PASS_WAIT_REVISIT_TO_APPROVE(CounterOrderHandleEnum.WAIT_REVISIT_TO_APPROVE_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.PURCHASE.getCode()),
    CRM_112A_AUDIT_PASS_WAIT_REVISIT_TO_APPROVE(CounterOrderHandleEnum.WAIT_REVISIT_TO_APPROVE_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum._112A.getCode()),

    CRM_112B_AUDIT_PASS_WAIT_REVISIT_TO_APPROVE(CounterOrderHandleEnum.WAIT_REVISIT_TO_APPROVE_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum._112B.getCode()),

    CRM_SUB_AND_FIRST_PAID_AUDIT_PASS_WAIT_REVISIT_TO_APPROVE(CounterOrderHandleEnum.WAIT_REVISIT_TO_APPROVE_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),

    CRM_REDEEM_AUDIT_PASS_WAIT_REVISIT_TO_APPROVE(CounterOrderHandleEnum.WAIT_REVISIT_TO_APPROVE_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.REDEEM.getCode()),

    CRM_REVOKE_AUDIT_PASS_WAIT_REVISIT_TO_APPROVE(CounterOrderHandleEnum.WAIT_REVISIT_TO_APPROVE_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.APPROVE.getCode(), CounterBizTypeEnum.REVOKE.getCode()),


    /************************************等待回访>>>>>>>>驳回至经办处理器**************************************/
    CRM_SUBS_AUDIT_NOT_PASS_WAIT_REVISIT_TO_FAILURE(CounterOrderHandleEnum.WAIT_REVISIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.SUBS.getCode()),

    CRM_PURCHASE_AUDIT_NOT_PASS_WAIT_REVISIT_FAILURE(CounterOrderHandleEnum.WAIT_REVISIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.PURCHASE.getCode()),
    CRM_112A_AUDIT_NOT_PASS_WAIT_REVISIT_TO_FAILURE(CounterOrderHandleEnum.WAIT_REVISIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum._112A.getCode()),

    CRM_112B_AUDIT_NOT_PASS_WAIT_REVISIT_TO_FAILURE(CounterOrderHandleEnum.WAIT_REVISIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum._112B.getCode()),

    CRM_SUB_AND_FIRST_PAID_AUDIT_NOT_PASS_WAIT_REVISIT_TO_FAILURE(CounterOrderHandleEnum.WAIT_REVISIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.SUB_AND_FIRST_PAID.getCode()),

    CRM_REDEEM_AUDIT_NOT_PASS_WAIT_REVISIT_TO_FAILURE(CounterOrderHandleEnum.WAIT_REVISIT_NOT_PASS_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.REDEEM.getCode()),

    CRM_REVOKE_AUDIT_NOT_PASS_WAIT_REVISIT_TO_FAILURE(CounterOrderHandleEnum.WAIT_REVISIT_TO_APPROVE_HANDLE, CounterOrderAuditStatusEnum.WAIT_REVISIT.getCode(), CounterOrderAuditStatusEnum.FAILURE.getCode(), CounterBizTypeEnum.REVOKE.getCode()),


    ;

    private final CounterOrderHandleEnum handleEnum;

    private final String oldAuditStatus;

    private final String auditStatus;

    private final String bizType;

    CrmCounterOrderAuditHandleRelationEnum(CounterOrderHandleEnum handleEnum, String oldAuditStatus, String auditStatus, String bizType) {
        this.handleEnum = handleEnum;
        this.oldAuditStatus = oldAuditStatus;
        this.auditStatus = auditStatus;
        this.bizType = bizType;
    }

     public static String getByOldAuditStatusAndAuditStatusAndBizType(String oldAuditStatus, String auditStatus, String bizType) {
        for (CrmCounterOrderAuditHandleRelationEnum auditEnum : CrmCounterOrderAuditHandleRelationEnum.values()) {
            if(null == oldAuditStatus){
                if(null == auditEnum.getOldAuditStatus() && auditEnum.getAuditStatus().equals(auditStatus) && auditEnum.getBizType().equals(bizType)){
                    return auditEnum.getHandleEnum().getCode();
                }
            }
            if (StringUtils.isNotBlank(auditEnum.getOldAuditStatus()) && auditEnum.getOldAuditStatus().equals(oldAuditStatus)
                    && auditEnum.getAuditStatus().equals(auditStatus)
                    && auditEnum.getBizType().equals(bizType)) {
                return auditEnum.getHandleEnum().getCode();
            }
        }
        return null;
    }

    public CounterOrderHandleEnum getHandleEnum() {
        return handleEnum;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public String getBizType() {
        return bizType;
    }

    public String getOldAuditStatus() {
        return oldAuditStatus;
    }
}
