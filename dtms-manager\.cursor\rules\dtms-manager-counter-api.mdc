---
description: 根据设计文档接口定义生成dtms-manager-counter项目的接口代码
globs: *.java, *.md
alwaysApply: false
---
# DTMS-Manager-Counter项目API生成规则
# 作者: hongdong.xie
# 日期: 2025-03-07 18:49:03

#==================== 编码与命名规范 ====================

# 接口定义规范
interface_rules:
  - 路径：小写字母，多词用/分隔
  - 方法：使用@PostMapping，所有接口默认POST方法
  - 参数：使用@RequestBody注解
  - 返回：统一使用com.howbuy.dtms.manager.common.Response<VO>格式，通过Response.ok(vo)返回
  - 校验：不使用特定的校验注解，在Service层进行校验
  - 异常处理：使用BusinessException抛出业务异常，通过ExceptionCodeEnum定义异常码

# 接口修改/新增规则
interface_modify_rules:
  - 判断依据：
    - 设计文档中接口名后标注"——修改"的，表示在原有接口修改
    - 设计文档中接口名后标注"——新增"的，表示新增接口
    - 设计文档中接口名后无标注的，表示新增接口
  - 修改原则：
    - 修改接口时，保持原有的路径和方法名不变
    - 修改接口时，需要兼容原有的功能
    - 修改接口时，新增字段采用追加方式，不要删除或修改原有字段
    - 修改接口时，保持原有的注释格式，仅更新内容
  - 新增原则：
    - 新增接口时，需要在对应的Controller中新增方法
    - 新增接口时，需要创建新的Request和VO类
    - 新增接口时，需要按规范添加完整的注释

# VO和Request对象复用规则
vo_request_rules:
  - 基本原则：
    - 即使不同接口的入参或出参结构完全一样，也不要复用VO和Request类
    - 每个接口都应该有自己独立的Request和VO类
    - 避免多个接口共用同一个Request或VO类
  - 命名规范：
    - Request类命名：Query+业务名+Request (例：QueryCounterOrderInfoRequest)
    - VO类命名：业务名+VO (例：CounterOrderInfoVO)
    - 内部VO类命名：业务名+VO (例：HkCustInfoVO)
  - 目的说明：
    - 避免后期维护时因共用类导致的连带影响
    - 保证每个接口的参数独立演进
    - 提高代码的可维护性和可扩展性

# 类和方法命名规范
naming_rules:
  - Controller: 业务名Controller (例：CounterOrderCommonController)
  - Request对象: Query+业务名+Request (例：QueryCounterOrderInfoRequest)
  - 响应VO: 业务名+VO (例：CounterOrderInfoVO)
  - 内部VO: 业务名+VO (例：HkCustInfoVO)
  - 所有类和方法必须提供完整注释

# 对象规范
object_rules:
  - Request类：
    - 使用@Setter/@Getter (不用@Data)
    - 不使用特定的校验注解，在Service层进行校验
    - 所有字段添加中文注释
  - VO类：
    - 使用@Setter/@Getter (不用@Data)
    - 直接用于Response的VO不需要继承特定基类
    - 所有字段添加中文注释
  - 复杂对象：
    - 列表使用List<专用VO>类型
    - 字段命名应体现列表性质(例：prebookList)
    - 嵌套对象创建专用VO类

#==================== 注释规范 ====================

# 类注释规范
class_comment:
  ```
  /**
   * @author: 作者名称
   * @Description: 类的功能描述
   * @Date: yyyy/MM/dd HH:mm
   */
  ```

# 方法注释规范
method_comment:
  ```
  /**
   * 方法功能描述
   * @param 参数名 参数说明
   * @return 返回值说明
   */
  ```

# APIDOC规范
apidoc_rules:
  - 基本格式：
    ```
    /**
     * @api {POST} /路径 方法名()
     * @apiVersion 1.0.0
     * @apiGroup 控制器名
     * @apiName 方法名()
     * @apiDescription 接口功能描述
     * @apiParam (请求体) {类型} 参数名 参数说明
     * @apiParamExample 请求体示例
     * {"参数":"值"}
     * @apiSuccess (响应结果) {类型} code 状态码      状态码0000表示成功
     * @apiSuccess (响应结果) {类型} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {类型} data.字段名 说明
     * @apiSuccessExample 响应结果示例
     * {"code":"I","data":{},"description":"成功"}
     */
    ```
  - 注意事项：
    - 所有参数都使用{String}类型，除非特殊说明
    - 数组类型使用{Array}，对象类型使用{Object}，数字类型使用{Number}
    - 嵌套属性使用点号(例：data.items.option)
    - 参数说明包含可选值说明(例：0-否 1-是)
    - 所有注释使用中文，示例数据符合实际业务场景

#==================== 示例 ====================

# 接口示例
example_interface:
  ```java
  /**
   * @api {POST} /counter/order/common/querycounterinfo queryCounterInfo()
   * @apiVersion 1.0.0
   * @apiGroup CounterOrderCommonController
   * @apiName queryCounterInfo()
   * @apiDescription 柜台流水号查询柜台订单详细信息
   * @apiParam (请求体) {String} appSerialNo 柜台订单号
   * @apiParam (请求体) {String} hkCustNo 香港客户号
   * @apiParamExample 请求体示例
   * {"hkCustNo":"HK123456","appSerialNo":"APP123456"}
   * @apiSuccess (响应结果) {String} code 状态码      状态码0000表示成功
   * @apiSuccess (响应结果) {String} description 描述信息
   * @apiSuccess (响应结果) {Object} data 数据封装
   * @apiSuccess (响应结果) {Object} data.hkCustInfoVO 香港客户信息
   * @apiSuccess (响应结果) {String} data.hkCustInfoVO.hkCustNo 香港客户号
   * @apiSuccessExample 响应结果示例
   * {"code":"I","data":{"hkCustInfoVO":{"hkCustNo":"HK123456"}},"description":"成功"}
   */
  @PostMapping("querycounterinfo")
  public Response<CounterOrderInfoVO> queryCounterInfo(@RequestBody QueryCounterOrderInfoRequest request) {
      CounterOrderInfoVO vo = counterOrderCommonService.getCounterOrderInfo(request);
      return Response.ok(vo);
  }
  ```

# 请求对象示例
request_example:
  ```java
  /**
   * @author: hongdong.xie
   * @Description: 查询柜台订单详细信息请求参数
   * @Date: 2025/03/07 18:49
   */
  @Setter
  @Getter
  public class QueryCounterOrderInfoRequest {
      /**
       * 柜台订单号
       */
      private String appSerialNo;
      
      /**
       * 香港客户号
       */
      private String hkCustNo;
  }
  ```

# VO对象示例
vo_example:
  ```java
  /**
   * @author: hongdong.xie
   * @Description: 柜台订单详细信息响应对象
   * @Date: 2025/03/07 18:49
   */
  @Setter
  @Getter
  public class CounterOrderInfoVO {
      /**
       * 香港客户信息
       */
      private HkCustInfoVO hkCustInfoVO;
      
      /**
       * 客户预约信息列表
       */
      private List<PrebookInfoVO> prebookList;
      
      /**
       * 订单明细信息
       */
      private OrderDetailVO order;
      
      /**
       * 客户份额明细列表
       */
      private List<CustBalanceVO> custBalanceList;
      
      /**
       * 香港客户银行卡列表
       */
      private List<BankCardVO> bankCardList;
  }
  
  /**
   * @author: hongdong.xie
   * @Description: 香港客户信息
   * @Date: 2025/03/07 18:49
   */
  @Setter
  @Getter
  public class HkCustInfoVO {
      /**
       * 香港客户号
       */
      private String hkCustNo;
      
      /**
       * 一账通号
       */
      private String hboneNo;
      
      // 其他字段...
  }
  ```

# 复杂VO对象示例
complex_vo_example:
  ```java
  /**
   * @author: hongdong.xie
   * @Description: 柜台订单详细信息响应对象
   * @Date: 2025/03/07 18:49
   */
  @Setter
  @Getter
  public class CounterOrderInfoVO {
      /**
       * 香港客户信息
       */
      private HkCustInfoVO hkCustInfoVO;
      
      /**
       * 客户预约信息列表
       */
      private List<PrebookInfoVO> prebookList;
      
      /**
       * 订单明细信息
       */
      private OrderDetailVO order;
      
      /**
       * 客户份额明细列表
       */
      private List<CustBalanceVO> custBalanceList;
      
      /**
       * 香港客户银行卡列表
       */
      private List<BankCardVO> bankCardList;
  }
  
  /**
   * @author: hongdong.xie
   * @Description: 预约信息VO
   * @Date: 2025/03/07 18:49
   */
  @Setter
  @Getter
  public class PrebookInfoVO {
      /**
       * 预约id
       */
      private String preId;
      
      /**
       * 预约基金代码
       */
      private String fundCode;
      
      /**
       * 预约基金名称
       */
      private String fundName;
      
      /**
       * 预约折扣
       */
      private Number prebookDiscount;
      
      // 其他字段...
  }
  ``` 