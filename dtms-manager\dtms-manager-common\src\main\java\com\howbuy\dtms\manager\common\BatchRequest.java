package com.howbuy.dtms.manager.common;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 批量请求基础类
 * @date 2024/7/29 11:13
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BatchRequest extends BaseRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 请求id列表
     */
    private List<Long> idList;

    /**
     * 基金代码列表
     */
    private List<String> fundCodeList;

    /**
     *  确认标记 1-确认 0-取消
     */
    private String confirmFlag;
}
