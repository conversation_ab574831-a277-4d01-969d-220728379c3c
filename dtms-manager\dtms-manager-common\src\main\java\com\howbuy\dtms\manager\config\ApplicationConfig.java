package com.howbuy.dtms.manager.config;

import com.howbuy.dtms.manager.interceptor.LogInterceptor;
import com.howbuy.dtms.manager.utils.UuidTaskDecorator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration;
import org.springframework.boot.task.TaskExecutorBuilder;
import org.springframework.boot.task.TaskExecutorCustomizer;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskDecorator;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.concurrent.DelegatingSecurityContextExecutor;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.time.Duration;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * guangyao.wu 2024-11-12 17:00
 */
@Configuration
public class ApplicationConfig implements WebMvcConfigurer {

    @Autowired
    private LogInterceptor logInterceptor;

    @Bean
    public TaskDecorator uuidTaskDecorator() {
        return new UuidTaskDecorator();
    }

    /**
     * @see TaskExecutionAutoConfiguration
     */
    @Bean
    public TaskExecutorCustomizer taskExecutorCustomizer() {
        return taskExecutor -> {
            taskExecutor.setThreadNamePrefix("DtmsManagerThreadPool_");
            taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        };
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(logInterceptor);
    }

    @Bean
    public Executor executor(TaskExecutorBuilder builder) {
        ThreadPoolTaskExecutor taskExecutor = builder.build();
        taskExecutor.initialize();
        // 防止多线程下，SecurityContext失效问题
        return new DelegatingSecurityContextExecutor(taskExecutor);
    }

    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder restTemplateBuilder) {
//        Supplier<ClientHttpRequestFactory> requestFactory = () ->
//                new BufferingClientHttpRequestFactory(new HttpComponentsClientHttpRequestFactory());
        return restTemplateBuilder
                .requestFactory(HttpComponentsClientHttpRequestFactory::new)
                .setReadTimeout(Duration.ofSeconds(300))
                .setConnectTimeout(Duration.ofSeconds(300))
                .build();
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/static/**").addResourceLocations("/static/");
    }
}
